# Kiểm tra tên branch hợp lệ
BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)
# Quy tắc: chỉ chữ thường, số, -, /, bắt đầu bằng loại công việ<PERSON>, mô tả ngắn
echo "$BRANCH_NAME" | grep -Eq '^(feature|fix|hotfix|chore|refactor|release|synccode)\/[a-z0-9\-]+$'
if [ $? -ne 0 ]; then
  echo "\033[0;31m[TÊN BRANCH KHÔNG HỢP LỆ]\033[0m"
  echo "Tên branch phải theo dạng: <loai>/<mo-ta-ngan-gon>"
  echo "Ví dụ: feature/login-page, fix/typo-header, synccode/content-sync-code"
  exit 1
fi

# Format code with prettier
npm run lint:prettier

# Lint code with eslint
npm run lint:js

# Type check (if TypeScript is used)
# npm run type-check
