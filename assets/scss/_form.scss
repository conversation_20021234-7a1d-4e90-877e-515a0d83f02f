select:focus {
    outline-offset: 0px;
    outline: 0px solid transparent;
    box-shadow: unset;
}

[type='checkbox']:checked {
    background-image: url(/assets/images/img/icons/check-black.svg);
}

.border-yellow {
    border-color: #ffd255;
}

*:focus-visible {
    outline: none;
}

.amount-jackpot {
    text-shadow:
        0px 1px 0px #eba132,
        0px 4px 4px rgba(0, 0, 0, 0.25);
}

input:focus ~ label {
    color: #22a374;
}

input:is(:-webkit-autofill, :autofill) {
    -webkit-box-shadow: 0 0 0 1000px #081321 inset !important;
    box-shadow: 0 0 0px 1000px #081321 inset !important;
    -webkit-text-fill-color: #b7bec6 !important;
    color: #b7bec6 !important;
}

input:focus {
    -webkit-text-fill-color: #fff !important;
    color: #fff !important;
}

.loader-image {
    background: url('/assets/images/img/loader/spinner.svg') center center no-repeat;
    border-radius: 14px;
}

@layer utilities {
    .header-hover {
        color: #00d486;
    }
}

select:not([size]) {
    background-image: url('/assets/images/img/icons/arrow-down.svg');
    background-size: 24px;
}

.input-text {
    box-shadow: none;

    &:focus {
        box-shadow: none;
    }
}
