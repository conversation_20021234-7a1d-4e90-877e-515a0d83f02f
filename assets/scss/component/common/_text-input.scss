.common-input {
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type='number'] {
        -moz-appearance: textfield;
    }
    @media (max-width: 1023px) {
        .group-input {
            background: #f6f7fa !important;
            > div > div {
                display: grid !important;
                grid-template-columns: 1fr 2.2fr 1fr;
                align-items: center;
                label {
                    position: relative;
                    order: 1;
                    transform: unset;
                    padding-left: 5px;
                    top: 0;
                    &:before {
                        content: unset;
                    }
                }
                input {
                    order: 2;
                    background: none;
                    border: none;
                    text-align: right;
                    padding: 0;
                }
            }
        }
    }
}
