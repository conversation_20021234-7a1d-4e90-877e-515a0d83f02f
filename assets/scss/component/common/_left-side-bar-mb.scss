.left-side-bar-mb {
    max-height: calc(100dvh - var(--header-height));
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: var(--menu-mobile-bottom);
    &::-webkit-scrollbar {
        display: none;
    }
    .active {
        img {
            filter: brightness(0) saturate(100%) invert(79%) sepia(39%) saturate(632%)
                hue-rotate(355deg) brightness(101%) contrast(101%);
        }
        a {
            background: linear-gradient(180deg, #fffbf0 0%, #fff1cb 100%);
            border: 1px solid #ffd2554f;
        }
    }
}
@media (max-width: 1199px) {
    .show-breadcrumb-mb {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        padding: 16px 12px;
    }
    .main-mobile {
        position: relative;
        &:has(.show-breadcrumb-mb) {
            padding-top: var(--breadcrumb-height);

            .left-side-bar-mb {
                top: 92px;
                padding-bottom: 112px;
                &.active {
                    position: fixed;
                    top: 76px;
                }
            }
        }
    }
}
