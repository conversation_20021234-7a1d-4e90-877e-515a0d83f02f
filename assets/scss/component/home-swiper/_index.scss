.home-swiper {
    --swiper-navigation-size: 40px;
    .swiper-wrapper {
        @apply 2xl:h-[300px];
    }
    .swiper-scrollbar {
        @apply bottom-1 !h-[3px] !w-[45px] !-translate-x-1/2 !bg-[#ffffff4d] xl:!bottom-[14px] xl:!h-[6px] xl:!w-[90px];
        left: 50% !important;
        .swiper-scrollbar-drag {
            @apply bg-[#fff] !opacity-70;
        }
    }
    .swiper-button-prev {
        @apply max-xl:!hidden;
    }
    .swiper-button-next {
        @apply max-xl:!hidden;
    }
}
.home-swiper.update-ux-ui {
    .swiper-wrapper {
        @apply 2xl:!h-full;
    }
}
$homeSwiperViewPort: 390;

@function homeSwiperPxToVW($size) {
    $vw: calc($homeSwiperViewPort * 0.01);
    @return calc($size / $vw) * 1vw;
}
.home-swiper {
    --swiper-navigation-size: 40px;
    @media (max-width: 1199px) {
        --swiper-scrollbar-bottom: 8px;
    }
}
.swiper-wrapper {
    height: homeSwiperPxToVW(94);
    @media (max-width: 1199px) {
        height: homeSwiperPxToVW(94);
    }
}
