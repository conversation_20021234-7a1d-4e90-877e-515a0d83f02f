.category__item {
    i {
        background: -webkit-linear-gradient(299deg, #b1b9cb 6.81%, #717589 163.63%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    &:hover,
    &.active {
        background: url('/assets/images/game/double-arrow-left.svg') 95% center / 29px 24px
            no-repeat;
        color: white;
        i {
            background: transparent;
            -webkit-text-fill-color: unset;
        }
    }
}
.games {
    video[id^='h5live-go_'] {
        transform: translate(-50%, -59%) scale(1.4) rotate(0deg) !important;
    }  
    video[id^='h5live-rik'] {
        transform: translate(-50%, -63%) scale(1.3) rotate(0deg) !important;
    }
    video[id^='h5live-rik_vgmn_111'] {
        transform: translate(-50%, -52%) scale(1.45) rotate(0deg) !important;
    }
    video[id^='h5live-rik_vgmn_109'] {
        transform: translate(-50%, -61%) scale(1.5) rotate(0deg) !important;
    }
    video[id^='h5live-techplay'] {
        transform: translate(-50%, -31%) scale(1.7) rotate(0deg) !important;
    }
    video[id^='h5live-techplay_bc_77784'], video[id^='h5live-techplay_sb_77783'] {
        transform: translate(-50%, -25%) scale(1.8) rotate(0deg) !important;
    }
    video[id^='h5live-sunwin'] {
        transform: translate(-50%, -56%) scale(1.4) rotate(0deg) !important;
    }
    video[id^='h5live-sunwin_G1S_306'] {
        transform: translate(-50%, -51%) scale(1.3) rotate(0deg) !important;
    }
    video[id^='h5live-b52'] {
        transform: translate(-50%, -50%) scale(1.25) rotate(0deg) !important;
    }
    video[id^='h5live-789club'] {
        transform: translate(-50%, -50%) scale(1.45) rotate(0deg) !important;
    }
}