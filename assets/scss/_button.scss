.btn {
    --height: 36px;
    --padding: 8px 22px;
    --radius: 8px;
    --font-size: 14px;
    --line-height: 20px;
    --font-weight: 500;
    --border: 1px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
    height: var(--height);
    padding: var(--padding);
    font-size: var(--font-size);
    line-height: var(--line-height);
    font-weight: var(--font-weight);
    background: var(--bg) !important;
    color: var(--color);
    border-radius: var(--radius) !important;
    border: var(--border);
    @media (min-width: 1024px) {
        --height: 48px;
        --padding: 12px 24px;
        --radius: 12px;
        --font-size: 16px;
        --line-height: 24px;
    }

    &-primary {
        --bg: rgb(228, 24, 99);
        --color: #fdfeff;
        @media (min-width: 1024px) {
            &:hover {
                --bg: rgba(228, 24, 99, 0.8);
            }
        }
    }
    &-secondary {
        --bg: #22a374;
        --color: #fff;
        @media (min-width: 1024px) {
            &:hover {
                --bg: #39b689;
            }
        }
    }
    &-outline {
        --bg: #162943;
        --color: #fdfeff;
        --border: 1px solid #283b55;
        @media (min-width: 1024px) {
            &:hover {
                --color: rgba(0, 212, 134, 0.8);
                --border: 1px solid rgba(0, 212, 134, 0.8);
            }
        }
    }
    &-brown {
        --bg: #f1f3f9;
        --color: #0b0b0c;
        @media (min-width: 1024px) {
            &:hover {
                --bg: #f7f7f9;
            }
        }
    }
    &-sort {
        --bg: #162943;
        --color: #b7bec6;
        @media (min-width: 1024px) {
            &:hover {
                --bg: #e41863;
                --color: #fff;
            }
        }
    }
    &-size-md {
        --height: 30px;
        --padding: 4px 7px;
        --radius: 8px;
        --font-size: 12px;
        --line-height: 16px;
        @media (min-width: 1024px) {
            --height: 36px;
            --padding: 8px 22px;
            --radius: 8px;
            --font-size: 14px;
            --line-height: 20px;
        }
    }
    &-size-sm {
        --height: 24px;
        --padding: 2px 6px;
        --radius: 8px;
        --font-size: 12px;
        --line-height: 18px;
    }
    &-submit {
        --height: 44px;
        --padding: 12px 24px;
        --radius: 8px;
        --font-size: 16px;
        --line-height: 20px;
    }
    &:disabled {
        --bg: rgba(228, 24, 99, 0.5);
        --color: rgba(255, 255, 255, 0.5);
        pointer-events: none;
    }
}
