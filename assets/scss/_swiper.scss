// .swiper-pagination .swiper-pagination-bullet {
//   background-color: transparent;
// }
// #hotMatchSwiper .swiper-pagination-bullets {
//   display: none;
// }
// .maintain img {
//   filter: grayscale(1) brightness(1.2);
// }
// @media (min-width: 1280px) {
//   .pagination-horizontal {
//     position: static;
//     margin-top: -10px;
//   }
// }
// @media (max-width: 1279px) {
//   #homeSwiper.swiper-horizontal > .swiper-pagination-bullets {
//     bottom: 0;
//   }
// }

.swiper-pagination-horizontal {
    @apply flex justify-center max-lg:pt-4 lg:pt-3;
    &.small {
        @apply pt-4;
        .swiper-pagination-bullet {
            @apply h-3 w-3;
        }
    }
    .swiper-pagination-bullet {
        @apply bg-slate-600 opacity-100 max-lg:h-2 max-lg:w-2 lg:h-3 lg:w-3;
    }
    .swiper-pagination-bullet-active {
        @apply bg-green-400;
    }
}
