.home-topwinner-item {
    .top-item {
        --bg: url('/assets/images/v2/home/<USER>');
        background: var(--bg) no-repeat center / 100% 100%;
        @apply order-3 h-[140px] w-[84px] overflow-hidden rounded-lg;
        &--0 {
            @apply relative -top-[14px] order-2;
        }
        &--1 {
            --bg: url('/assets/images/v2/home/<USER>');
            @apply order-1;
        }
        &--2 {
            --bg: url('/assets/images/v2/home/<USER>');
        }
        &__avatar {
            @apply mx-auto mb-[18px] size-[39px] rounded-lg border border-solid border-[#FFFFFF];
        }
        &__title {
            @apply mb-1 text-[10px] font-medium leading-[15px] text-[#FFFFFF];
        }
        &__bottom {
            @apply bg-[#DFE3EC];
        }
        &__amount {
            @apply flex items-center justify-center gap-1 text-[10px] font-bold leading-4;
            img {
                @apply size-[12px] object-contain;
            }
        }
        @media (max-width: 1443px) {
            &__amount {
                @apply gap-[2px] px-[2px];
            }
        }
    }
}
