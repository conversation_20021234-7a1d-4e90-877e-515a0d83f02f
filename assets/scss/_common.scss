.stretched-link::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    content: '';
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}

.no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

input {
    &::placeholder {
        color: #757575;
        font-weight: 400;
    }
}
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.site-nav li:hover button:after {
    content: '';
    position: absolute;
    height: 20px;
    width: 300%;
    top: 40px;
    left: -100%;
}
.site-nav li:hover {
    .arrow-down {
        transform: rotate(90deg);
    }
}
.site-nav li:hover .content-submenu {
    height: auto;
    opacity: 1;
}
.shadow-small {
    box-shadow:
        0 1px 4px rgba(0, 0, 0, 0.12),
        0 1px 3px rgba(0, 0, 0, 0.24);
}
.shadow-medium {
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.16),
        0 3px 6px rgba(0, 0, 0, 0.23);
}

// vue transition
.v-enter-active,
.v-leave-active {
    transition: opacity 0.5s ease;
}

.v-enter-from,
.v-leave-to {
    @apply opacity-0;
    transform: translateX(-100%);
}
.provider-icon {
    line-height: 0;
    backdrop-filter: blur(4px);
    border-radius: 50%;
    padding: 2px;
    background: rgba(0, 0, 0, 0.4);
    i {
        font-size: 20px;
        background: linear-gradient(180deg, #d8deea 0%, #a1a9bb 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

body {
    --header-height: 74px;
    --menu-mobile-bottom: 70px;
    --breadcrumb-height: 48px;
    font-family: 'Inter', sans-serif;
    @apply bg-slate-950;
    &:has(.modal-open) {
        @apply overflow-hidden;
    }
    @media (max-width: 1199px) {
        --header-height: 62px;
    }
}

.section__readmore {
    @apply flex cursor-pointer items-center gap-1.5 text-sm font-medium text-[#717589] hover:text-[#39B689] xl:min-h-[36px];
    &:hover {
        img {
            filter: invert(78%) sepia(6%) saturate(3584%) hue-rotate(105deg) brightness(77%)
                contrast(91%);
        }
    }
}

.main__header {
    box-shadow: 0px 4px 10px 0px #a1a5a926;
}

@media (max-width: 1199px) {
    .main-mobile {
        min-height: calc(100svh - var(--header-height) - var(--menu-mobile-bottom));
    }
}
@media (max-width: 1199px) {
    .wrapper {
        &:has(.menu-bottom) {
            @apply pb-[var(--menu-mobile-bottom)];
        }
    }
}

.border-gradient {
    background-clip: padding-box;
    border: solid var(--size, 2px) transparent;
    &::before {
        content: '';
        margin: 0;
        position: absolute;
        z-index: -1;
        inset: 0;
        border-radius: inherit;
        background-image: var(
            --bg,
            linear-gradient(90deg, #ffebb4 0%, rgba(255, 235, 180, 0) 50%, #ffebb4 100%)
        );
    }
}
.text-gradient {
    background: var(
        --bg,
        linear-gradient(90deg, #ffebb4 0%, rgba(255, 235, 180, 0) 50%, #ffebb4 100%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.common-scrollbar {
    &::-webkit-scrollbar {
        width: 4px;
        border-radius: 100%;
        margin-left: 5px;
    }
    &::-webkit-scrollbar-thumb {
        background-color: #d9d9d9;
        border-radius: 4px;
        &:hover {
            background-color: #979fb0;
        }
    }
}

// #chat-widget-container {
//   &::after {
//     content: '';
//     display: block;
//     width: calc(100% - 2rem);
//     height: calc(100% - 2rem);
//     background: #fff;
//     border-radius: 1rem;
//     position: absolute;
//     left: 1rem;
//     top: 1rem;
//     z-index: -1;
//   }
// }
