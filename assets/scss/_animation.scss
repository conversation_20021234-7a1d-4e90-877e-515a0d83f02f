@keyframes shine {
    100% {
        left: 125%;
    }
}

@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(90deg);
    }
    50% {
        transform: rotate(180deg);
    }
    75% {
        transform: rotate(270deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// hover shine effect
.hover-shine {
    &__target {
        @apply relative overflow-hidden;
        &:before {
            content: '';
            @apply pointer-events-none absolute -left-full top-0 z-10 block h-full w-1/2 bg-transparent;
            background: -webkit-linear-gradient(
                left,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.5) 100%
            );
            background: -moz-linear-gradient(
                left,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.5) 100%
            );
            background: -o-linear-gradient(
                left,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.5) 100%
            );
            background: linear-gradient(
                to right,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.5) 100%
            );
            -webkit-transform: skew(-25deg);
            -moz-transform: skew(-25deg);
            -ms-transform: skew(-25deg);
            -o-transform: skew(-25deg);
            transform: skew(-25deg);
        }
    }

    &:hover {
        .hover-shine__target:before {
            -webkit-animation: shine 1s;
            -moz-animation: shine 1s;
            -ms-animation: shine 1s;
            -o-animation: shine 1s;
            animation: shine 1s;
        }
    }
}
@keyframes progressBar {
    0% {
        @apply w-0;
    }
    20% {
        @apply w-1/5;
    }
    50% {
        @apply w-1/2;
    }
    60% {
        @apply w-1/2;
    }
    80% {
        @apply w-2/3;
    }
    100% {
        @apply w-[90%];
    }
}
