.home-tab {
    &__item {
        background: linear-gradient(116.33deg, #1a334d -10.12%, rgba(9, 24, 39, 0.2) 96.09%);

        @apply rounded-xl px-1 pb-[6px] pt-2 text-center text-xs text-white;
        &:not(.active) {
            img {
                @apply grayscale;
            }
        }
        &.active {
            background: url(/assets/images/home/<USER>/item-bg-active.png) no-repeat center /
                cover;
        }
    }
}
@media (max-width: 1199px) {
    body {
        &:has(.menu__left) {
            &:has(.show-breadcrumb-mb) {
                .main__content {
                    @apply h-[calc(100svh-var(--header-height)-48px)];
                }
            }
        }
    }
    .main-mobile {
        position: fixed;
        top: var(--header-height);
        overflow: hidden;
        height: calc(100svh - var(--header-height));
        &__content {
            overflow: hidden auto;
        }
    }
    .menu {
        &__left {
            height: calc(100svh - var(--header-height));
            padding-bottom: 100px;
        }
    }
}
