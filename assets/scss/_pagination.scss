.pagination-custom {
    .page-number {
        display: flex;
        align-items: center;
        background: #f2f2f2;
        padding: 8px;
        border-radius: 8px;
        box-shadow: -1px 1px 1px #d2d2d2;

        .page-item {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
        }

        .page-item {
            &.active {
                border-radius: 4px;
                background: #22a374;
            }
        }

        @media (hover: hover) {
            .page-item:hover {
                border-radius: 4px;
                background: #22a374;
            }
        }

        .dot-page {
            pointer-events: none;
        }
    }
    .page-item {
        margin: 0 8px;
        cursor: pointer;

        .page-link {
            background-color: transparent;
            letter-spacing: 1px;
            color: #868887;
            font-size: 14px;
            box-sizing: border-box;
            border: none;
            box-shadow: none;
            font-weight: 500;
            display: block;
            // @include media-query(max, $size-small){
            //   padding: 0.4rem 0.5rem;
            // }
            img {
                vertical-align: middle;
                width: 24px;
                height: 24px;
            }
            &.icon {
                border: none;
                border-radius: 0;
            }
        }
        &.active,
        &:hover {
            .page-link {
                color: white;
            }
        }
        &.disable {
            cursor: not-allowed;
            opacity: 0.3;

            .page-link {
                border: none;
            }
            &:hover {
                a {
                    background-color: transparent;
                }
            }
        }
    }
    .dot {
        width: 24px;
        height: 24px;
        text-align: center;
        span {
            position: relative;
            bottom: 0;
            color: white;
        }
    }
}
