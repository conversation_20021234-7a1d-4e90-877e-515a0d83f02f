<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-5.25089" y="-5.24924" width="32.4998" height="32.5"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3.58px);clip-path:url(#bgblur_0_12186_36952_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_iii_12186_36952)" data-figma-bg-blur-radius="7.15909">
<rect x="1.9082" y="1.90985" width="18.1818" height="18.1818" rx="9.09091" fill="white"/>
</g>
<foreignObject x="3.84189" y="-6.29514" width="24.4549" height="34.5909"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3.58px);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_iii_12186_36952)" data-figma-bg-blur-radius="7.15909">
<path d="M11.001 19.6367C15.7707 19.6367 19.6373 15.77 19.6373 11.0003C19.6373 6.23058 15.7707 2.36395 11.001 2.36395" stroke="#CBCBCB" stroke-width="3"/>
</g>
<foreignObject x="-6.29483" y="-6.29575" width="24.4549" height="34.5909"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3.58px);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_iii_12186_36952)" data-figma-bg-blur-radius="7.15909">
<path d="M11.001 2.36332C6.23124 2.36332 2.36461 6.22995 2.36461 10.9997C2.36461 15.7694 6.23124 19.636 11.001 19.636" stroke="#CBCBCB" stroke-width="3"/>
</g>
<defs>
<filter id="filter0_iii_12186_36952" x="-5.25089" y="-5.24924" width="32.4998" height="32.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.53409"/>
<feGaussianBlur stdDeviation="0.767045"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_12186_36952"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.04545"/>
<feGaussianBlur stdDeviation="1.02273"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_12186_36952" result="effect2_innerShadow_12186_36952"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.511364"/>
<feGaussianBlur stdDeviation="0.255682"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_12186_36952" result="effect3_innerShadow_12186_36952"/>
</filter>
<clipPath id="bgblur_0_12186_36952_clip_path"><rect transform="translate(5.25089 5.24924)" x="1.9082" y="1.90985" width="18.1818" height="18.1818" rx="9.09091"/>
</clipPath><filter id="filter1_iii_12186_36952" x="3.84189" y="-6.29514" width="24.4549" height="34.5909" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.53409"/>
<feGaussianBlur stdDeviation="0.767045"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_12186_36952"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.04545"/>
<feGaussianBlur stdDeviation="1.02273"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_12186_36952" result="effect2_innerShadow_12186_36952"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.511364"/>
<feGaussianBlur stdDeviation="0.255682"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_12186_36952" result="effect3_innerShadow_12186_36952"/>
</filter>
<filter id="filter2_iii_12186_36952" x="-6.29483" y="-6.29575" width="24.4549" height="34.5909" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.53409"/>
<feGaussianBlur stdDeviation="0.767045"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_12186_36952"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.04545"/>
<feGaussianBlur stdDeviation="1.02273"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_12186_36952" result="effect2_innerShadow_12186_36952"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.511364"/>
<feGaussianBlur stdDeviation="0.255682"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_12186_36952" result="effect3_innerShadow_12186_36952"/>
</filter>
</defs>
</svg>
