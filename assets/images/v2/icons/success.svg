<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_12186_26037)">
<rect opacity="0.8" x="1.11182" y="1.11169" width="17.7778" height="17.7778" rx="8.88889" fill="#108429"/>
<foreignObject x="-8.75" y="-8.75" width="37.5" height="37.5"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4.38px);clip-path:url(#bgblur_1_12186_26037_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_iii_12186_26037)" data-figma-bg-blur-radius="8.75">
<rect width="20" height="20" rx="10" fill="#17BA3A"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.6834 6.97657C15.1055 7.38982 15.1055 8.05984 14.6834 8.47309L9.54822 13.4995C9.12604 13.9128 8.44153 13.9128 8.01934 13.4995L5.31664 10.854C4.89445 10.4408 4.89445 9.77077 5.31664 9.35752C5.73883 8.94427 6.42333 8.94427 6.84552 9.35752L8.78378 11.2548L13.1545 6.97657C13.5767 6.56331 14.2612 6.56331 14.6834 6.97657Z" fill="white"/>
</g>
<defs>
<filter id="filter0_iii_12186_26037" x="-8.75" y="-8.75" width="37.5" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.875"/>
<feGaussianBlur stdDeviation="0.9375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_12186_26037"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.5"/>
<feGaussianBlur stdDeviation="1.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_12186_26037" result="effect2_innerShadow_12186_26037"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.625"/>
<feGaussianBlur stdDeviation="0.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_12186_26037" result="effect3_innerShadow_12186_26037"/>
</filter>
<clipPath id="bgblur_1_12186_26037_clip_path"><rect transform="translate(8.75 8.75)" width="20" height="20" rx="10"/>
</clipPath><clipPath id="clip0_12186_26037">
<rect width="20" height="20" rx="10" fill="white"/>
</clipPath>
</defs>
</svg>
