import { PAGE_URL } from '~/constants/page-urls'

export const sportList = [
    {
        id: 1,
        title: 'k - sports',
        subTitle: '<PERSON><PERSON> phục mọi giới hạn',
        imgSrc: '/assets/images/v2/sports/k-sports.webp',
        imgSrcMobile: '/assets/images/v2/sports/k-sports-mb.webp',
        logo: '/assets/images/v2/sports/k-sports-logo.webp',
        to: PAGE_URL.K_SPORTS,
    },
    {
        id: 2,
        title: 'saba - sports',
        subTitle: 'Th<PERSON> thao cuồng nhi<PERSON>t',
        imgSrc: '/assets/images/v2/sports/saba-sports.webp',
        imgSrcMobile: '/assets/images/v2/sports/saba-sports-mb.webp',
        logo: '/assets/images/v2/sports/saba-logo.webp',
        to: PAGE_URL.SABA_SPORTS,
    },
    {
        id: 3,
        title: 'bti - sports',
        subTitle: 'Tỉ lệ cược cực cao',
        imgSrc: '/assets/images/v2/sports/bti-sports.webp',
        imgSrcMobile: '/assets/images/v2/sports/bti-sports-mb.webp',
        logo: '/assets/images/v2/sports/bti-logo.webp',
        to: PAGE_URL.BTI_SPORTS,
    },
    {
        id: 4,
        title: 'im - sports',
        subTitle: 'Sống trọn từng giây',
        imgSrc: '/assets/images/v2/sports/im-sports.webp',
        imgSrcMobile: '/assets/images/v2/sports/im-sports-mb.webp',
        logo: '/assets/images/v2/sports/im-play-logo.webp',
        to: PAGE_URL.IM_SPORTS,
        isRequireLogin: true,
    },
]

export const virtualSportList = [
    {
        id: 1,
        title: 'k - sports',
        subTitle: 'Cược thông minh, rinh quà bự',
        imgSrc: '/assets/images/v2/sports/virual-k-sports.webp',
        imgSrcMobile: '/assets/images/v2/sports/virual-k-sports-mb.webp',
        logo: '/assets/images/v2/sports/k-sports-logo.webp',
        to: PAGE_URL.VIRTUAL_K_SPORTS,
    },
    {
        id: 2,
        title: 'im - sports',
        subTitle: 'Tận hưởng từng khoảnh khắc',
        imgSrc: '/assets/images/v2/sports/virual-im-play.webp',
        imgSrcMobile: '/assets/images/v2/sports/virual-im-play-mb.webp',
        logo: '/assets/images/v2/sports/im-play-logo.webp',
        to: PAGE_URL.IM_PLAY,
    },
    {
        id: 3,
        title: 'saba - sports',
        subTitle: 'Vui cùng thể thao ảo',
        imgSrc: '/assets/images/v2/sports/virual-saba-sports.webp',
        imgSrcMobile: '/assets/images/v2/sports/virual-saba-sports-mb.webp',
        logo: '/assets/images/v2/sports/saba-logo.webp',
        to: PAGE_URL.VIRTUAL_SABA_SPORTS,
        isRequireLogin: true,
    },
    {
        id: 4,
        title: 'pp - sports',
        subTitle: 'Bứt phá bản thân',
        imgSrc: '/assets/images/v2/sports/virual-pp-sports.webp',
        imgSrcMobile: '/assets/images/v2/sports/virual-pp-sports-mb.webp',
        logo: '/assets/images/v2/sports/pp-sports-logo.webp',
        to: PAGE_URL.VIRTUAL_PP_SPORTS,
        isRequireLogin: true,
    },
]
