import { WS168, GA28 } from '~/constants/apiLink'
import { PAGE_URL } from '~/constants/page-urls'

export const BADGE_TYPE = {
    hot: 'HOT',
    live: 'LIVE',
    new: 'NEW',
}

export const BADGE_TYPE_MAPPING = {
    [BADGE_TYPE.hot]: {
        icon: 'hot',
        i18Key: 'hot',
        backgroundColor: 'bg-[#FF573F]',
    },
    [BADGE_TYPE.live]: {
        icon: 'live',
        i18Key: 'hot',
        backgroundColor: 'red',
    },
    [BADGE_TYPE.new]: {
        icon: 'star',
        i18Key: 'hot',
        backgroundColor: 'bg-[#09CA89]',
    },
}

export const MAIN_MENU_LIST = [
    {
        id: 'favorite',
        order: 0,
        order_mobile: 0,
        url: '/casino/all?sort=favorite',
        title: 'Favorite',
        i18_key: 'header.nav_menu.favorite',
        icon_name: 'favorite',
        is_show_desktop: false,
        is_show_mobile: true,
    },
    {
        id: 'sport',
        order: 1,
        order_mobile: 1,
        url: '/ca-cuoc-the-thao',
        title: 'Sports',
        i18_key: 'header.nav_menu.sport',
        icon_name: 'the-thao',
        is_show_desktop: true,
        is_show_mobile: true,
        subMenu: [
            {
                id: 'sportBook',
                order: 1,
                i18_key: 'header.nav_menu.sport',
                subMenu: [
                    {
                        id: 1,
                        order: 1,
                        name: 'k-sports',
                        key: 'k-sports',
                        url: PAGE_URL.K_SPORTS,
                    },
                    {
                        id: 2,
                        order: 2,
                        name: 'saba-sports',
                        key: 'saba-sports',
                        url: PAGE_URL.SABA_SPORTS,
                    },
                    {
                        id: 3,
                        order: 3,
                        name: 'bti-sports',
                        key: 'bti-sports',
                        url: PAGE_URL.BTI_SPORTS,
                    },
                    {
                        id: 4,
                        order: 4,
                        name: 'im-sports',
                        key: 'im-sports',
                        url: PAGE_URL.IM_SPORTS,
                    },
                ],
            },
            {
                id: 'virtualSports',
                order: 2,
                i18_key: 'header.nav_menu.virtual-sports',
                subMenu: [
                    {
                        id: 1,
                        order: 1,
                        name: 'virtual-k-sports',
                        key: 'virtual-k-sports',
                        url: PAGE_URL.VIRTUAL_K_SPORTS,
                    },
                    {
                        id: 2,
                        order: 2,
                        name: 'virtual-im-play-sports',
                        key: 'virtual-im-play-sports',
                        url: PAGE_URL.IM_PLAY,
                    },
                    {
                        id: 3,
                        order: 3,
                        name: 'virtual-saba-sports',
                        key: 'virtual-saba-sports',
                        url: PAGE_URL.VIRTUAL_SABA_SPORTS,
                    },
                    {
                        id: 4,
                        order: 4,
                        name: 'virtual-pp-sports',
                        key: 'virtual-pp-sports',
                        url: PAGE_URL.VIRTUAL_PP_SPORTS,
                        isRequireLogin: true,
                    },
                ],
            },
        ],
    },
    {
        id: 'nohu',
        order: 4,
        order_mobile: 4,
        url: '/cong-game/no-hu',
        title: 'Nohu',
        i18_key: 'header.nav_menu.nohu',
        icon_name: 'no-hu',
        is_show_desktop: true,
        is_show_mobile: true,
    },
    {
        id: 'live_casino',
        order: 2,
        order_mobile: 2,
        url: '/casino',
        regex: new RegExp('/casino*'),
        title: 'Casino',
        i18_key: 'header.nav_menu.live_casino',
        icon_name: 'live-casino',
        type: BADGE_TYPE.live,
        is_show_desktop: true,
        is_show_mobile: true,
    },
    {
        id: 'quayso',
        order: 3,
        order_mobile: 3,
        url_mobile: '/quay-so',
        title: 'quayso_title',
        i18_key: 'header.nav_menu.quayso',
        icon_name: 'quay-so',
        type: BADGE_TYPE.hot,
        is_show_desktop: true,
        is_show_mobile: true,
        subMenu: [
            {
                order: 1,
                name: 'quayso1',
                key: 'quayso1',
                url: '/quay-so/quayso1',
            },
            {
                order: 2,
                name: 'quayso2',
                key: 'quayso2',
                url: '/quay-so/quayso2',
            },
            {
                order: 3,
                name: 'quayso5',
                key: 'quayso5',
                url: '/quay-so/quayso5',
            },
            {
                order: 4,
                name: 'quayso3',
                key: 'quayso3',
                url: '/quay-so/quayso3',
            },
            {
                order: 5,
                name: 'numbergame1',
                key: 'numbergame1',
                url: '/quay-so/numbergame1',
            },
            {
                order: 6,
                name: 'numbergame2',
                key: 'numbergame2',
                url: '/quay-so/numbergame2',
            },
        ],
    },
    {
        id: 'fishing',
        order: 8,
        order_mobile: 9,
        url: '/cong-game/ban-ca',
        title: 'Fishing',
        i18_key: 'header.nav_menu.banca',
        icon_name: 'ban-ca',
        is_show_desktop: true,
        is_show_mobile: true,
    },
    {
        id: 'cock_fight',
        order: 11,
        order_mobile: 7,
        title: 'Cockfight',
        i18_key: 'header.nav_menu.cockfight',
        icon_name: 'da-ga',
        is_show_desktop: true,
        is_show_mobile: true,
        url_mobile: '/cockfight',
        subMenu: [
            {
                id: 'ga28',
                url: '/cockfight/ga28',
                name: 'ga28',
                key: 'ga28',
                link: GA28,
                isRequireLogin: true,
            },
            {
                id: 'ws168',
                url: '/cockfight/ws168',
                name: 'ws168',
                key: 'ws168',
                link: WS168,
                isRequireLogin: true,
            },
        ],
    },
    {
        id: 'game_cards',
        order: 5,
        order_mobile: 5,
        url: '/cong-game/game-bai',
        title: 'Game Cards',
        i18_key: 'header.nav_menu.game_cards',
        icon_name: 'game-cards',
        is_show_desktop: true,
        is_show_mobile: true,
    },
    {
        id: 'keno',
        order: 7,
        order_mobile: 8,
        url: '/cong-game/keno',
        title: 'keno_title',
        i18_key: 'header.nav_menu.keno',
        icon_name: 'keno',
        is_show_desktop: true,
        is_show_mobile: true,
    },
    {
        id: 'esports',
        order: 9,
        order_mobile: 10,
        url: '/esports',
        title: 'esports',
        i18_key: 'header.nav_menu.esports_mobile',
        icon_name: 'the-thao-ao',
        is_show_desktop: true,
        is_show_mobile: true,
    },
    {
        id: 'virtual_sports',
        order: 10,
        order_mobile: 11,
        url: '/virtual-sports',
        title: 'virtual_sports',
        i18_key: 'header.nav_menu.virtual-sports',
        icon_name: 'e-sports',
        is_show_desktop: false,
        is_show_mobile: true,
    },
    {
        id: 'lode',
        order: 6,
        order_mobile: 6,
        title: 'Lode',
        i18_key: 'header.nav_menu.lode',
        icon_name: 'lo-de',
        is_show_desktop: true,
        is_show_mobile: true,
        url_mobile: '/lo-de',
        subMenu: [
            {
                id: 'lode3mien',
                url: '/lo-de/lode3mien',
                name: 'lode_3_mien',
                key: 'lode_3_mien',
                isRequireLogin: true,
            },
            {
                id: 'lodesieutoc',
                url: '/lo-de/lode-sieutoc',
                title: 'lode_sieu_toc',
                name: 'lode_sieu_toc',
                key: 'lode_sieu_toc',
            },
            {
                id: 'mega645',
                url: '/lo-de/mega645',
                title: 'mega645',
                name: 'mega645',
                key: 'mega645',
            },
            {
                id: 'mega655',
                url: '/lo-de/power655',
                title: 'power655',
                name: 'power655',
                key: 'power655',
            },
            {
                id: 'lodemd5',
                url: '/lo-de/lodemd5',
                title: 'lodemd5',
                name: 'lodemd5',
                key: 'lodemd5',
            },
        ],
    },
    {
        id: 'games',
        order: 12,
        order_mobile: 12,
        url: '/cong-game',
        regex: new RegExp(`${PAGE_URL.GAMES.INDEX}(?!/(?:game-bai|slots|ban-ca|nohu|keno)).*`),
        title: 'Games',
        i18_key: 'header.nav_menu.games',
        icon_name: 'cong-game',
        is_show_desktop: true,
        is_show_mobile: true,
    },
    {
        id: 'promotion',
        order: 13,
        order_mobile: 13,
        url: '/khuyen-mai',
        regex: new RegExp('/events*'),
        title: 'promotion',
        i18_key: 'header.nav_menu.events_home',
        icon_name: 'khuyen-mai',
        is_show_desktop: true,
        is_show_mobile: true,
    },
    {
        id: 'support',
        order: 14,
        order_mobile: 14,
        url: '/tro-giup',
        title: 'support',
        i18_key: 'sidebar.support_mobile',
        icon_name: 'tro-giup',
        is_show_desktop: false,
        is_show_mobile: true,
    },
    {
        id: 'help',
        order: 15,
        order_mobile: 15,
        url: '/link',
        title: 'help',
        i18_key: 'sidebar.link_mobile',
        icon_name: 'link',
        is_show_desktop: false,
        is_show_mobile: true,
    },
]

export const ACCOUNT_MENU_LIST = {
    'user_sidebar.daskboard': [
        {
            key: 'dashboard',
            name: 'user_sidebar.dashboard',
            link: ['/user/dashboard'],
        },
    ],
    'user_sidebar.transactions': [
        {
            key: 'user-deposit',
            name: 'user_sidebar.deposit',
            link: ['/user/deposit'],
        },
        {
            key: 'user-withdraw',
            name: 'user_sidebar.withdraw',
            link: ['/user/withdraw'],
        },
        {
            key: 'bank-account',
            name: 'user_sidebar.bank_account',
            link: ['/user/bank-account'],
        },
        {
            key: 'transaction-history',
            name: 'user_sidebar.transaction_bet_history',
            link: ['/user/bet-history', '/user/transaction-history'],
        },
    ],
    'user_sidebar.infomation': [
        {
            key: 'infomation',
            name: 'user_sidebar.profile',
            link: ['/user/profile'],
        },
        {
            key: 'promotion',
            name: 'user_sidebar.promotions',
            link: ['/user/general-promotions'],
        },
    ],
}
