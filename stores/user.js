import { defineStore } from 'pinia'
import { STATUS_OK, UNAUTHORIZED } from '~/constants/apiStatus'
import { useRecaptcha } from '~/composables/useRecaptcha'
import { useGame } from '~/composables/use-game'
import { useCasino } from '~/composables/use-casino'
// import { usePlayGame } from '~/composables/use-play-game'
import { useAuthService, useUserService, useVerificationService } from '~/services'
import { useAlertStore, useModalStore, useCommonStore } from '~/stores'
import { storeToRefs } from 'pinia'
import { MIN_BALANCE_THRESHOLD } from '~/constants/user'
import { PAGE_URL } from '~/constants/page-urls'
// import { HISTORY_ACTIONS, HISTORY_METHOD, HISTORY_STATUS, HISTORY_TYPES } from '~/constants'
import { GAME_TYPE } from '~/constants'

export const useUserStore = defineStore(
    'userStore',
    () => {
        const authService = useAuthService()
        const userService = useUserService()
        const verificationService = useVerificationService()

        const useAlertStoreInstance = useAlertStore()
        const user = ref(null)
        const balance = ref(0)
        const runtimeConfig = useRuntimeConfig()
        const useNuxtAppInstance = useNuxtApp()
        const { executeRecaptcha } = useRecaptcha()
        // const { openSubLinkV2 } = usePlayGame()

        const isLoggedIn = ref(false)
        const refreshId = ref('')
        const navigateAfterLogin = ref('')
        const isLoadingVerifyTelegramOtp = ref(false)
        const isLoadingVerifyEmail = ref(false)
        const isLoadingVerifyEmailOtp = ref(false)
        const emailToVerify = ref('')
        const sendEmailOtpCountdown = ref(0)
        const sendEmailOtpInterval = ref('')

        const showLoggedOutAlert = useState('showLoggedOutAlert', () => false)

        const useModalStoreInstance = useModalStore()
        const {
            showLoginModal,
            showVerifyTelegramModal,
            showVerifyEmailModal,
            showVerifyEmailOtpModal,
            showUpdateFullnameModal,
        } = storeToRefs(useModalStoreInstance)
        const { showLoginFailedModal, showEmailOtpFailedModal, showTelegramOtpFailedModal } =
            useNotify()

        const commonStoreInstance = useCommonStore()
        const { currentGame } = storeToRefs(commonStoreInstance)

        const { delay } = useCommon()
        // const recentTransactionRef = ref({})
        // const isNewbie = computed(
        //     () => !user.value?.package_id && !balance.value
        // )

        const playerErrorHandler = payload => {
            const reg500 = '50[0-9]'
            const reg400 = '40[0-9]'
            const globalRegex500 = new RegExp(reg500, 'g')
            const globalRegex400 = new RegExp(reg400, 'g')
            if (globalRegex500.test(payload?.value?.statusCode)) {
                useAlertStoreInstance.showMessage({
                    message_key: 'error.big_is_busy',
                    type: 'ERROR',
                })
            } else if (globalRegex400.test(payload?.value?.statusCode)) {
                this.showMessage({
                    message_key: 'error.big_is_busy',
                    type: 'ERROR',
                })
            } else if (payload?.value?.data?.data?.message) {
                // the payload is error object from fetcher
                // useAlertStoreInstance.showMessage({
                //     message: payload?.value?.data?.data?.message,
                //     type: 'ERROR',
                // })
                useAlertStoreInstance.open({
                    title: 'Thất bại!',
                    message: payload?.value?.data?.data?.message,
                    type: 'ERROR',
                })
            } else if (payload?.value?.message) {
                // the payload is data object from fetcher
                // useAlertStoreInstance.showMessage({
                //     message: payload?.value?.message,
                //     type: 'ERROR',
                // })
                useAlertStoreInstance.open({
                    title: 'Thất bại!',
                    message: payload?.value?.message,
                    type: 'ERROR',
                })
            } else {
                useAlertStoreInstance.open({
                    title: 'Thất bại!',
                    message: payload?.value?.data?.data?.message,
                    message_key: 'error.unknown',
                    type: 'ERROR',
                })
                // useAlertStoreInstance.showMessage({
                //     message_key: 'error.unknown',
                //     type: 'ERROR',
                // })
            }
        }

        const verifyUsername = async username => {
            try {
                const { data, pending, error } = await userService.verifyUsername(username)
                // response {exist: true}
                if (data.value.exist) {
                    useAlertStoreInstance.showMessage({
                        message: 'Tài khoản đã tồn tại',
                        type: 'SUCCESS',
                    })
                } else {
                    useAlertStoreInstance.showMessage({
                        message: 'Tài khoản không tồn tại',
                        type: 'ERROR',
                    })
                }
                return { data, pending, error }
            } catch (error) {
                // TODO: show error message
                if (error?.value?.data?.data?.message) {
                    useAlertStoreInstance.showMessage({
                        message: error?.value?.data?.data?.message,
                        type: 'ERROR',
                    })
                } else {
                    console.log('show error message', error)
                }
                return { data: null, pending: false, error }
            }
        }
        const handleOpenGameAfter = (isRedirect = '') => {
            navigateAfterLogin.value = ''
            if (currentGame.value.type === GAME_TYPE.game) {
                const { handleShowGame } = useGame()
                handleShowGame()
            } else if (currentGame.value.type === GAME_TYPE.casino) {
                const { handleShowGame } = useCasino()
                handleShowGame()
            } else if (isRedirect) {
                if (isRedirect === 'login') {
                    navigateAfterLogin.value = PAGE_URL.DEPOSIT_LINK.INDEX
                } else {
                    navigateTo(PAGE_URL.DEPOSIT_LINK.INDEX)
                }
            }
        }
        const register = async payload => {
            try {
                const token = await executeRecaptcha()
                const { data, pending, error } = await authService.register({
                    ...payload,
                    token: token,
                })
                // set user only if status is OK
                if (data.value?.status === STATUS_OK) {
                    window?.dataLayer.push({ event: 'formSubmitted', formName: 'Form_Register' })

                    setUserData(data?.value?.data[0])
                    // posthog identify
                    const userProps = data?.value?.data[0]
                    userProps.token = ''
                    userProps.tp_token = ''
                    userProps.etoken = ''
                    useNuxtAppInstance.$identifyUser(userProps.id, userProps)
                    const postHog = useNuxtAppInstance.$posthog()
                    if (postHog) {
                        postHog.capture('user_signup', userProps)
                    }
                    useAlertStoreInstance.open({
                        title: 'Thành công!',
                        message: 'Đăng ký tài khoản thành công',
                        type: 'SUCCESS',
                    })
                    handleOpenGameAfter('register')
                } else if (data.value.message) {
                    useAlertStoreInstance.open({
                        title: 'Thất bại!',
                        message: data.value.message,
                        type: 'ERROR',
                    })
                } else {
                    throw data
                }
                return { data, pending, error }
            } catch (error) {
                // TODO: show error message
                playerErrorHandler(error)
                // a fallback
                return { data: null, pending: false, error }
            }
        }

        const login = async payload => {
            try {
                const token = await executeRecaptcha()
                const { data, pending, error } = await authService.login({
                    username: payload.username,
                    password: payload.password,
                    token: token,
                })
                // set user only if status is ok
                if (data.value.status === STATUS_OK) {
                    window?.dataLayer.push({ event: 'formSubmitted', formName: 'Form_Login' })

                    useAlertStoreInstance.open({
                        title: 'Thành công!',
                        message: 'Đăng nhập tài khoản thành công',
                        type: 'SUCCESS',
                    })
                    // posthog identify
                    const userProps = { ...data?.value?.data[0] }
                    userProps.token = ''
                    userProps.tp_token = ''
                    userProps.etoken = ''
                    useNuxtAppInstance.$identifyUser(userProps.id, userProps)
                    setUserData(data?.value?.data[0])

                    // await getRecentTransaction()

                    // Define the minimum balance threshold required to avoid redirecting to the deposit page
                    if (data?.value?.data[0]?.balance > MIN_BALANCE_THRESHOLD) {
                        if (
                            !navigateAfterLogin.value ||
                            navigateAfterLogin.value === PAGE_URL.DEPOSIT_LINK.INDEX
                        ) {
                            navigateAfterLogin.value = ''
                        }
                        handleOpenGameAfter()
                    } else {
                        handleOpenGameAfter('login')
                    }
                    // else {
                    //     navigateAfterLogin.value = recentTransactionRef.value?.recentDepositLink || PAGE_URL.DEPOSIT_LINK.INDEX
                    // }

                    // save username & saveAccount to local storage
                    if (payload.saveAccount) {
                        localStorage.setItem('username', payload.username)
                        localStorage.setItem('saveAccount', '1')
                    } else {
                        localStorage.setItem('username', '')
                        localStorage.setItem('saveAccount', '0')
                    }
                    showLoginModal.value = false
                } else if (data?.value?.status !== STATUS_OK) {
                    showLoginModal.value = false
                    // showLoginFailedModal(data.value.message)
                    useAlertStoreInstance.open({
                        title: 'Thất bại!',
                        message: data.value?.message?.toString() || 'Đăng nhập tài khoản thất bại!',
                        type: 'ERROR',
                    });
                    clearUserData()
                } else {
                    await logout()
                    clearUserData()
                    throw data
                }
                return { data, pending, error }
            } catch (error) {
                console.error('Login error:', error)
                // TODO: show error message
                // showErrorMessage(error)
                // return { data: null, pending: false, error }
            }
        }

        const loginToken = async payload => {
            try {
                if (!payload?.token) {
                    throw new Error('Token is missing')
                }
                const { data } = await authService.loginToken(payload.token)

                if (!data) {
                    throw new Error('No data returned from the server')
                }
                if (data?.value?.status === STATUS_OK) {
                    setUserData(data?.value?.data[0])
                }
                return { data }
            } catch (err) {
                return { error: err }
            }
        }

        const logout = async () => {
            try {
                const { data, pending, error } = await authService.logout('/logout')
                // posthog identify reset
                const postHog = useNuxtAppInstance.$posthog()
                if (postHog) {
                    postHog.reset()
                }
                clearUserData()
                return { data, pending, error }
            } catch (error) {
                clearUserData()
                return { data: null, pending: false, error }
            } finally {
                console.clear('goodbye')
            }
        }

        const refresh = async () => {
            try {
                const { data, pending, error } = await authService.refresh()
                if (data?.value?.user) {
                    setUserData(data?.value?.user)
                } else {
                    console.log('refresh is ok but no user')
                    throw error
                }
                return { data, pending, error }
            } catch (error) {
                if (error.value.data.status === UNAUTHORIZED) {
                    clearUserData()
                    showLoggedOutAlert.value = true
                    navigateTo('/')
                    return { data: null, pending: false, error: error }
                }
            }
        }

        const updatePassword = async payload => {
            return await userService.updatePassword(payload)
        }

        const updateProfile = async payload => {
            try {
                const { data } = await userService.updateProfile(payload)
                if (data.value.status === STATUS_OK) {
                    await refresh()
                    showUpdateFullnameModal.value = false
                    useAlertStoreInstance.open({
                        title: 'Thành công!',
                        message: 'Cập nhật tên hiển thị thành công',
                        type: 'SUCCESS',
                    })
                    delay(2000)

                    if (currentGame.value.type === GAME_TYPE.game) {
                        const { handleShowGame } = useGame()

                        handleShowGame()
                    } else if (currentGame.value.type === GAME_TYPE.casino) {
                        const { handleShowGame } = useCasino()
                        handleShowGame()
                    }
                } else {
                    useAlertStoreInstance.open({
                        title: 'Thất bại!',
                        message: data.value?.message,
                        type: 'ERROR',
                    })
                }
                return data
            } catch (error) {
                useAlertStoreInstance.showMessage({
                    type: 'ERROR',
                    message:
                        error?.value?.data?.message ||
                        error?.value?.message ||
                        error?.value ||
                        error,
                })
                return null
            }
        }

        const setUserData = payload => {
            user.value = payload
            isLoggedIn.value = true
            balance.value = payload.balance
            clearRefresh()
            startRefresh()
        }
        const clearUserData = () => {
            user.value = null
            emailToVerify.value = ''
            isLoggedIn.value = false
            clearRefresh()
        }

        const startRefresh = () => {
            refreshId.value = setInterval(() => refresh(), runtimeConfig.public.refreshInterval)
        }
        const clearRefresh = () => {
            clearInterval(refreshId.value)
        }

        const countdown = () => {
            if (sendEmailOtpCountdown.value > 0) {
                sendEmailOtpCountdown.value--
            } else {
                clearInterval(sendEmailOtpInterval.value)
                sendEmailOtpInterval.value = null
            }
        }

        const verifyEmail = async payload => {
            try {
                isLoadingVerifyEmail.value = true
                const { data } = await verificationService.verifyEmail(payload)
                if (data.value.status === STATUS_OK) {
                    emailToVerify.value = payload.email
                    showVerifyEmailModal.value = false
                    showVerifyEmailOtpModal.value = true
                    sendEmailOtpCountdown.value = 60
                    sendEmailOtpInterval.value = setInterval(() => countdown(), 1000)
                } else {
                    throw data
                }
            } catch (error) {
                showVerifyEmailModal.value = false
                useAlertStoreInstance.showMessageError(error)
            } finally {
                isLoadingVerifyEmail.value = false
            }
        }

        const resendVerifyEmail = async () => {
            try {
                sendEmailOtpCountdown.value = 60
                sendEmailOtpInterval.value = setInterval(() => countdown(), 1000)
                await verifyEmail({ email: emailToVerify.value })
            } catch (error) {
                console.log(error)
            }
        }

        const verifyEmailOtp = async payload => {
            try {
                isLoadingVerifyEmailOtp.value = true
                const { data } = await verificationService.verifyEmailOtp(payload)
                showVerifyEmailOtpModal.value = false
                if (data.value.status === STATUS_OK) {
                    useAlertStoreInstance.open({
                        title: 'Thành công!',
                        message: 'Xác minh Email thành công',
                        type: 'SUCCESS',
                    })
                    // useAlertStoreInstance.showMessageSuccess(data.value?.message)
                } else {
                    showEmailOtpFailedModal(data.value?.message)
                }
            } catch (error) {
                useAlertStoreInstance.showMessageError(error, {
                    icon: '/assets/images/v2/icons/email-error.webp',
                })
            } finally {
                showVerifyEmailOtpModal.value = false
                isLoadingVerifyEmailOtp.value = false
            }
        }

        const verifyTelegramOtp = async payload => {
            try {
                isLoadingVerifyTelegramOtp.value = true
                const { data } = await verificationService.verifyTelegramOtp(payload)
                showVerifyTelegramModal.value = false
                if (data.value.status === STATUS_OK) {
                    useAlertStoreInstance.showMessageSuccess(data.value.message)
                } else {
                    showTelegramOtpFailedModal()
                }
            } catch (error) {
                useAlertStoreInstance.showMessageError(error, {
                    icon: '/assets/images/v2/icons/tele-error.webp',
                })
            } finally {
                showVerifyTelegramModal.value = false
                isLoadingVerifyTelegramOtp.value = false
            }
        }

        // const getRecentTransactionLink = (recentMethod = '', currentPath = '') => {
        //     switch (recentMethod) {
        //         case HISTORY_METHOD.IBANKING: // Mapping with CODEPAY
        //             return PAGE_URL.DEPOSIT_LINK.CODE_PAY
        //         case HISTORY_METHOD.PHONE_CARD:
        //             return currentPath.includes(PAGE_URL.DEPOSIT_LINK.INDEX) ? PAGE_URL.DEPOSIT_LINK.PHONE_CARD : PAGE_URL.WITHDRAW_LINK.PHONE_CARD
        //         case HISTORY_METHOD.E_WALLET:
        //             return PAGE_URL.DEPOSIT_LINK.E_WALLET
        //         case HISTORY_METHOD.CRYPTOPAY:
        //             return currentPath.includes(PAGE_URL.DEPOSIT_LINK.INDEX) ?  PAGE_URL.DEPOSIT_LINK.CRYPTO_PAY : PAGE_URL.WITHDRAW_LINK.CRYPTO
        //         case HISTORY_METHOD.BANK:
        //             return PAGE_URL.WITHDRAW_LINK.BANK
        //         default:
        //             return currentPath.includes(PAGE_URL.DEPOSIT_LINK.INDEX) ? PAGE_URL.DEPOSIT_LINK.INDEX : PAGE_URL.WITHDRAW_LINK.INDEX
        //     }
        // }

        // const getRecentTransaction = async () => {
        //     const params = {
        //         page: 1,
        //         limit: '500',
        //     }
        //     try {
        //         const { data, error } = await fetcher.get(
        //             '/lsgd',
        //             params,
        //             5000
        //         )
        //         if (error.value) {
        //             return PAGE_URL.DEPOSIT_LINK.CODE_PAY
        //         }
        //         const recentTransaction = (actionType) => data.value?.data.find(
        //             (transaction) =>
        //                 transaction?.type === HISTORY_TYPES.PAYMENT &&
        //                 transaction?.action === actionType &&
        //                 transaction?.status === HISTORY_STATUS.FINISHED
        //         )

        //         const _recentDeposit = recentTransaction(HISTORY_ACTIONS.DEPOSIT)
        //         const _recentWithdraw = recentTransaction(HISTORY_ACTIONS.WITHDRAW)
        //         const recentDepositLink = getRecentTransactionLink(_recentDeposit?.method, PAGE_URL.TRANSACTION_LINK.DEPOSIT_LINK)
        //         const recentWithdrawLink = getRecentTransactionLink(_recentWithdraw?.method, PAGE_URL.TRANSACTION_LINK.WITHDRAW_LINK)
        //         recentTransactionRef.value = {
        //             recentDepositLink,
        //             recentWithdrawLink,
        //         }
        //     } catch (error) {}
        // }

        return {
            user,
            balance,
            isLoggedIn,
            refreshId,
            navigateAfterLogin,
            isLoadingVerifyTelegramOtp,
            isLoadingVerifyEmail,
            isLoadingVerifyEmailOtp,
            sendEmailOtpCountdown,
            sendEmailOtpInterval,
            verifyUsername,
            register,
            login,
            loginToken,
            logout,
            refresh,
            startRefresh,
            clearRefresh,
            clearUserData,
            updatePassword,
            updateProfile,

            // verifyEmail
            emailToVerify,
            verifyEmail,
            resendVerifyEmail,
            verifyEmailOtp,
            verifyTelegramOtp,

            // isNewbie,
            // recentTransactionRef,
            // getRecentTransaction,
        }
    },
    {
        persist: {
            storage: persistedState.localStorage,
        },
    }
)
