import { defineStore } from 'pinia'

export const useModalStore = defineStore('modalStore', () => {
    const isShowModalSlide = ref(false) // use for all modals slide
    const isShowModalPopup = ref(false) // use for all modals popup

    const showLoginModal = ref(false)
    const showSignUpModal = ref(false)
    const showForgotPasswordModal = ref(false)

    const showVerifyEmailModal = ref(false)
    const showVerifyEmailOtpModal = ref(false)
    const showEmailVerifiedModal = ref(false)
    const showVerifyTelegramModal = ref(false)

    const showCancelPromoModal = ref(false)
    const showConfirmPromoModal = ref(false)
    const showCancellationReminderModal = ref(false)

    const showUpdateFullnameModal = ref(false)
    const showErrorModal = ref(false)

    const modalFlags = computed(
        () =>
            showLoginModal.value ||
            showSignUpModal.value ||
            showForgotPasswordModal.value ||
            showVerifyEmailModal.value ||
            showVerifyEmailOtpModal.value ||
            showEmailVerifiedModal.value ||
            showVerifyTelegramModal.value ||
            showCancelPromoModal.value ||
            showConfirmPromoModal.value ||
            showCancellationReminderModal.value ||
            showUpdateFullnameModal.value ||
            showErrorModal.value
    )

    watch(
        () => modalFlags.value,
        newVal => {
            isShowModalPopup.value = newVal
        },
        { immediate: true }
    )

    return {
        isShowModalSlide,
        isShowModalPopup,

        showLoginModal,
        showSignUpModal,
        showForgotPasswordModal,

        showVerifyEmailModal,
        showVerifyEmailOtpModal,
        showEmailVerifiedModal,
        showVerifyTelegramModal,

        showCancelPromoModal,
        showConfirmPromoModal,
        showCancellationReminderModal,

        showUpdateFullnameModal,
        showErrorModal,
    }
})
