import { defineStore, storeToRefs } from 'pinia'
import {
    QUAYSO1,
    QUAYSO2,
    QUAYSO3,
    QUAYSO5,
    NUMBERGAME1,
    NUMBERGAME2,
    KENOSIEUTOC,
    KENOVIETLOTT,
    LODE3MIEN,
    LODESIEUTOC,
    ESPORT,
    WS168,
    GA28,
    MEGA645,
    POWER655,
    LODEMD5,
} from '~/constants/apiLink'
import { useDepositStore } from '~/stores'
import useCustomFetch from '~/composables/useCustomFetch'
import { PAGE_URL } from '~/constants/page-urls'

export const useNavBarStore = defineStore('navBarStore', () => {
    const useDepositStoreInstance = useDepositStore()
    const { p2pLink } = storeToRefs(useDepositStoreInstance)

    const fetcher = useCustomFetch()
    const staticUrl = useRuntimeConfig().public.staticUrl
    const mainNavbar = ref([
        {
            id: 'sports',
            url: PAGE_URL.SPORTS,
            title: 'sports',
            i18_key: 'header.nav_menu.sport',
            urlRegex: new RegExp('/(?!e)[a-zA-Z]+(-[a-zA-Z]*)?sports'),
        },
        {
            id: 'casino',
            url: '/casino',
            title: 'Live Casino',
            i18_key: 'header.nav_menu.live_casino',
            label: 'live',
            urlRegex: new RegExp('/casino.*'),
        },
        {
            id: 'quayso',
            url: '/quay-so',
            title: 'quayso_title',
            i18_key: 'header.nav_menu.quayso',
            label: 'hot',
            urlRegex: new RegExp('^/cong-game/quay-so.*$'),
            sub_menu: [
                {
                    id: 'quayso1',
                    url: '/quay-so/quayso1',
                    title: 'quayso1',
                    i18_key: 'header.nav_menu.quayso1',
                    icon: `${staticUrl}/assets/images/icons/quayso1.png`,
                    type: 'link',
                    link: QUAYSO1,
                    loginRequired: false,
                    isCheckFullName: true,
                },
                {
                    id: 'quayso2',
                    url: '/quay-so/quayso2',
                    title: 'quayso2',
                    i18_key: 'header.nav_menu.quayso2',
                    icon: `${staticUrl}/assets/images/icons/quayso2.png`,
                    type: 'link',
                    link: QUAYSO2,
                    loginRequired: false,
                    isCheckFullName: true,
                },
                {
                    id: 'quayso3',
                    url: '/quay-so/quayso3',
                    title: 'quayso3',
                    i18_key: 'header.nav_menu.quayso3',
                    icon: '/assets/images/icons/quayso3.png',
                    type: 'link',
                    link: QUAYSO3,
                    loginRequired: false,
                    isCheckFullName: true,
                },
                {
                    id: 'quayso5',
                    url: '/quay-so/quayso5',
                    title: 'quayso5',
                    i18_key: 'header.nav_menu.quayso5',
                    icon: '/assets/images/icons/quayso5.png',
                    type: 'link',
                    link: QUAYSO5,
                    loginRequired: false,
                    isCheckFullName: true,
                },
                {
                    id: 'numbergame1',
                    url: '/quay-so/numbergame1',
                    title: 'numbergame1',
                    i18_key: 'header.nav_menu.numbergame1',
                    icon: '/assets/images/icons/numbergame1.png',
                    type: 'link',
                    link: NUMBERGAME1,
                    loginRequired: false,
                    isCheckFullName: true,
                },
                {
                    id: 'numbergame2',
                    url: '/quay-so/numbergame2',
                    title: 'numbergame2',
                    i18_key: 'header.nav_menu.numbergame2',
                    icon: '/assets/images/icons/numbergame2.png',
                    type: 'link',
                    link: NUMBERGAME2,
                    loginRequired: false,
                    isCheckFullName: true,
                },
            ],
        },
        {
            id: 'nohu',
            url: '/cong-game/no-hu',
            title: 'nohu',
            i18_key: 'header.nav_menu.nohu',
            urlRegex: new RegExp('^/cong-game/no-hu.*$'),
        },

        {
            id: 'game_cards',
            url: PAGE_URL.GAMES.GAME_CARDS,
            title: 'game_cards',
            urlRegex: new RegExp('^/cong-game/game-bai.*$'),
            i18_key: 'header.nav_menu.game_cards',
        },

        {
            id: 'lode',
            url: '/lo-de',
            title: 'lode_title',
            i18_key: 'header.nav_menu.lode',
            label: 'hot',
            urlRegex: new RegExp('^/lo-de.*'),
            sub_menu: [
                {
                    id: 'lode3mien',
                    url: '/lo-de/lode3mien',
                    title: 'lode_3_mien',
                    i18_key: 'header.nav_menu.lode_3_mien',
                    icon: '/assets/images/icons/lode_3_mien.png',
                    type: '',
                    link: LODE3MIEN,
                    isCheckFullName: true,
                },
                {
                    id: 'lodesieutoc',
                    url: '/lo-de/lode-sieutoc',
                    title: 'lode_sieu_toc',
                    i18_key: 'header.nav_menu.lode_sieu_toc',
                    icon: '/assets/images/icons/lode_sieu_toc.png',
                    type: 'link',
                    link: LODESIEUTOC,
                    isCheckFullName: true,
                },
                {
                    id: 'mega645',
                    url: '/lo-de/mega645',
                    title: 'mega645',
                    i18_key: 'header.nav_menu.mega645',
                    icon: '/assets/images/icons/lode_3_mien.png',
                    type: '',
                    link: MEGA645,
                    loginRequired: true,
                    isCheckFullName: true,
                },
                {
                    id: 'mega655',
                    url: '/lo-de/power655',
                    title: 'power655',
                    i18_key: 'header.nav_menu.power655',
                    icon: '/assets/images/icons/lode_3_mien.png',
                    type: '',
                    link: POWER655,
                    loginRequired: true,
                    isCheckFullName: true,
                },
                {
                    id: 'lodemd5',
                    url: '/lo-de/lodemd5',
                    title: 'lodemd5',
                    i18_key: 'header.nav_menu.lode_md5',
                    icon: '/assets/images/icons/lodemd5.png',
                    type: '',
                    link: LODEMD5,
                    loginRequired: true,
                    isCheckFullName: true,
                },
            ],
        },
        {
            id: 'keno',
            url: '/cong-game/keno',
            title: 'keno_title',
            i18_key: 'header.nav_menu.keno',
            urlRegex: new RegExp('^/cong-game/keno.*$'),
            // sub_menu: [
            //     {
            //         id: 'kenosieutoc',
            //         url: '/keno/kenost',
            //         title: 'keno',
            //         i18_key: 'header.nav_menu.keno_sieu_toc',
            //         icon: '/assets/images/icons/keno_sieu_toc.png',
            //         type: 'link',
            //         link: KENOSIEUTOC,
            //     },
            //     {
            //         id: 'kenovietlott',
            //         url: '/keno/kenovl',
            //         title: 'keno',
            //         i18_key: 'header.nav_menu.keno_vietlott',
            //         type: 'link',
            //         icon: '/assets/images/icons/keno_vietlott.png',
            //         link: KENOVIETLOTT,
            //     },
            // ],
        },
        {
            id: 'banca',
            url: PAGE_URL.GAMES.FISHING,
            title: 'banca_title',
            i18_key: 'header.nav_menu.banca',
            urlRegex: new RegExp('^/cong-game/ban-ca.*$'),
        },
        {
            id: 'esports',
            url: '/esports',
            title: 'esports',
            i18_key: 'header.nav_menu.esports',
            isCheckFullName: true,
        },
        {
            id: 'cockfight',
            url: '',
            title: 'cockfight',
            i18_key: 'header.nav_menu.cockfight',
            is_mobile: false,
            label: 'new',
            isButton: true,
            urlRegex: new RegExp('^/cockfight.*'),
            sub_menu: [
                {
                    id: 'ga28',
                    url: '/cockfight/ga28',
                    title: 'cockfight',
                    i18_key: 'header.nav_menu.cockfight_ga28',
                    type: 'link',
                    link: GA28,
                    loginRequired: true,
                    isCheckFullName: true,
                },
                {
                    id: 'ws168',
                    url: '/cockfight/ws168',
                    title: 'cockfight_ws168',
                    i18_key: 'header.nav_menu.cockfight_ws168',
                    type: 'link',
                    link: WS168,
                    loginRequired: true,
                    isCheckFullName: true,
                },
            ],
        },
        {
            id: 'games',
            url: PAGE_URL.GAMES.INDEX,
            title: 'Games',
            i18_key: 'header.nav_menu.games',
            urlRegex: new RegExp('^/cong-game/(?!(?:no-hu|game-bai|ban-ca|keno|quay-so)).*'),
        },
        {
            id: 'promotion',
            url: '/khuyen-mai',
            title: 'promotion',
            i18_key: 'header.nav_menu.promotion',
            is_mobile: false,
        },
    ])
    const getLink = async link => {
        try {
            const { data } = await fetcher.get(link)
            let url = ''
            if (data.value.status === 'OK') {
                url = data.value.data.url
            }
            return url
        } catch (error) {
            console.error('Error fetching link:', error)
        }
    }

    const getLinkUrl = async link => {
        let urlGetLink = ''
        switch (link) {
            case QUAYSO1:
                urlGetLink = '/gameUrl?p=vingame&gId=quayso'
                break
            case QUAYSO2:
                urlGetLink = '/gameUrl?p=vingame&gId=quayso2'
                break
            case QUAYSO3:
                urlGetLink = '/gameUrl?p=vingame&gId=atom'
                break
            case NUMBERGAME1:
                urlGetLink = '/tp/numberGameUrl'
                break
            case NUMBERGAME2:
                urlGetLink = '/tp/numberGame2Url'
                break
            case KENOSIEUTOC:
                urlGetLink = '/tp/kenoUrl'
                break
            case KENOVIETLOTT:
                urlGetLink = '/tp/kenoVLUrl'
                break
            case LODE3MIEN:
                urlGetLink = '/lodeUrl'
                break
            case LODESIEUTOC:
                urlGetLink = '/lodeVirtualUrl'
                break
            case ESPORT:
                urlGetLink = '/athena/esportsUrl'
                break
            case WS168:
                urlGetLink = '/gameUrl?p=ws168&gId=lobby'
                break
            case GA28:
                urlGetLink = '/gameUrl?p=ga28&gId=lobby'
                break
            default:
                break
        }
        const url = await getLink(urlGetLink)
        return url
    }
    const userMenu = computed(() => {
        const p2pItem = p2pLink.value
            ? [
                  {
                      name: 'P2P',
                      i18_key: 'user_sidebar.p2p',
                      id: 'p2p',
                      url: '/user/p2p',
                      icon: '/assets/images/icons/p2p.svg',
                      icon_active: '/assets/images/icons/p2p_active.svg',
                      label: 'new',
                  },
              ]
            : []
        return [
            {
                name: 'Transaction',
                i18_key: 'user_sidebar.transactions',
                id: 'transaction',
                children: [
                    {
                        name: 'Deposit',
                        i18_key: 'user_sidebar.deposit',
                        id: 'deposit',
                        url: '/user/deposit',
                        icon: `${staticUrl}/assets/images/icons/deposit.svg`,
                        icon_active: `${staticUrl}/assets/images/icons/deposit_active.svg`,
                    },
                    {
                        name: 'Withdraw',
                        i18_key: 'user_sidebar.withdraw',
                        id: 'withdraw',
                        url: '/user/withdraw',
                        icon: `${staticUrl}/assets/images/icons/withdraw.svg`,
                        icon_active: `${staticUrl}/assets/images/icons/withdraw_active.svg`,
                    },
                    ...p2pItem,
                ],
            },
            {
                name: 'Promotions',
                i18_key: 'user_sidebar.promotions',
                id: 'promotions',
                children: [
                    {
                        name: 'General promotions',
                        i18_key: 'user_sidebar.general_promotions',
                        id: 'general_promotions',
                        url: '/user/general-promotions',
                        icon: `${staticUrl}/assets/images/icons/personaldiscount.svg`,
                        icon_active: `${staticUrl}/assets/images/icons/personaldiscount_active.svg`,
                    },
                    {
                        name: 'Giftcode',
                        i18_key: 'user_sidebar.giftcode',
                        id: 'gift_code',
                        url: '/user/gift-code',
                        icon: `${staticUrl}/assets/images/icons/giftcode_unactive.svg`,
                        icon_active: `${staticUrl}/assets/images/icons/giftcode.svg`,
                    },
                ],
            },
            {
                name: 'Infomation',
                i18_key: 'user_sidebar.infomation',
                id: 'infomation',
                children: [
                    {
                        name: 'Profile',
                        i18_key: 'user_sidebar.profile',
                        id: 'profile',
                        url: '/user/profile',
                        icon: `${staticUrl}/assets/images/icons/profile.svg`,
                        icon_active: `${staticUrl}/assets/images/icons/profile_active.svg`,
                    },
                    {
                        name: 'Bank account',
                        i18_key: 'user_sidebar.bank_account',
                        id: 'bank_account',
                        url: '/user/bank-account',
                        icon: `${staticUrl}/assets/images/icons/bank.svg`,
                        icon_active: `${staticUrl}/assets/images/icons/bank_active.svg`,
                    },
                ],
            },
            {
                name: 'History',
                i18_key: 'user_sidebar.history',
                id: 'history',
                children: [
                    {
                        name: 'Bet History',
                        i18_key: 'user_sidebar.bet_history',
                        id: 'bet_history',
                        url: '/user/bet-history',
                        icon: `${staticUrl}/assets/images/icons/bethistory.svg`,
                        icon_active: `${staticUrl}/assets/images/icons/bethistory_active.svg`,
                    },
                    {
                        name: 'Transaction History',
                        i18_key: 'user_sidebar.transaction_history',
                        id: 'transaction_history',
                        url: '/user/transaction-history',
                        icon: `${staticUrl}/assets/images/icons/transactions.svg`,
                        icon_active: `${staticUrl}/assets/images/icons/transactions_active.svg`,
                    },
                ],
            },
        ]
    })
    // user navbar
    const userNavBar = ref([
        {
            name: 'Transaction',
            i18_key: 'user_sidebar.transactions',
            id: 'transaction',
            children: [
                {
                    name: 'Deposit',
                    i18_key: 'user_sidebar.deposit',
                    id: 'deposit',
                    url: '/user/deposit',
                    icon: `${staticUrl}/assets/images/icons/deposit.svg`,
                    icon_active: `${staticUrl}/assets/images/icons/deposit_active.svg`,
                },
                {
                    name: 'Withdraw',
                    i18_key: 'user_sidebar.withdraw',
                    id: 'withdraw',
                    url: '/user/withdraw',
                    icon: `${staticUrl}/assets/images/icons/withdraw.svg`,
                    icon_active: `${staticUrl}/assets/images/icons/withdraw_active.svg`,
                },
            ],
        },
        {
            name: 'History',
            i18_key: 'user_sidebar.history',
            id: 'history',
            children: [
                {
                    name: 'Bet History',
                    i18_key: 'user_sidebar.bet_history',
                    id: 'bet_history',
                    url: '/user/bet-history',
                    icon: `${staticUrl}/assets/images/icons/bethistory.svg`,
                    icon_active: `${staticUrl}/assets/images/icons/bethistory_active.svg`,
                },
                {
                    name: 'Transaction History',
                    i18_key: 'user_sidebar.transaction_history',
                    id: 'transaction_history',
                    url: '/user/transaction-history',
                    icon: `${staticUrl}/assets/images/icons/transactions.svg`,
                    icon_active: `${staticUrl}/assets/images/icons/transactions_active.svg`,
                },
            ],
        },
        {
            name: 'Infomation',
            i18_key: 'user_sidebar.infomation',
            id: 'infomation',
            children: [
                {
                    name: 'Profile',
                    i18_key: 'user_sidebar.profile',
                    id: 'profile',
                    url: '/user/profile',
                    icon: `${staticUrl}/assets/images/icons/profile.svg`,
                    icon_active: `${staticUrl}/assets/images/icons/profile_active.svg`,
                },
                // {
                //     name: 'Notification',
                //     i18_key: 'user_sidebar.notification',
                //     id: 'notification',
                //     url: '/user/notification',
                //     icon: `${staticUrl}/assets/images/icons/notify.svg`,
                //     icon_active: `${staticUrl}/assets/images/icons/notify_active.svg`,
                // },
                {
                    name: 'Bank account',
                    i18_key: 'user_sidebar.bank_account',
                    id: 'bank_account',
                    url: '/user/bank-account',
                    icon: `${staticUrl}/assets/images/icons/bank.svg`,
                    icon_active: `${staticUrl}/assets/images/icons/bank_active.svg`,
                },
                // {
                //     name: 'Agency',
                //     i18_key: 'user_sidebar.agency',
                //     id: 'agency',
                //     url: '/user/agency',
                //     icon: `${staticUrl}/assets/images/icons/agency.svg`,
                //     icon_active: `${staticUrl}/assets/images/icons/agency_active.svg`,
                // },
            ],
        },
        {
            name: 'Promotions',
            i18_key: 'user_sidebar.promotions',
            id: 'promotions',
            children: [
                {
                    name: 'General promotions',
                    i18_key: 'user_sidebar.general_promotions',
                    id: 'general_promotions',
                    url: '/user/general-promotions',
                    icon: `${staticUrl}/assets/images/icons/personaldiscount.svg`,
                    icon_active: `${staticUrl}/assets/images/icons/personaldiscount_active.svg`,
                },
                {
                    name: 'Giftcode',
                    i18_key: 'user_sidebar.giftcode',
                    id: 'giftcode',
                    url: '/user/gift-code',
                    icon: `${staticUrl}/assets/images/icons/giftcode.svg`,
                    icon_active: `${staticUrl}/assets/images/icons/giftcode.svg`,
                },
                // {
                //     name: 'Giftcode',
                //     i18_key: 'user_sidebar.giftcode',
                //     id: 'giftcode',
                //     url: '/user/gift-code',
                //     icon: `${staticUrl}/assets/images/icons/giftcode.svg`,
                //     icon_active: `${staticUrl}/assets/images/icons/giftcode.svg`,
                // },
                // {
                //     name: 'For newbies',
                //     i18_key: 'user_sidebar.for_newbies',
                //     id: 'for_newbies',
                //     url: '/user/newbies',
                //     icon: `${staticUrl}/assets/images/icons/newbiesdis.svg`,
                //     icon_active: `${staticUrl}/assets/images/icons/newbiesdis_active.svg`,
                // },
            ],
        },
    ])
    return {
        getLinkUrl,
        mainNavbar,

        // user navbar
        userNavBar,
        userMenu,
    }
})
