import { defineStore } from 'pinia'
import useCustomFetch from '~/composables/useCustomFetch'
import { useAlertStore } from '~/stores'

export const useDepositStore = defineStore(
    'depositStore',
    () => {
        const fetcher = useCustomFetch()
        const useAlertStoreInstance = useAlertStore()
        const userVerifiedBanks = ref([])
        const plan = ref(null)
        // codePay
        const nicepayStep = ref(1)
        const nicepayQrcode = ref('')
        const nicepayInfo = ref(null)
        const discardingNicepayTime = ref(0)
        const setNicepayQRcode = qrcode => {
            nicepayQrcode.value = qrcode
        }
        const getNicepayQRcode = () => {
            return nicepayQrcode.value || ''
        }
        const setNicepayStep = step => {
            nicepayStep.value = step
        }
        const setDiscardingNicepayTime = nicepayTime => {
            discardingNicepayTime.value = nicepayTime
        }
        const getDiscardingNicepayTime = () => {
            return discardingNicepayTime.value || 0
        }
        const createCodepayDeposit = async payload => {
            try {
                const { data } = await fetcher.post('/payment/nicepay', payload)
                if (data.value?.status === 'OK') {
                    window?.dataLayer.push({ event: 'formSubmitted', formName: 'Form_Deposite' })
                    nicepayInfo.value = data.value?.data?.[0] || null
                    return true
                } else {
                    useAlertStoreInstance.showMessage({
                        type: 'ERROR',
                        message: data.value?.message,
                        timeOut: 3000,
                    })
                }
            } catch (error) {
                useAlertStoreInstance.showMessageError(error)
            }
        }

        // transfer
        const transferBanksList = ref([])
        const getTransferBanksList = async () => {
            try {
                const { data } = await fetcher.get('/payment/indexdeposit')

                if (data.value.status === 'OK') {
                    transferBanksList.value = data.value.data.banks.sort((a, b) => {
                        return b.status - a.status
                    })
                    userVerifiedBanks.value = data.value.data.userBanks
                }
            } catch (error) {
                console.log(error)
            }
        }
        const createTransferDepost = async payload => {
            return await fetcher.post('/payment/depositbank', payload)
        }

        // paywin
        const paywinBanksList = ref([])
        const getPaywinBanksList = async () => {
            try {
                const { data } = await fetcher.get('/payment/indexdeposit')

                if (data.value.status === 'OK') {
                    paywinBanksList.value = data.value.data.autoBanks
                }
            } catch (error) {
                console.log(error)
            }
        }
        const createPaywinDeposit = async payload => {
            return await fetcher.post('/payment/smartpay', payload)
        }

        // phonecard
        const depositPhonecardList = ref([])
        const depositPhonecardStatus = ref(1)
        const getDepositPhonecardList = async () => {
            try {
                const { data } = await fetcher.get('/payment/gwinfo')
                depositPhonecardStatus.value = data.value.status
                if (data.value.status === 1) {
                    depositPhonecardList.value = data.value.cardlist
                }
            } catch (error) {
                console.log(error)
            }
        }

        const createPhonecardDeposit = async payload => {
            return await fetcher.post('/payment/depositcard', payload)
        }

        // momo
        const momoAccounts = ref({})
        const momoRemark = ref('')

        const getMomoRemark = async () => {
            try {
                const { data } = await fetcher.get('/user/momo/code')

                if (data.value.status === 'OK') {
                    momoRemark.value = data.value.data.code
                }
            } catch (error) {
                console.log(error)
            }
        }

        const currencyList = ref({})
        const getCurrencyList = async () => {
            try {
                const { data } = await fetcher.get('payment/crypto/deposit/currency')
                if (data.value.status === 'OK') {
                    currencyList.value = data.value.data
                }
            } catch (error) {
                console.log(error)
            }
        }
        const cryptoAccount = ref({})
        const getCryptoAccount = async queryParams => {
            try {
                const [address] = await Promise.all([
                    fetcher.get('/payment/crypto/address', queryParams),
                ])
                if (address.data.value.status === 'OK') {
                    const wallet = address.data.value.data[0]
                    cryptoAccount.value = {
                        network: wallet.network,
                        address: wallet.address,
                        qr_code: wallet.qrcode,
                    }
                }
            } catch (error) {
                console.log(error)
            }
        }

        // packages
        const packages = ref([])
        // Recommend
        const recommendMethods = ref([])
        const ewalletTypes = ref({
            viettelPays: [],
            momos: [],
        })

        // crypto
        const depositCryptos = ref([])
        const getCryptoDeposit = async () => {
            try {
                const { data } = await fetcher.get('payment/crypto/deposit')
                if (data.value.status === 'OK') {
                    depositCryptos.value = data.value.data
                }
            } catch (error) {
                console.log(error)
            }
        }

        // ewallet
        const ewalletSonicData = ref(null)
        const ewalletSonicDataList = ref({
            MOMO: null,
            VIETTEL_PAY: null,
            ZALO_PAY: null,
        })
        const createSonicEWalletDeposit = async payload => {
            try {
                const { data } = await fetcher.post('/payment/ewallet', payload)
                if (data.value?.data?.[0]?.account_no) {
                    ewalletSonicData.value = {
                        ...(data.value?.data?.[0] || {}),
                        amount: payload.amount,
                    }
                } else {
                    ewalletSonicData.value = null
                }
                ewalletSonicDataList.value = {
                    ...ewalletSonicDataList.value,
                    [payload.wallet]: ewalletSonicData.value,
                }
                return data
            } catch (error) {
                console.log(error)
            }
        }

        const checkSonicEWalletDeposit = async payload => {
            try {
                const { data } = await fetcher.get('/payment/ewallet', payload)
                return data
            } catch (error) {
                console.log(error)
            }
        }

        const turboAccounts = ref([])
        const getTurboAccounts = async method => {
            try {
                const { data } = await fetcher.get(`/payment/${method}`)
                if (data.value.status === 'OK') {
                    turboAccounts.value = data.value?.data || []
                } else {
                    turboAccounts.value = []
                }
            } catch (error) {
                turboAccounts.value = []
                console.log(error)
            }
        }

        const momoCode = ref('')
        const getMomoCode = async () => {
            try {
                const { data } = await fetcher.get('/payment/ewalletCode')
                if (data.value.status === 'OK') {
                    momoCode.value = data.value?.data || ''
                }
            } catch (error) {
                console.log(error)
            }
        }

        const nicepayInfoStatus = ref(1)
        const p2pLink = ref('')
        const initialData = async () => {
            const { data } = await fetcher.get('/payment/indexdeposit')
            if (data.value?.status === 'OK') {
                momoAccounts.value = data.value.data?.momos
                recommendMethods.value = data.value.data?.recommendDeposit
                packages.value = data.value.data?.packages.map(item => ({
                    ...item,
                    label: item.name,
                    value: item.id,
                }))

                nicepayInfoStatus.value = data.value.data?.nicepayInfo?.status
                p2pLink.value = data?.value?.data?.p2pLink || ''

                ewalletTypes.value = {
                    viettelPays: data.value.data?.viettelPays || [],
                    momos: data.value.data?.momos || [],
                }
            }
        }

        const getWelcomeCommission = async () => {
            try {
                const { data } = await fetcher.get('/account/info')
                if (data.value?.status === 'OK') {
                    plan.value =
                        data.value?.data && Object.values(data.value?.data || {})?.length
                            ? data?.value?.data
                            : null
                }
            } catch (error) {
                console.log(error)
            }
        }

        return {
            userVerifiedBanks,
            // codepay
            nicepayStep,
            nicepayInfo,
            nicepayInfoStatus,
            nicepayQrcode,
            getNicepayQRcode,
            setNicepayQRcode,
            setNicepayStep,
            discardingNicepayTime,
            setDiscardingNicepayTime,
            getDiscardingNicepayTime,
            createCodepayDeposit,

            // transfer
            transferBanksList,
            getTransferBanksList,
            createTransferDepost,

            // pawWin
            paywinBanksList,
            getPaywinBanksList,
            createPaywinDeposit,

            // phonecard
            depositPhonecardList,
            depositPhonecardStatus,
            getDepositPhonecardList,
            createPhonecardDeposit,

            // momo
            momoAccounts,
            momoRemark,
            getMomoRemark,

            // ewallet
            createSonicEWalletDeposit,
            ewalletSonicData,
            ewalletTypes,
            checkSonicEWalletDeposit,
            turboAccounts,
            getTurboAccounts,
            getMomoCode,
            momoCode,
            ewalletSonicDataList,

            // crypto
            currencyList,
            getCurrencyList,
            cryptoAccount,
            getCryptoAccount,
            depositCryptos,
            getCryptoDeposit,

            // packages
            packages,

            // Recommend
            recommendMethods,
            initialData,

            // P2P,
            p2pLink,

            plan,
            getWelcomeCommission,
        }
    },
    {
        persist: {
            storage: persistedState.localStorage,
        },
    }
)
