<template>
    <CommonPageWrapper class="page-promotion-detail">
        <div class="overflow-hidden bg-slate-900 max-lg:pt-1 lg:rounded-2xl lg:px-12 lg:py-5">
            <CommonSpinner v-if="!post.content" />
            <div v-else class="promotion-content">
                <CommonImage
                    :src="post.image"
                    :srcMb="post.imageMb"
                    class="rounded-lg xl:rounded-[10px]"
                />
                <div>
                    <div
                        class="py-6 text-center text-[18px] font-bold leading-[26px] text-white xl:py-8 xl:text-[22px] xl:leading-[28px]"
                    >
                        <h1>{{ post.title }}</h1>
                    </div>

                    <div class="promotion-content__content" v-html="post.content || null"></div>
                    <div class="flex justify-center">
                        <NuxtLink
                            role="button"
                            class="btn-register cursor-pointer text-center uppercase text-white"
                            @click="clickButton"
                        >
                            <span class="btn-link-inner flex flex-col gap-1">
                                <span class="text-xl font-bold">
                                    <template v-if="isLoggedIn">
                                        {{ $t('promotion_detail.btn_deposit.title') }}
                                    </template>
                                    <template v-else>
                                        {{ $t('promotion_detail.btn_register.title') }}
                                    </template>
                                </span>
                                <span class="text-sm">
                                    {{ $t('promotion_detail.btn_register.desc') }}
                                </span>
                            </span>
                        </NuxtLink>
                    </div>
                </div>
            </div>
        </div>
    </CommonPageWrapper>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore, useUserStore } from '~/stores'
import { PAGE_URL } from '~/constants/page-urls'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const { BRAND_NAME: brandName } = useRuntimeConfig().public
const post = reactive({
    title: '',
    content: '',
    image: '',
})
const { locale } = useI18n()

const useUserStoreInstance = useUserStore()
const useModalStoreInstance = useModalStore()
const { isLoggedIn } = storeToRefs(useUserStoreInstance)
const { showSignUpModal } = storeToRefs(useModalStoreInstance)

const clickButton = () => {
    if (isLoggedIn.value) {
        router.push(PAGE_URL.DEPOSIT_LINK.INDEX)
    } else {
        showSignUpModal.value = true
    }
}
const { default: postData } = await import(
    `~/resources/promotion/${route.params.slug}-${locale.value}.ts`
)
Object.assign(post, postData(t, brandName))
</script>
<style lang="scss" scoped>
.promotion-content {
    &:deep(.promotion-content__content) {
        @apply text-[14px] leading-[18px] text-white;
        p {
            @apply mb-3;
        }
        ul,
        ol {
            @apply mb-8 list-disc pl-6;
            li {
                @apply mb-3;
            }
        }
        ul {
            @apply list-disc;
            ul {
                list-style-type: circle;
            }
        }
        ol {
            @apply list-decimal;
        }
        table {
            @apply mb-8 w-full table-auto border-collapse border border-slate-600;
            th {
                @apply bg-slate-800 text-left font-semibold text-white;
            }
            th,
            td {
                @apply border border-slate-600 px-4 py-3;
            }
        }
        br {
            margin-bottom: 24px;
        }
        @media (max-width: 1199px) {
            ul,
            ol {
                @apply mb-6;
            }
            table {
                @apply mb-6;
                th,
                td {
                    @apply px-[10px] py-3;
                }
            }
        }
    }
}

.btn-register {
    @apply relative rounded-full bg-rose-600 max-lg:px-8 max-lg:py-[10px] lg:px-11 lg:py-4;
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-radius: 45px;
    }
    @media screen and (max-width: 1023px) {
        padding: 10px 32px;
    }
}
</style>
