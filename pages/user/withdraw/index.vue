<template>
    <CommonBoxWrapper class-wrapper="max-lg:p-0 max-lg:!p-0 max-lg:rounded-none">
        <template v-if="isLoading && !showMobile">
            <CommonSpinner />
        </template>
        <template v-else>
            <CommonTabs
                :tabs="withdrawTabs"
                class-nav-wrapper="max-lg:!justify-around max-lg:!px-3"
                class-tab-wrapper="max-lg:!pb-3"
                class-content-wrapper="max-lg:px-3"
            />
        </template>
    </CommonBoxWrapper>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { nextTick } from 'vue'
import { useWithdrawStore, useDepositStore, useUserStore } from '~/stores'
import { useCommon } from '~/composables/use-common'
import { PROMOTION_PLAN_TYPE } from '~/constants/user'
const { showPreventWithdrawModal } = useNotify()

const useWithdrawStoreInstances = useWithdrawStore()
const { getCryptoCurrencyList, getWithdrawBanksList } = useWithdrawStoreInstances
const { currencyList, withdrawBanksList } = storeToRefs(useWithdrawStoreInstances)
const useDepositStoreInstance = useDepositStore()
const { p2pLink } = storeToRefs(useDepositStoreInstance)

const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)

const isLoading = ref(true)

const { showMobile } = useCommon()
const route = useRoute()

const withdrawTabs = computed(() => {
    const tabs = [
        {
            id: 1,
            key: 'bank',
            link: '/user/withdraw?tab=bank',
            title: 'user.withdraw.tabs.bank',
            content: resolveComponent('UserWithdrawBanks'),
            classNavInner: 'col-span-8 col-start-3',
            classContentInner: 'col-span-8 col-start-3',
            isMaintenance: getMaintenance('codepay'),
        },
        {
            id: 5,
            key: 'p2p',
            link: '/user/withdraw?tab=p2p',
            title: 'user_sidebar.p2p',
            titleMobile: 'user_sidebar.p2p',
            content: resolveComponent('UserP2p'),
            classNavInner: 'col-span-8 col-start-3',
            classContentInner: 'col-span-12 col-start-1',
            isMaintenance: getMaintenance('p2p'),
        },
        {
            id: 2,
            key: 'phonecard',
            link: '/user/withdraw?tab=phonecard',
            title: 'user.withdraw.tabs.phonecard',
            content: resolveComponent('UserWithdrawPhonecard'),
            classNavInner: 'col-span-8 col-start-3',
            classContentInner: 'col-span-8 col-start-3',
            isMaintenance: getMaintenance('phonecard'),
        },
        {
            id: 3,
            key: 'crypto',
            link: '/user/withdraw?tab=crypto',
            title: 'user.withdraw.tabs.crypto',
            content: resolveComponent('UserWithdrawCrypto'),
            classNavInner: 'col-span-8 col-start-3',
            classContentInner: 'col-span-8 col-start-3',
            isMaintenance: getMaintenance('crypto'),
        },
    ]

    return showMobile.value
        ? tabs.sort((a, b) => {
              if (a.isMaintenance === b.isMaintenance) return 0
              return a.isMaintenance ? 1 : -1
          })
        : tabs
})

const getMaintenance = key => {
    if (!key) {
        return false
    }
    switch (key) {
        case 'p2p':
            return !p2pLink.value
        case 'crypto':
            return !currencyList.value?.length
        case 'bank':
            return (
                !withdrawBanksList.value?.length ||
                withdrawBanksList.value.every(item => !item.status)
            )
        default:
            return false
    }
}

watchEffect(async () => {
    if (route.name !== 'user-withdraw') return
    await nextTick(async () => {
        try {
            if (user.value?.package_id === PROMOTION_PLAN_TYPE.WELCOME) {
                showPreventWithdrawModal()
            }
        } catch (error) {
            console.error(error)
        }
    })
})

onMounted(async () => {
    await nextTick(async () => {
        await Promise.all([getCryptoCurrencyList(), getWithdrawBanksList()])
        isLoading.value = false
    })
})
</script>
