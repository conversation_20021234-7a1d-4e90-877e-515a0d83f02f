<template>
    <div>
        <CommonGameSkeleton v-if="isFirstLoad" />
        <template v-else>
            <div class="grid grid-cols-2 gap-x-[6px] gap-y-3 md:grid-cols-5 md:gap-x-2 md:gap-y-4">
                <CommonCasinoCard
                    v-for="game in casinos"
                    :key="`${game.name}-${game.api_url}`"
                    :data="game"
                    :jackpotvalue="findJackpot(game)"
                    show-favorite
                    favoriteColor="fill-white"
                    titleClass="text-sm font-normal capitalize text-white xl:text-base"
                    providerClass="text-[10px] text-slate-300 max-xl:font-medium lg:text-sm"
                    buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                    @click="handleShowGame(game)"
                />
            </div>
            <CommonGameLoadMore />

            <BlocksNoDataGames v-if="!casinos?.length" />
        </template>
    </div>
</template>
<script setup>
import { useCasinoStore } from '~/stores'

const casinoStoreInstance = useCasinoStore()
const { casinos, isFirstLoad } = storeToRefs(casinoStoreInstance)

const { handleShowGame } = useCasino()
const { findJackpot } = useJackpot()
</script>
