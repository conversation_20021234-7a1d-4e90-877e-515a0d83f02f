import { storeToRefs } from 'pinia'
import { MODAL_TYPE } from '~/constants'
import { PAGE_URL } from '~/constants/page-urls'
import { useModalStore, useShowErrorStore, useUserStore } from '~/stores'

export const useNotify = () => {
    const onClickLiveChat = () => window.LiveChatWidget.call('maximize')

    const useShowErrorStoreInstance = useShowErrorStore()
    const { onShowErrorModal } = useShowErrorStoreInstance
    const brandName = useRuntimeConfig().public.BRAND_NAME
    const useModalStoreInstance = useModalStore()
    const { showLoginModal, showVerifyEmailOtpModal, showVerifyTelegramModal, showSignUpModal } =
        storeToRefs(useModalStoreInstance)
    const useUserStoreInstances = useUserStore()
    const { logout } = useUserStoreInstances

    const t = (message = '', params: Record<string, unknown> | undefined = undefined) => {
        if (params) {
            return {
                message,
                params,
            }
        }
        return message
    }

    const showGameDenyModal = () => {
        onShowErrorModal({
            title: t('error_modal.game_deny.title'),
            message: t('error_modal.game_deny.message'),
            cancelButton: t('common.home'),
            confirmButton: t('error_modal.game_deny.confirm'),
            icon: '/assets/images/v2/icons/ic-game-deny.webp',
            onCancel: () => {
                navigateTo('/')
            },
            onConfirm: () => {
                navigateTo('/user/general-promotions')
            },
            clickOutSide: true,
        })
    }

    const showGameMaintenanceModal = (closeAction = () => navigateTo(PAGE_URL.GAMES.INDEX)) => {
        onShowErrorModal({
            title: t('error_modal.game_maintenance.title'),
            message: t('error_modal.game_maintenance.message'),
            cancelButton: t('common.home'),
            confirmButton: t('error_modal.game_maintenance.confirm'),
            icon: '/assets/images/v2/icons/ic-game-maintenance.webp',
            onCancel: () => {
                navigateTo('/')
            },
            onConfirm: () => {
                closeAction()
            },
            clickOutSide: true,
        })
    }

    const showLoginFailedModal = (message = '') => {
        onShowErrorModal({
            title: t('error_modal.login.error.title'),
            message: message || t('error_modal.login.error.message'),
            confirmButton: t('error_modal.login.error.confirm'),
            icon: '/assets/images/v2/icons/login-error.webp',
            onConfirm: () => {
                navigateTo('/')
            },
            clickOutSide: true,
        })
    }

    const showLoggedExpiredModal = (message = '') => {
        onShowErrorModal({
            title: t('error_modal.login.logged_expired.title'),
            message: message || t('error_modal.login.logged_expired.message'),
            confirmButton: t('error_modal.login.logged_expired.confirm'),
            icon: '/assets/images/v2/icons/login-warning.webp',
            onConfirm: () => {
                showLoginModal.value = true
            },
            clickOutSide: true,
        })
    }

    const showLogoutWarningModal = () => {
        onShowErrorModal({
            title: t('error_modal.logout.title'),
            message: t('error_modal.logout.message'),
            cancelButton: t('error_modal.logout.confirm'),
            btnCancelButton:
                'btn-outline lg:max-w-[182px] max-w-[162px] h-[46px] max-lg:text-[16px]',
            confirmButton: t('error_modal.logout.cancel'),
            btnConfirmButton:
                'btn-primary lg:max-w-[182px] max-w-[162px] h-[46px] max-lg:text-[16px]',
            classContentWrapper: 'lg:pt-8 pt-5',
            customClass: 'lg:h-[324px] h-[296px] [&_.modal-message]:mt-2',
            icon: '/assets/images/v2/icons/logout.webp',
            onCancel: () => {
                logout()
            },
            onConfirm: () => {
                return false
            },
        })
    }

    const showMessage = (
        { message = '', type = MODAL_TYPE.ERROR, callback = () => {} },
        options = undefined
    ) => {
        onShowErrorModal({
            type,
            title:
                options?.title ||
                (type === MODAL_TYPE.ERROR
                    ? t('error_modal.system.title')
                    : t('modal.common.success.title')),
            message:
                message ||
                (type === MODAL_TYPE.ERROR
                    ? t('error_modal.system.message')
                    : t('modal.common.success.message')),
            icon: options?.icon,
            cancelButton:
                type.toLowerCase() === MODAL_TYPE.ERROR ? t('error_modal.system.cancel') : null,
            confirmButton:
                type.toLowerCase() === MODAL_TYPE.ERROR
                    ? t('error_modal.system.confirm_cskh')
                    : t('error_modal.system.confirm'),
            onCancel: () => {
                navigateTo('/')
                callback()

                if (options?.onCancel) {
                    options.onCancel()
                }
            },
            onConfirm: () => {
                if (type.toLowerCase() === MODAL_TYPE.ERROR) {
                    onClickLiveChat()
                }
                callback()

                if (options?.onConfirm) {
                    options.onConfirm()
                }
            },
            clickOutSide: true,
        })
    }

    const showMessageError = (payload, option = undefined) => {
        const reg500 = '50[0-9]'
        const reg400 = '40[1-9]'
        const globalRegex500 = new RegExp(reg500, 'g')
        const globalRegex400 = new RegExp(reg400, 'g')
        let message = t('error_modal.system.message')
        if (globalRegex500.test(payload?.value?.statusCode)) {
            message = t('error.big_is_busy')
        } else if (globalRegex400.test(payload?.value?.statusCode)) {
            message = t('error.big_is_busy')
        } else if (payload?.value?.data?.data?.message) {
            message = payload?.value?.data?.data?.message
        } else if (payload?.value?.message) {
            message = payload?.value?.message
        } else if (payload.message) {
            message = payload.message
        } else if (payload && typeof payload === 'string') {
            message = payload
        }
        showMessage({ message, type: MODAL_TYPE.ERROR }, option)
    }

    const showMessageSuccess = (message = t('error_modal.system_success.message')) => {
        showMessage({
            message,
            type: MODAL_TYPE.SUCCESS,
        })
    }

    const showDepositPlay = () => {
        onShowErrorModal({
            title: t('error_modal.user.deposit_play.title'),
            message: t('error_modal.user.deposit_play.message', { brandName }),
            cancelButton: t('error_modal.user.deposit_play.cancel'),
            confirmButton: t('error_modal.user.deposit_play.confirm'),
            icon: '/assets/images/v2/icons/deposit-error.webp',
            onCancel: () => {
                navigateTo('/')
            },
            onConfirm: () => {
                navigateTo('/user/deposit')
            },
            clickOutSide: true,
        })
    }

    const showAddBankSuccessModal = () => {
        onShowErrorModal({
            title: t('error_modal.user.addbank_success.title'),
            message: t('error_modal.user.addbank_success.message'),
            confirmButton: t('error_modal.user.addbank_success.confirm_livechat'),
            icon: '/assets/images/v2/icons/bank-success.webp',
            onConfirm: () => {
                onClickLiveChat()
            },
            clickOutSide: true,
        })
    }

    const showWithdrawSuccessModal = () => {
        onShowErrorModal({
            title: t('error_modal.withdraw.success.title'),
            message: t('error_modal.withdraw.success.message'),
            confirmButton: t('error_modal.withdraw.success.confirm'),
            icon: '/assets/images/v2/icons/withdraw-success.webp',
            onClose: () => {
                navigateTo('/user/transaction-history')
            },
            clickOutSide: true,
        })
    }

    const showWithdrawFailedModal = (message = '') => {
        onShowErrorModal({
            title: t('error_modal.withdraw.error.title'),
            message: message || t('error_modal.withdraw.error.message'),
            confirmButton: t('error_modal.withdraw.error.confirm'),
            icon: '/assets/images/v2/icons/withdraw-error.webp',
            clickOutSide: true,
        })
    }

    const showDepositSuccessModal = (amount = 0) => {
        onShowErrorModal({
            title: t('error_modal.deposit.success.title'),
            message: amount
                ? t('error_modal.deposit.success.message', {
                      amount,
                  })
                : t('error_modal.deposit.success.message'),
            confirmButton: t('error_modal.deposit.success.confirm'),
            cancelButton: t('error_modal.deposit.success.cancel'),
            icon: '/assets/images/v2/icons/deposit-success.webp',
            onConfirm: () => {
                navigateTo('/user/transaction-history')
            },
            onCancel: () => {
                navigateTo('/')
            },
            clickOutSide: true,
        })
    }

    const showDepositFailedModal = (message = '') => {
        onShowErrorModal({
            title: t('error_modal.deposit.error.title'),
            message: message || t('error_modal.deposit.error.message'),
            confirmButton: t('error_modal.deposit.error.confirm'),
            cancelButton: t('error_modal.deposit.error.cancel'),
            icon: '/assets/images/v2/icons/deposit-error.webp',
            onCancel: () => {
                onClickLiveChat()
            },
            clickOutSide: true,
        })
    }

    const showCancelPromotionFailedModal = (message = '') => {
        onShowErrorModal({
            title: t('error_modal.user.cancel_promo_failed.title'),
            message: message || t('error_modal.user.cancel_promo_failed.message'),
            cancelButton: t('error_modal.user.cancel_promo_failed.cancel'),
            confirmButton: t('error_modal.user.cancel_promo_failed.confirm'),
            icon: '/assets/images/v2/icons/promotion-error.webp',
            onCancel: () => {
                navigateTo('/')
            },
            onConfirm: () => {
                navigateTo('/promo/khuyen-mai-100-lan-nap-dau-tien')
            },
            clickOutSide: true,
        })
    }

    const showEmailOtpFailedModal = () => {
        onShowErrorModal({
            title: t('error_modal.sendmailOtp.error.title'),
            message: t('error_modal.sendmailOtp.error.message'),
            cancelButton: t('error_modal.sendmailOtp.error.cancel'),
            confirmButton: t('error_modal.sendmailOtp.error.confirm'),
            icon: '/assets/images/v2/icons/email-error.webp',
            onCancel: () => {},
            onConfirm: () => {
                showVerifyEmailOtpModal.value = true
            },
            clickOutSide: true,
        })
    }

    const showTelegramOtpFailedModal = () => {
        onShowErrorModal({
            title: t('error_modal.sendtelegramOtp.error.title'),
            message: t('error_modal.sendtelegramOtp.error.message'),
            cancelButton: t('error_modal.sendtelegramOtp.error.cancel'),
            confirmButton: t('error_modal.sendtelegramOtp.error.confirm'),
            icon: '/assets/images/v2/icons/tele-error.webp',
            onCancel: () => {},
            onConfirm: () => {
                showVerifyTelegramModal.value = true
            },
            clickOutSide: true,
        })
    }

    const showPreventWithdrawModal = () => {
        onShowErrorModal({
            title: t('modal.promotion.prevent_withdraw.title'),
            message: t('modal.promotion.prevent_withdraw.content'),
            cancelButton: t('common.close'),
            confirmButton: t('modal.promotion.prevent_withdraw.confirm'),
            icon: '/assets/images/v2/icons/withdraw-warning.webp',
            onCancel: () => {
                return false
            },
            onConfirm: () => {
                navigateTo('/user/general-promotions')
            },
        })
    }

    const showChangePasswordFailedModal = (message = '') => {
        onShowErrorModal({
            title: t('error_modal.changepass.error.title'),
            message: t('error_modal.changepass.error.message'),
            cancelButton: t('error_modal.changepass.error.cancel'),
            confirmButton: t('error_modal.changepass.error.confirm'),
            icon: '/assets/images/modal/ic-change-password-error.svg',
            onConfirm: () => {
                navigateTo('/')
            },
            onCancel: () => {
                navigateTo('/')
            },
        })
    }

    const showNotiSignupModal = () => {
        onShowErrorModal({
          title: t('modal.noti_signup.title'),
          message: t('modal.noti_signup.content', { brandName }),
          cancelButton: t('modal.noti_signup.cancel'),
          btnCancelButton: 'text-white bg-[#162943] border border-solid border-[#283B55] hover:opacity-80',
          confirmButton: t('modal.noti_signup.confirm'),
          icon: '/assets/images/v2/icons/noti-signup.webp',
          onConfirm: () => {
            showSignUpModal.value = true
          },
          onCancel: () => {
            navigateTo('/');
            showSignUpModal.value = false
          },
        });
    };

    return {
        showGameDenyModal,
        showGameMaintenanceModal,
        showLoginFailedModal,
        showLoggedExpiredModal,
        showLogoutWarningModal,
        showMessage,
        showMessageError,
        showMessageSuccess,
        showAddBankSuccessModal,
        showWithdrawSuccessModal,
        showWithdrawFailedModal,
        showDepositSuccessModal,
        showDepositFailedModal,
        showDepositPlay,
        showCancelPromotionFailedModal,
        showEmailOtpFailedModal,
        showTelegramOtpFailedModal,
        showPreventWithdrawModal,
        showChangePasswordFailedModal,
        showNotiSignupModal
    }
}
