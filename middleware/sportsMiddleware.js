import { useModalStore } from '~/stores'

export default defineNuxtRouteMiddleware(() => {
    // Create a reactive state and set default value
    const openSportIframe = useState('openSportIframe', () => ref(false))
    const useModalStoreInstance = useModalStore()

    if (process.client) {
        const userStoreFromStorage = localStorage.getItem('userStore')
        const userStore = JSON.parse(userStoreFromStorage)
        const user = userStore?.user

        if (user && user?.is_updated_fullname === 0) {
            openSportIframe.value = false
            useModalStoreInstance.showUpdateFullnameModal = true
            return navigateTo('/')
        }

        openSportIframe.value = true
    }
})
