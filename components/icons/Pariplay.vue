<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M4.3976 7.29297C4.3976 6.84825 4.36984 6.40237 4.40557 5.95937C4.44012 5.53275 4.58275 5.12451 4.79655 4.74471C5.21352 4.0081 5.82204 3.48217 6.62212 3.16691C7.26048 2.91051 7.96253 2.84354 8.63993 2.97443C9.23054 3.08475 9.73639 3.36055 10.2272 3.67886C11.3493 4.40571 12.4715 5.12709 13.5936 5.85221C14.8788 6.68277 16.1663 7.50988 17.4456 8.34876C17.8451 8.61049 18.2473 8.87767 18.6029 9.19139C19.1427 9.668 19.4693 10.2831 19.5721 10.9852C19.627 11.3587 19.6639 11.7514 19.6122 12.1226C19.4891 13.0066 19.0824 13.7731 18.3424 14.3143C17.6632 14.8108 16.9403 15.2486 16.2337 15.7086C15.2361 16.3578 14.2372 17.0054 13.2369 17.6512C12.18 18.335 11.1284 19.0262 10.0612 19.6938C9.72707 19.9081 9.36678 20.0811 8.98868 20.2087C8.55951 20.3479 8.10427 20.3941 7.6548 20.3443C7.16459 20.2894 6.70303 20.1699 6.27307 19.9447C5.47575 19.5272 4.90847 18.9104 4.59398 18.0773C4.4292 17.6432 4.36748 17.1927 4.36836 16.7316C4.36836 16.1521 4.36069 15.5727 4.37102 14.9935C4.38136 14.4046 4.61081 13.9139 5.10751 13.557C6.00464 12.9118 7.3022 13.2313 7.77351 14.223C7.88956 14.4666 7.93386 14.7186 7.94478 14.9837C7.96309 15.425 8.31598 15.7172 8.7421 15.6235C8.90629 15.5873 9.05955 15.4819 9.20455 15.3885C10.1576 14.7756 11.1085 14.1599 12.0572 13.5412C12.4792 13.2675 12.9088 13.0043 13.3217 12.7182C14.0995 12.1786 14.0753 11.0367 13.2794 10.5198C12.0037 9.69146 10.7265 8.8656 9.44788 8.04223C9.31322 7.95604 9.17443 7.87359 9.0442 7.78022C8.81859 7.6179 8.57171 7.58314 8.32277 7.69403C8.06615 7.80895 7.94715 8.02872 7.94124 8.3051C7.93002 8.85267 7.70145 9.30084 7.26204 9.63755C7.0392 9.80968 6.77714 9.92722 6.49788 9.9803C6.21862 10.0334 5.93035 10.0204 5.65731 9.94256C5.38426 9.86469 5.13443 9.72416 4.92884 9.5328C4.72324 9.34144 4.56789 9.10484 4.47585 8.8429C4.40853 8.65415 4.38372 8.44328 4.37309 8.24074C4.35626 7.92472 4.36866 7.60871 4.36866 7.29269L4.3976 7.29297Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 20,
    },
    h: {
        type: Number,
        default: 20,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
