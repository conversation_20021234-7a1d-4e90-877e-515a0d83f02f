<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M84.2081 32.3545C82.9606 25.5148 77.0399 20.5639 70.1489 20.5639H60.4263C59.7729 20.5639 59.1194 20.8025 58.6244 21.2399L53.0403 26.0914H40.8988L35.9682 21.7966L35.3147 21.22C34.8197 20.7826 34.1662 20.544 33.5128 20.544H23.7902C16.8992 20.544 10.9786 25.4949 9.73106 32.3346L4.44403 61.2048C2.8203 70.013 8.50336 78.4832 17.2161 80.2925C23.3348 81.5651 29.5129 79.2586 33.3544 74.3078L35.9682 70.9475L39.4137 66.5335H47.8721C47.8523 66.0762 47.8325 65.599 47.8325 65.1218C47.8325 50.7463 59.4759 39.075 73.7726 39.075C78.1884 39.075 82.3665 40.1884 86.01 42.1569L84.2081 32.3545ZM33.79 47.6645H29.8099V51.661C29.8099 53.192 28.5822 54.4247 27.0575 54.4247C25.5327 54.4247 24.305 53.192 24.305 51.661V47.6645H20.3249C18.8002 47.6645 17.5725 46.4317 17.5725 44.9007C17.5725 43.3697 18.8002 42.137 20.3249 42.137H24.305V38.1405C24.305 36.6095 25.5327 35.3768 27.0575 35.3768C28.5822 35.3768 29.8099 36.6095 29.8099 38.1405V42.137H33.79C35.3147 42.137 36.5424 43.3697 36.5424 44.9007C36.5424 46.4118 35.3147 47.6645 33.79 47.6645Z"
                :class="fillColor"
            />
            <path
                d="M31.295 17.3229L32.4039 18.5557C32.6811 18.8738 32.4633 19.3709 32.0475 19.3709H23.7902C20.8398 19.3709 18.0873 20.2656 15.7904 21.8364C15.3547 22.1346 14.8201 21.6177 15.1171 21.1803L17.2755 17.7803C17.6715 17.1639 18.325 16.806 19.0378 16.7861L29.8693 16.6668C30.4039 16.6867 30.9386 16.9054 31.295 17.3229Z"
                :class="fillColor"
            />
            <path
                d="M62.7431 17.3229L61.6342 18.5557C61.357 18.8738 61.5748 19.3709 61.9907 19.3709H70.2479C73.1984 19.3709 75.9508 20.2656 78.2478 21.8364C78.6834 22.1346 79.2181 21.6177 78.921 21.1803L76.7627 17.7803C76.3666 17.1639 75.7132 16.806 75.0003 16.7861L64.1688 16.6668C63.6342 16.6867 63.1194 16.9054 62.7431 17.3229Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M73.535 43.1709C61.4758 43.1709 51.6938 52.9931 51.6938 65.1019C51.6938 77.2107 61.4758 87.0329 73.535 87.0329C85.5942 87.0329 95.3762 77.2107 95.3762 65.1019C95.3762 52.9931 85.5942 43.1709 73.535 43.1709ZM60.5253 56.4727L82.1289 78.1651C79.6735 79.8153 76.7231 80.7697 73.535 80.7697C64.9213 80.7697 57.9313 73.751 57.9313 65.1019C57.9313 61.9206 58.8818 58.9382 60.5253 56.4727ZM86.5447 73.7311L64.9411 52.0387C67.3965 50.3885 70.3469 49.4341 73.535 49.4341C82.1487 49.4341 89.1387 56.4528 89.1387 65.1019C89.1387 68.3031 88.1882 71.2656 86.5447 73.7311Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-slate-600',
    },
})
</script>
