<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M7.05458 21.125C6.76678 21.1245 6.48407 21.049 6.23427 20.9061C5.67177 20.5873 5.32208 19.9686 5.32208 19.2969V5.70312C5.32208 5.02952 5.67177 4.41265 6.23427 4.0939C6.49002 3.94691 6.78056 3.87133 7.07552 3.87504C7.37047 3.87875 7.65902 3.96162 7.91099 4.11499L19.529 11.0694C19.7711 11.2212 19.9707 11.432 20.109 11.6821C20.2474 11.9322 20.32 12.2133 20.32 12.4991C20.32 12.7848 20.2474 13.066 20.109 13.316C19.9707 13.5661 19.7711 13.7769 19.529 13.9287L7.90911 20.885C7.65125 21.0409 7.3559 21.1238 7.05458 21.125Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
