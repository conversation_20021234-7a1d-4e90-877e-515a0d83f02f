<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="20" height="20" rx="10" :class="fillColorPrimary" />
            <path
                d="M10 5C9.38635 5 8.88889 5.49746 8.88889 6.11111V8.88889H6.11111C5.49746 8.88889 5 9.38635 5 10C5 10.6136 5.49746 11.1111 6.11111 11.1111H8.88889V13.8889C8.88889 14.5025 9.38635 15 10 15C10.6136 15 11.1111 14.5025 11.1111 13.8889V11.1111H13.8889C14.5025 11.1111 15 10.6136 15 10C15 9.38635 14.5025 8.88889 13.8889 8.88889H11.1111V6.11111C11.1111 5.49746 10.6136 5 10 5Z"
                fill="white"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 20,
    },
    h: {
        type: Number,
        default: 20,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
