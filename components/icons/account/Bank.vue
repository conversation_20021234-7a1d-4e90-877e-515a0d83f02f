<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="18"
            viewBox="0 0 20 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M9.70996 2.25635C9.70996 2.0957 9.84082 1.96484 10.002 1.96484L10.0303 1.96631L10.0464 1.96826C10.0669 1.97119 10.0864 1.97656 10.1055 1.98389C10.1245 1.99121 10.1426 2.00049 10.1592 2.01123C10.2402 2.06299 10.2935 2.15332 10.2935 2.25635V2.32373C10.3364 2.34277 10.377 2.36572 10.4155 2.39209C10.54 2.47705 10.6382 2.59912 10.6938 2.74316C10.7026 2.76611 10.7085 2.78906 10.7114 2.81201C10.7129 2.82617 10.7139 2.84033 10.7134 2.85449C10.7109 2.96875 10.6406 3.07568 10.5278 3.11914C10.3779 3.17725 10.2095 3.10254 10.1519 2.95264C10.145 2.93506 10.1353 2.91895 10.123 2.90479C10.1152 2.89551 10.1064 2.88721 10.0972 2.87988C10.0957 2.87842 10.0938 2.87695 10.0923 2.87598C10.0908 2.875 10.0889 2.87354 10.0874 2.87256C10.0601 2.854 10.0259 2.84277 9.98926 2.84277C9.93066 2.84277 9.88721 2.86279 9.86084 2.89258C9.85254 2.90137 9.84619 2.91211 9.84033 2.92432C9.83936 2.92627 9.83838 2.92822 9.8374 2.93018C9.83643 2.93213 9.83594 2.93457 9.83496 2.93652L9.8291 2.95654C9.82666 2.96582 9.82568 2.97559 9.82568 2.98535C9.82568 2.99951 9.82812 3.01367 9.8335 3.02734C9.84033 3.04639 9.85205 3.06396 9.86963 3.07959C9.91455 3.11963 9.99316 3.13916 10.0713 3.15869C10.2661 3.20654 10.46 3.25488 10.6343 3.49902C10.6489 3.51953 10.6621 3.53955 10.6738 3.55908L10.6978 3.604C10.7061 3.62109 10.7139 3.63916 10.7207 3.65771C10.728 3.67725 10.7349 3.69775 10.7412 3.71973C10.7617 3.78955 10.7715 3.8584 10.7715 3.92432C10.7715 4.08252 10.7197 4.23975 10.6245 4.36963C10.5435 4.48047 10.4312 4.5708 10.2935 4.62598V4.6875C10.2935 4.74414 10.2773 4.79688 10.2495 4.8418C10.1982 4.92432 10.1064 4.979 10.002 4.979C9.84082 4.979 9.70996 4.84863 9.70996 4.6875V4.63379C9.67773 4.62158 9.64648 4.60742 9.61621 4.59131L9.57861 4.56982L9.5459 4.54883C9.51416 4.52734 9.48389 4.50391 9.45557 4.47852L9.41455 4.4375C9.37012 4.39062 9.33154 4.33838 9.29932 4.28125C9.28223 4.25098 9.26709 4.21973 9.25391 4.18701C9.19385 4.03809 9.2666 3.86865 9.41553 3.80908C9.48291 3.78174 9.55469 3.78174 9.61865 3.80371C9.63037 3.80762 9.64111 3.8125 9.65186 3.81787L9.67432 3.83008C9.68994 3.83936 9.70508 3.8501 9.71875 3.86279C9.75098 3.8916 9.77686 3.92822 9.79395 3.9707C9.80908 4.00879 9.83691 4.04199 9.87158 4.06543C9.89014 4.07812 9.91113 4.08789 9.93408 4.09375C9.94727 4.09766 9.96094 4.09961 9.9751 4.10059C9.97754 4.10059 9.97949 4.10107 9.98193 4.10107C9.98438 4.10107 9.98682 4.10107 9.98926 4.10107C10.0176 4.10107 10.041 4.09814 10.0596 4.09277C10.1011 4.08057 10.1333 4.05664 10.1553 4.02588C10.1768 3.99658 10.188 3.96094 10.188 3.92432L10.1875 3.90771C10.187 3.89795 10.1855 3.88916 10.1831 3.88184L10.1733 3.85938L10.1606 3.83691C10.1108 3.76709 10.0215 3.74561 9.93213 3.72363C9.86963 3.70801 9.80664 3.69238 9.74316 3.66895C9.65771 3.6377 9.57129 3.5918 9.48242 3.51221C9.43701 3.47217 9.39844 3.42871 9.36621 3.38281C9.33154 3.33252 9.30469 3.27979 9.28467 3.2251C9.25635 3.14697 9.24268 3.06592 9.24268 2.98535C9.24268 2.89795 9.25879 2.81104 9.29004 2.729C9.3208 2.64893 9.36719 2.57275 9.42773 2.50488C9.50293 2.42139 9.59814 2.35303 9.70996 2.31006V2.25635Z"
                :class="fillColorPrimary"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0.77832 5.74219C0.821777 5.6582 0.887695 5.58496 0.973145 5.53027L9.31982 0.203613C9.74561 -0.0678711 10.2578 -0.0678711 10.6836 0.203613L19.0303 5.53027C19.2393 5.66357 19.3315 5.90918 19.2622 6.14697C19.2354 6.23926 19.1875 6.31934 19.124 6.38281C19.1006 6.40674 19.0747 6.42822 19.0474 6.44727C19.0078 6.47412 18.9648 6.49609 18.9189 6.51172C18.8755 6.52686 18.8296 6.53662 18.7817 6.54053L18.7349 6.54248H18.4678V7.27295C18.4678 7.37012 18.4312 7.46094 18.3682 7.53711C18.3267 7.58691 18.2744 7.63086 18.2139 7.66553C18.1509 7.70166 18.0791 7.72852 18.002 7.74365V13.5391C18.2686 13.5913 18.4678 13.7832 18.4678 14.0098V14.7607H18.623C18.9746 14.7607 19.2627 15.0483 19.2627 15.3999V16.0425C19.4897 16.061 19.6909 16.1743 19.8257 16.3433C19.936 16.481 20.002 16.6558 20.002 16.8452V17.6509H0.00195312V16.8452C0.00195312 16.5791 0.132324 16.3428 0.332031 16.1958C0.44873 16.1099 0.589355 16.0547 0.741699 16.0425V15.3999C0.741699 15.0483 1.0293 14.7607 1.38086 14.7607H1.53613V14.0098C1.53613 13.895 1.5874 13.7891 1.67236 13.7056C1.75537 13.6245 1.87012 13.5649 2.00195 13.5391V7.74365C1.90771 7.7251 1.82227 7.68945 1.75098 7.64111C1.61963 7.55176 1.53613 7.41943 1.53613 7.27295V6.54248H1.26855C1.021 6.54248 0.810547 6.38477 0.741211 6.14697C0.700195 6.00635 0.71582 5.86328 0.77832 5.74219ZM15.7607 13.5249C15.4238 13.5249 15.1484 13.7432 15.1484 14.0098V14.7607H13.9302V14.0098C13.9302 13.8696 13.854 13.7427 13.7329 13.6538V7.62842C13.854 7.53955 13.9302 7.41309 13.9302 7.27295V6.54248H15.1484V7.27295C15.1484 7.53955 15.4238 7.75781 15.7607 7.75781H16.002V13.5249H15.7607ZM11.002 13.5581C10.7739 13.6289 10.6113 13.8047 10.6113 14.0098V14.7607H9.39307V14.0098C9.39307 13.8047 9.22998 13.6284 9.00195 13.5576V7.7251C9.08447 7.69971 9.15869 7.66016 9.21973 7.61035C9.32715 7.52295 9.39307 7.40381 9.39307 7.27295V6.54248H10.6113V7.27295C10.6113 7.47803 10.7739 7.65381 11.002 7.72461V13.5581ZM6.271 13.6543C6.15039 13.7432 6.07422 13.8696 6.07422 14.0098V14.7607H4.85498V14.0098C4.85498 13.9468 4.83984 13.8862 4.81201 13.8306C4.73535 13.6792 4.56494 13.5654 4.35889 13.5337V7.74902C4.44482 7.73584 4.52441 7.7085 4.59375 7.66992C4.75146 7.58203 4.85498 7.43652 4.85498 7.27295V6.54248H6.07422V7.27295C6.07422 7.41309 6.15039 7.53955 6.271 7.62842V13.6543ZM8.00879 3.47217C8.00879 4.57275 8.90137 5.46484 10.002 5.46484C11.1025 5.46484 11.9946 4.57275 11.9946 3.47217C11.9946 2.37158 11.1025 1.479 10.002 1.479C8.90137 1.479 8.00879 2.37158 8.00879 3.47217Z"
                :class="fillColorPrimary"
            />
            <path
                d="M9.70996 2.25635C9.70996 2.0957 9.84082 1.96484 10.002 1.96484C10.163 1.96484 10.2935 2.09526 10.2935 2.25635V2.32373C10.3364 2.34277 10.377 2.36572 10.4155 2.39209C10.54 2.47705 10.6382 2.59912 10.6938 2.74316C10.7517 2.89291 10.6776 3.06127 10.5278 3.11914C10.3779 3.17725 10.2095 3.10254 10.1519 2.95264C10.1393 2.92018 10.1164 2.89239 10.0874 2.87256C10.0601 2.854 10.0259 2.84277 9.98926 2.84277C9.93066 2.84277 9.88721 2.86279 9.86084 2.89258C9.8503 2.90437 9.84154 2.9194 9.83496 2.93652C9.82904 2.95202 9.82568 2.9686 9.82568 2.98535C9.82568 3.01852 9.83946 3.05263 9.86963 3.07959C9.91455 3.11963 9.99316 3.13916 10.0713 3.15869C10.2662 3.20694 10.4602 3.25473 10.6342 3.49777L10.6343 3.49902C10.6598 3.53465 10.681 3.56963 10.6978 3.604C10.7145 3.63846 10.7288 3.67723 10.7412 3.71973C10.7617 3.78955 10.7715 3.8584 10.7715 3.92432C10.7715 4.08252 10.7197 4.23975 10.6245 4.36963C10.5435 4.48047 10.4312 4.5708 10.2935 4.62598V4.6875C10.2935 4.84858 10.163 4.979 10.002 4.979C9.84082 4.979 9.70996 4.84863 9.70996 4.6875V4.63379C9.65179 4.61187 9.5969 4.5832 9.5459 4.54883C9.41669 4.4617 9.31403 4.33597 9.25391 4.18701C9.19385 4.03809 9.2666 3.86865 9.41553 3.80908C9.56465 3.74929 9.73415 3.82158 9.79395 3.9707C9.80908 4.00879 9.83691 4.04199 9.87158 4.06543C9.90483 4.08785 9.94563 4.10107 9.98926 4.10107C10.0176 4.10107 10.041 4.09814 10.0596 4.09277C10.1011 4.08057 10.1333 4.05664 10.1553 4.02588C10.1768 3.99658 10.188 3.96094 10.188 3.92432C10.188 3.90769 10.1865 3.89346 10.1831 3.88184C10.1819 3.8775 10.1785 3.86996 10.1733 3.85938C10.1728 3.85822 10.1723 3.857 10.1718 3.8559C10.1679 3.8482 10.1641 3.84184 10.1606 3.83691C10.1112 3.76786 10.0231 3.74596 9.93456 3.72407L9.93213 3.72363C9.78464 3.68714 9.63674 3.65011 9.48242 3.51221C9.31759 3.36487 9.24268 3.17418 9.24268 2.98535C9.24268 2.89795 9.25879 2.81104 9.29004 2.729C9.3208 2.64893 9.36719 2.57275 9.42773 2.50488C9.50293 2.42139 9.59814 2.35303 9.70996 2.31006V2.25635Z"
                :class="fillColorPrimary"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0.741211 6.14697C0.671794 5.9091 0.764228 5.66357 0.973145 5.53027L9.31982 0.203613C9.74561 -0.0678711 10.2578 -0.0678711 10.6836 0.203613L19.0303 5.53027C19.2393 5.66357 19.3315 5.90918 19.2622 6.14697C19.1928 6.38485 18.9827 6.54248 18.7349 6.54248H18.4678V7.27295C18.4678 7.49971 18.2685 7.69135 18.002 7.74365V13.5391C18.2686 13.5913 18.4678 13.7832 18.4678 14.0098V14.7607H18.623C18.9746 14.7607 19.2627 15.0483 19.2627 15.3999V16.0425C19.6752 16.0764 20.002 16.4244 20.002 16.8452V17.6509H0.00195312V16.8452C0.00195312 16.4244 0.32916 16.0764 0.741699 16.0425V15.3999C0.741699 15.0483 1.0293 14.7607 1.38086 14.7607H1.53613V14.0098C1.53613 13.783 1.73539 13.5913 2.00195 13.5391V7.74365C1.73539 7.69137 1.53613 7.49973 1.53613 7.27295V6.54248H1.26855C1.021 6.54248 0.810547 6.38477 0.741211 6.14697ZM15.7607 13.5249C15.4238 13.5249 15.1484 13.7432 15.1484 14.0098V14.7607H13.9302V14.0098C13.9302 13.8696 13.854 13.7427 13.7329 13.6538V7.62842C13.854 7.53955 13.9302 7.41309 13.9302 7.27295V6.54248H15.1484V7.27295C15.1484 7.53955 15.4238 7.75781 15.7607 7.75781H16.002V13.5249H15.7607ZM11.002 13.5581C10.7739 13.6289 10.6113 13.8047 10.6113 14.0098V14.7607H9.39307V14.0098C9.39307 13.8047 9.22998 13.6284 9.00195 13.5576V7.7251C9.23006 7.65448 9.39307 7.47793 9.39307 7.27295V6.54248H10.6113V7.27295C10.6113 7.47803 10.7739 7.65381 11.002 7.72461V13.5581ZM6.271 13.6543C6.15039 13.7432 6.07422 13.8696 6.07422 14.0098V14.7607H4.85498V14.0098C4.85498 13.7744 4.64061 13.5769 4.35889 13.5337V7.74902C4.64061 7.70586 4.85498 7.5083 4.85498 7.27295V6.54248H6.07422V7.27295C6.07422 7.41309 6.15039 7.53955 6.271 7.62842V13.6543ZM8.00879 3.47217C8.00879 4.57275 8.90137 5.46484 10.002 5.46484C11.1025 5.46484 11.9946 4.57275 11.9946 3.47217C11.9946 2.37158 11.1025 1.479 10.002 1.479C8.90137 1.479 8.00879 2.37158 8.00879 3.47217Z"
                :class="fillColorPrimary"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
