<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 17 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clip-path="url(#clip0_9910_14054)">
                <path
                    d="M2.55 0C1.14167 0 0 1.12824 0 2.52V15.12C0 16.5118 1.14167 17.64 2.55 17.64H4.675V7.56C4.675 6.63216 5.43612 5.88 6.375 5.88H12.75V3.81219C12.75 3.38666 12.5866 2.97699 12.2928 2.6659L10.2791 0.533705C9.95754 0.193278 9.50747 0 9.03626 0H2.55Z"
                    :class="fillColorSecondary"
                />
                <path
                    d="M1.7002 3.35969C1.7002 2.43185 2.46131 1.67969 3.4002 1.67969H13.2865C13.7577 1.67969 14.2077 1.87297 14.5293 2.21339L16.543 4.34559C16.8368 4.65667 17.0002 5.06635 17.0002 5.49188V19.3197C17.0002 20.2475 16.2391 20.9997 15.3002 20.9997H3.4002C2.46131 20.9997 1.7002 20.2475 1.7002 19.3197V3.35969Z"
                    :class="fillColorPrimary"
                />
                <path
                    d="M4.25 13.8615C4.25 13.3976 4.63056 13.0215 5.1 13.0215H13.6C14.0694 13.0215 14.45 13.3976 14.45 13.8615C14.45 14.3254 14.0694 14.7015 13.6 14.7015H5.1C4.63056 14.7015 4.25 14.3254 4.25 13.8615Z"
                    fill="white"
                />
                <path
                    d="M4.25 16.8015C4.25 16.3376 4.63056 15.9615 5.1 15.9615H7.65C8.11944 15.9615 8.5 16.3376 8.5 16.8015C8.5 17.2654 8.11944 17.6415 7.65 17.6415H5.1C4.63056 17.6415 4.25 17.2654 4.25 16.8015Z"
                    fill="white"
                />
                <path
                    d="M13.6 15.9615C13.1306 15.9615 12.75 16.3376 12.75 16.8015C12.75 17.2654 13.1306 17.6415 13.6 17.6415C14.0694 17.6415 14.45 17.2654 14.45 16.8015C14.45 16.3376 14.0694 15.9615 13.6 15.9615Z"
                    fill="white"
                />
            </g>
            <defs>
                <clipPath id="clip0_9910_14054">
                    <rect width="17" height="21" fill="white" />
                </clipPath>
            </defs>
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 17,
    },
    h: {
        type: Number,
        default: 21,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
