<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M10.5692 4.25H6.06915C5.67133 4.25 5.2898 4.40804 5.00849 4.68934C4.72719 4.97064 4.56915 5.35218 4.56915 5.75V10.25C4.56915 10.6478 4.72719 11.0294 5.00849 11.3107C5.2898 11.592 5.67133 11.75 6.06915 11.75H10.5692C10.967 11.75 11.3485 11.592 11.6298 11.3107C11.9111 11.0294 12.0692 10.6478 12.0692 10.25V5.75C12.0692 5.35218 11.9111 4.97064 11.6298 4.68934C11.3485 4.40804 10.967 4.25 10.5692 4.25ZM10.5692 10.25H6.06915V5.75H10.5692V10.25ZM19.5692 4.25H15.0692C14.6713 4.25 14.2898 4.40804 14.0085 4.68934C13.7272 4.97064 13.5692 5.35218 13.5692 5.75V10.25C13.5692 10.6478 13.7272 11.0294 14.0085 11.3107C14.2898 11.592 14.6713 11.75 15.0692 11.75H19.5692C19.967 11.75 20.3485 11.592 20.6298 11.3107C20.9111 11.0294 21.0692 10.6478 21.0692 10.25V5.75C21.0692 5.35218 20.9111 4.97064 20.6298 4.68934C20.3485 4.40804 19.967 4.25 19.5692 4.25ZM19.5692 10.25H15.0692V5.75H19.5692V10.25ZM10.5692 13.25H6.06915C5.67133 13.25 5.2898 13.408 5.00849 13.6893C4.72719 13.9706 4.56915 14.3522 4.56915 14.75V19.25C4.56915 19.6478 4.72719 20.0294 5.00849 20.3107C5.2898 20.592 5.67133 20.75 6.06915 20.75H10.5692C10.967 20.75 11.3485 20.592 11.6298 20.3107C11.9111 20.0294 12.0692 19.6478 12.0692 19.25V14.75C12.0692 14.3522 11.9111 13.9706 11.6298 13.6893C11.3485 13.408 10.967 13.25 10.5692 13.25ZM10.5692 19.25H6.06915V14.75H10.5692V19.25ZM19.5692 13.25H15.0692C14.6713 13.25 14.2898 13.408 14.0085 13.6893C13.7272 13.9706 13.5692 14.3522 13.5692 14.75V19.25C13.5692 19.6478 13.7272 20.0294 14.0085 20.3107C14.2898 20.592 14.6713 20.75 15.0692 20.75H19.5692C19.967 20.75 20.3485 20.592 20.6298 20.3107C20.9111 20.0294 21.0692 19.6478 21.0692 19.25V14.75C21.0692 14.3522 20.9111 13.9706 20.6298 13.6893C20.3485 13.408 19.967 13.25 19.5692 13.25ZM19.5692 19.25H15.0692V14.75H19.5692V19.25Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
