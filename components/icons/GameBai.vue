<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M21.047 2.38072L13.4211 1.03608C12.1379 0.809856 10.9098 1.66973 10.6835 2.95305L10.5021 3.98157C10.4118 3.99493 10.3217 4.01298 10.2322 4.03696L2.75258 6.0411C2.14286 6.20447 1.63322 6.59552 1.31762 7.14216C1.00202 7.68884 0.91815 8.32571 1.08156 8.93542L4.38054 21.2474C4.54391 21.8571 4.93496 22.3667 5.48164 22.6823C5.84579 22.8926 6.24986 23 6.65938 23C6.86464 23 7.07132 22.973 7.2749 22.9184L14.7545 20.9143C15.3642 20.7509 15.8738 20.3598 16.1894 19.8132C16.2832 19.6508 16.356 19.4803 16.4084 19.3051L18.0129 19.588C18.1511 19.6123 18.2886 19.6241 18.4244 19.6241C19.549 19.6241 20.5487 18.816 20.7506 17.671L22.9639 5.11841C23.1902 3.83514 22.3302 2.60703 21.047 2.38072ZM15.1804 18.3536C15.2546 18.6307 15.2165 18.9202 15.073 19.1687C14.9296 19.4172 14.6979 19.5949 14.4208 19.6692L6.94121 21.6734C6.66407 21.7477 6.37459 21.7095 6.12611 21.566C5.87763 21.4226 5.69987 21.1909 5.62562 20.9138L2.32664 8.60182C2.25239 8.32467 2.2905 8.03516 2.43397 7.78667C2.57744 7.53819 2.80908 7.36048 3.08622 7.28618L10.5658 5.28204C10.6584 5.25724 10.7523 5.24495 10.8456 5.24495C11.0317 5.24495 11.2154 5.29377 11.3809 5.38933C11.6294 5.5328 11.8071 5.76444 11.8814 6.04158L15.1804 18.3536ZM13.8894 8.55528C13.8941 8.51846 13.8991 8.48168 13.9055 8.44516C13.9751 8.05071 14.3193 7.77241 14.7068 7.77241C14.7535 7.77241 14.801 7.77645 14.8485 7.78487C15.1484 7.83776 15.3919 8.05153 15.4837 8.34276C15.556 8.57191 15.7498 8.74181 15.9865 8.78353C16.2232 8.82529 16.4633 8.73188 16.6096 8.54132C16.7956 8.29907 17.0974 8.18146 17.3974 8.23431C17.6115 8.27208 17.7981 8.39097 17.9228 8.56908C18.0475 8.74718 18.0954 8.96318 18.0577 9.17733C17.8013 10.6311 16.1628 11.8683 15.3485 12.4021C15.1442 12.1285 14.8744 11.739 14.6212 11.2861L13.8894 8.55528ZM21.6944 4.89459L19.4811 17.4472C19.3782 18.0306 18.8199 18.4213 18.2366 18.3186L16.4196 17.9982L15.3169 13.883C15.3823 13.8708 15.4467 13.8484 15.5075 13.8151C15.6449 13.7403 18.8774 11.9515 19.3271 9.40115C19.5285 8.25915 18.7632 7.16622 17.6212 6.96487C17.1495 6.88173 16.676 6.96152 16.2696 7.17769C15.9616 6.83554 15.5441 6.59861 15.0724 6.51543C14.5192 6.41785 13.9611 6.54159 13.5009 6.86381C13.4825 6.8767 13.4648 6.89028 13.4468 6.90364L13.1265 5.70798C12.9631 5.09826 12.5721 4.58862 12.0254 4.27302C11.9458 4.22709 11.8642 4.18666 11.7812 4.15061L11.9529 3.17687C12.0557 2.59358 12.614 2.2027 13.1972 2.30552L20.823 3.65016C21.4064 3.75302 21.7973 4.31126 21.6944 4.89459Z"
                :class="fillColor"
            />
            <path
                d="M9.31845 17.6589C9.43751 17.7636 9.58932 17.8195 9.74422 17.8195C9.79986 17.8195 9.85593 17.8123 9.91098 17.7975C10.1192 17.7417 10.2858 17.5855 10.3548 17.3812L11.8263 13.0242C11.9095 12.7779 11.8365 12.5058 11.6413 12.3341L8.18852 9.29656C8.02662 9.15417 7.80422 9.10209 7.596 9.15791C7.38773 9.21372 7.22115 9.36995 7.15218 9.57422L5.68071 13.9312C5.59752 14.1775 5.67044 14.4497 5.86564 14.6214L9.31845 17.6589ZM8.06379 10.9036L10.4677 13.0184L9.44323 16.0518L7.03935 13.937L8.06379 10.9036Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 20,
    },
    h: {
        type: Number,
        default: 20,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
