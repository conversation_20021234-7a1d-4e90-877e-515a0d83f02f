<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.1583 20.3645C10.9156 20.29 9.55977 20.4152 8.20066 20.1174C5.14308 19.4489 4.01678 17.5823 3.81841 15.0895C3.61348 12.5206 3.48724 9.91272 3.97415 7.3421C4.37418 5.22501 5.82346 3.70023 7.89081 3.32623C10.8107 2.79654 13.7584 2.74915 16.6668 3.31269C19.2407 3.81023 20.6671 5.40778 20.8933 8.1781C21.1114 10.8317 21.2687 13.5224 20.6949 16.1692C20.2359 18.2846 18.8899 19.4794 16.9078 20.0328C15.3765 20.4592 13.8223 20.3222 12.1583 20.3645ZM13.1666 12.1127C14.7503 12.1127 16.3356 12.0873 17.9193 12.1246C18.5489 12.1398 18.8063 11.9638 18.7801 11.2733C18.744 10.3138 18.7014 9.35257 18.5292 8.41333C18.1439 6.31317 16.7553 5.10824 14.6683 4.93901C13.0043 4.80362 11.3451 4.79516 9.67945 4.93901C7.74981 5.10316 6.44481 6.17271 6.08741 8.14256C5.65131 10.544 5.67427 12.9741 6.13331 15.3704C6.46776 17.1203 7.80228 18.3354 9.43353 18.3845C11.3976 18.4454 13.3682 18.4555 15.3306 18.3591C17.3209 18.2626 18.5358 16.9866 18.7145 15.0218C18.7456 14.6834 18.8063 14.2501 18.3095 14.3009C17.857 14.3466 17.2177 13.9201 16.9849 14.702C16.5291 16.2234 16.1946 16.5601 14.7339 16.6008C13.178 16.6448 11.6173 16.7023 10.0664 16.5246C8.95481 16.3977 8.13345 15.8375 7.78916 14.6546C7.6121 14.0437 7.64489 13.4294 7.60554 12.8134C7.56783 12.2363 7.82359 12.0941 8.3351 12.1009C9.94668 12.1246 11.5583 12.1093 13.1698 12.111L13.1666 12.1127Z"
                :class="fillColor"
            />
            <path
                d="M12.5151 10.086C11.2068 10.086 9.89526 10.0471 8.58862 10.1013C7.90824 10.13 7.8148 9.82374 7.88201 9.24835C8.1099 7.32757 8.6542 6.74203 10.515 6.61849C11.9561 6.52203 13.4037 6.51018 14.8448 6.65741C16.3383 6.80972 17.2318 7.83527 17.2548 9.31266C17.263 9.87958 17.122 10.1182 16.5236 10.0996C15.1891 10.0573 13.8513 10.086 12.5151 10.086Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
