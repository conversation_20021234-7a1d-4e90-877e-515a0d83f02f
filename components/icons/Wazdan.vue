<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.3858 9.00812C13.4289 9.00812 14.4721 9.01082 15.5152 9.00586C15.7116 9.00496 15.8796 9.04149 15.9579 9.23811C16.0363 9.43472 15.9674 9.59211 15.8179 9.74182C14.8734 10.687 13.9361 11.6404 12.9943 12.5883C11.8354 13.7549 10.6724 14.9175 9.51581 16.0863C9.35997 16.2437 9.19602 16.3461 8.97803 16.2536C8.76499 16.1634 8.73796 15.9691 8.73796 15.763C8.73932 13.689 8.73841 11.6155 8.73932 9.5416C8.73932 9.13033 8.86453 9.00857 9.2789 9.00857C10.3144 9.00857 11.3503 9.00812 12.3858 9.00812Z"
                :class="fillColor"
            />
            <path
                d="M20.3926 9.00812C21.4357 9.00812 22.4789 9.01037 23.522 9.00586C23.7184 9.00496 23.8814 9.04374 23.9643 9.23991C24.0494 9.44239 23.9702 9.59526 23.8242 9.74182C22.7635 10.8047 21.7073 11.8726 20.6489 12.9378C19.6062 13.9871 18.5613 15.0342 17.5213 16.0863C17.3659 16.2433 17.2024 16.3461 16.9835 16.2527C16.7691 16.1616 16.7466 15.9686 16.747 15.7616C16.7493 13.6877 16.7484 11.6137 16.7488 9.53979C16.7488 9.12582 16.8691 9.00767 17.2857 9.00767C18.3212 9.00767 19.3571 9.00812 20.3926 9.00812Z"
                :class="fillColor"
            />
            <path
                d="M7.97044 12.65C7.97044 13.7021 7.96819 14.7538 7.97225 15.8058C7.97315 16.0002 7.94072 16.172 7.74479 16.2532C7.54752 16.3348 7.38987 16.2636 7.24169 16.1138C5.13065 13.988 3.01646 11.8654 0.901361 9.74318C0.757682 9.59887 0.674358 9.44419 0.763087 9.23991C0.847763 9.04555 1.01126 9 1.20538 9C3.29931 9.00135 5.39278 9 7.48671 9.0018C7.84613 9.0018 7.96999 9.13258 7.96999 9.49425C7.97044 10.5463 7.97044 11.598 7.97044 12.65Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
