<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M5.47568 9.07715C5.16391 9.07715 4.91116 9.32989 4.91116 9.64167V11.2725C4.91116 11.5843 5.16391 11.837 5.47568 11.837C5.78746 11.837 6.0402 11.5843 6.0402 11.2725V9.64167C6.0402 9.32989 5.78746 9.07715 5.47568 9.07715Z"
                :class="fillColor"
            />
            <path
                d="M15.5107 11.5862C15.5107 11.2745 15.258 11.0217 14.9462 11.0217C14.6344 11.0217 14.3817 11.2745 14.3817 11.5862V13.468C14.3817 13.7798 14.6344 14.0325 14.9462 14.0325H16.4516C16.7634 14.0325 17.0161 13.7798 17.0161 13.468C17.0161 13.1562 16.7634 12.9035 16.4516 12.9035H15.5107V11.5862Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M2.52517 5C1.66849 5 0.833496 5.61851 0.833496 6.54302V8.52902C0.833496 8.79256 1.01584 9.02103 1.27282 9.07948C2.05954 9.25842 2.54575 9.85046 2.54575 10.457C2.54575 11.0636 2.05954 11.6556 1.27282 11.8346C1.01584 11.893 0.833496 12.1215 0.833496 12.385V14.371C0.833496 15.2956 1.6685 15.9141 2.52517 15.9141H5.46408L5.47568 15.9142L5.48728 15.9141H13.0024C13.6539 16.3872 14.4555 16.6663 15.3222 16.6663C17.5047 16.6663 19.2739 14.8971 19.2739 12.7147C19.2739 10.9943 18.1746 9.53073 16.6401 8.98809V6.54302C16.6401 5.61852 15.805 5 14.9484 5H2.52517ZM11.3706 12.7147C11.3706 13.4738 11.5847 14.1829 11.9557 14.785H6.0402V13.7188C6.0402 13.407 5.78746 13.1543 5.47568 13.1543C5.16391 13.1543 4.91116 13.407 4.91116 13.7188V14.785H2.52517C2.13683 14.785 1.96254 14.5274 1.96254 14.371V12.8C2.93743 12.4166 3.67479 11.5436 3.67479 10.457C3.67479 9.37047 2.93743 8.49748 1.96254 8.11404V6.54302C1.96254 6.3867 2.13683 6.12904 2.52517 6.12904H4.91116V7.19536C4.91116 7.50713 5.16391 7.75988 5.47568 7.75988C5.78746 7.75988 6.0402 7.50713 6.0402 7.19536V6.12904H14.9484C15.3367 6.12904 15.511 6.38671 15.511 6.54302V8.76745C15.4485 8.76451 15.3855 8.76302 15.3222 8.76302C13.1398 8.76302 11.3706 10.5322 11.3706 12.7147ZM15.3222 9.89206C13.7634 9.89206 12.4996 11.1558 12.4996 12.7147C12.4996 14.2735 13.7634 15.5373 15.3222 15.5373C16.8811 15.5373 18.1448 14.2735 18.1448 12.7147C18.1448 11.1558 16.8811 9.89206 15.3222 9.89206Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
