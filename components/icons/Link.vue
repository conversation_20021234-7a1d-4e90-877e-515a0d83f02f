<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.4621 17.2195L8.34105 19.3405C7.46172 20.2198 6.03771 20.2198 5.15911 19.3407C4.28034 18.4619 4.28034 17.0378 5.15894 16.1592L9.40172 11.9164C10.2803 11.0378 11.7045 11.0378 12.5831 11.9164C12.876 12.2093 13.3508 12.2093 13.6437 11.9164C13.9366 11.6235 13.9366 11.1486 13.6437 10.8557C12.1793 9.39131 9.80546 9.39131 8.34105 10.8557L4.0983 15.0985C2.6339 16.5629 2.6339 18.9367 4.0983 20.4011C5.56253 21.8663 7.93658 21.8663 9.40176 20.4011L11.5228 18.2801C11.8157 17.9872 11.8157 17.5123 11.5228 17.2195C11.2299 16.9266 10.755 16.9266 10.4621 17.2195Z"
                :class="fillColor"
            />
            <path
                d="M19.9002 4.5983C18.4358 3.1339 16.0611 3.1339 14.5967 4.5983L12.052 7.14307C11.7591 7.43596 11.7591 7.91085 12.052 8.20374C12.3448 8.49663 12.8197 8.49663 13.1126 8.20374L15.6574 5.65897C16.536 4.78034 17.9609 4.78034 18.8395 5.65897C19.7181 6.53757 19.7181 7.96172 18.8395 8.84031L14.173 13.5069C13.2944 14.3855 11.8702 14.3855 10.9916 13.5069C10.6988 13.214 10.2239 13.214 9.93097 13.5069C9.63808 13.7998 9.63808 14.2747 9.93097 14.5675C11.3954 16.0319 13.7692 16.0319 15.2336 14.5675L19.9002 9.90102C21.3646 8.43661 21.3646 6.06271 19.9002 4.5983Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
