<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.5639 5.56296C14.9537 4.66116 13.072 4.37071 11.2648 4.74497C9.45754 5.11924 7.84603 6.13307 6.72634 7.60022C6.01677 8.52997 5.53492 9.6022 5.30662 10.7317L5.40365 10.6347C5.71327 10.3251 6.21528 10.3251 6.52491 10.6347C6.83454 10.9443 6.83454 11.4463 6.52491 11.7559L4.9392 13.3417C4.62957 13.6513 4.12756 13.6513 3.81793 13.3417L2.23222 11.7559C1.92259 11.4463 1.92259 10.9443 2.23222 10.6347C2.54185 10.3251 3.04386 10.3251 3.35349 10.6347L3.66181 10.943C3.88382 9.38769 4.50005 7.90361 5.46579 6.6382C6.81714 4.8675 8.76206 3.6439 10.9432 3.19221C13.1244 2.74051 15.3953 3.09105 17.3387 4.17943C19.2822 5.26781 20.7676 7.02092 21.5221 9.11668C22.2767 11.2124 22.2496 13.5101 21.4459 15.5875C20.6422 17.6649 19.1159 19.3825 17.1473 20.4248C15.1788 21.4671 12.9003 21.764 10.7303 21.2611C8.56043 20.7581 6.64487 19.489 5.33561 17.687C5.07823 17.3328 5.15676 16.8369 5.51102 16.5795C5.86527 16.3222 6.3611 16.4007 6.61848 16.7549C7.7033 18.2481 9.29047 19.2996 11.0884 19.7163C12.8863 20.1331 14.7743 19.887 16.4053 19.0234C18.0364 18.1598 19.3011 16.7366 19.967 15.0154C20.6329 13.2941 20.6553 11.3903 20.0302 9.65382C19.405 7.91733 18.1742 6.46475 16.5639 5.56296Z"
                :class="fillColor"
            />
            <path
                d="M13.6117 8.02129C13.6117 7.5834 13.2567 7.22843 12.8188 7.22843C12.3809 7.22843 12.026 7.5834 12.026 8.02129V12.2498C12.026 12.4601 12.1095 12.6618 12.2582 12.8105L15.4296 15.9819C15.7392 16.2915 16.2413 16.2915 16.5509 15.9819C16.8605 15.6723 16.8605 15.1703 16.5509 14.8606L13.6117 11.9214V8.02129Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
