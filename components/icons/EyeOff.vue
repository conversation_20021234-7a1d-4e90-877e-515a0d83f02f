<template>
    <svg :width="w" :height="h" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M1.47018 1.46967C1.76307 1.17678 2.23794 1.17678 2.53084 1.46967L22.5308 21.4697C22.8237 21.7626 22.8237 22.2374 22.5308 22.5303C22.2379 22.8232 21.7631 22.8232 21.4702 22.5303L17.363 18.4232C16.0758 19.1017 14.6698 19.5306 13.2193 19.6852C11.5793 19.8599 9.92081 19.6799 8.35647 19.1572C6.79212 18.6346 5.35851 17.7815 4.1529 16.656C2.94729 15.5304 1.9979 14.1587 1.36914 12.6339C1.36574 12.6257 1.36249 12.6174 1.35938 12.609C1.21354 12.2161 1.21354 11.7839 1.35938 11.391C1.36249 11.3826 1.36574 11.3743 1.36914 11.3661C2.19589 9.36115 3.56998 7.63706 5.32554 6.38569L1.47018 2.53033C1.17728 2.23744 1.17728 1.76256 1.47018 1.46967ZM6.40329 7.46344C4.78246 8.55823 3.51456 10.1072 2.7626 11.9216C2.7458 11.9725 2.7458 12.0275 2.7626 12.0784C3.30938 13.3978 4.13247 14.5848 5.17653 15.5595C6.22489 16.5383 7.47151 17.28 8.83181 17.7345C10.1921 18.189 11.6342 18.3456 13.0604 18.1936C14.166 18.0758 15.2419 17.7747 16.2446 17.3047L14.0683 15.1284C13.4499 15.5372 12.7193 15.7565 11.9678 15.75C10.9845 15.7414 10.044 15.3471 9.34872 14.6518C8.65344 13.9565 8.25906 13.016 8.25051 12.0327C8.24398 11.2812 8.46328 10.5506 8.87206 9.93222L6.40329 7.46344ZM9.97013 11.0303C9.82395 11.3363 9.74745 11.6743 9.75046 12.0197C9.75558 12.6096 9.99221 13.174 10.4094 13.5911C10.8265 14.0083 11.3909 14.2449 11.9808 14.2501C12.3262 14.2531 12.6642 14.1766 12.9702 14.0304L9.97013 11.0303ZM17.0628 7.12491C15.1808 6.02055 12.989 5.56251 10.8223 5.82073C10.411 5.86975 10.0378 5.57605 9.98878 5.16475C9.93976 4.75345 10.2335 4.38029 10.6448 4.33127C13.1367 4.03429 15.6575 4.56108 17.8219 5.83119C19.9864 7.1013 21.6758 9.04492 22.6319 11.3652C22.6337 11.3695 22.6354 11.3738 22.6371 11.3781C22.6386 11.382 22.6401 11.386 22.6416 11.39C22.7875 11.7829 22.7875 12.2151 22.6416 12.608C22.6385 12.6164 22.6353 12.6247 22.6318 12.633C22.2387 13.5862 21.7191 14.4821 21.0871 15.2967C20.8332 15.624 20.362 15.6835 20.0348 15.4296C19.7075 15.1757 19.648 14.7045 19.9019 14.3773C20.4479 13.6736 20.8974 12.9001 21.2384 12.0774C21.2552 12.0264 21.2552 11.9715 21.2384 11.9206C20.4058 9.91019 18.9398 8.22631 17.0628 7.12491Z"
            :fill="color"
        />
    </svg>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    color: {
        type: String,
        default: '#333',
    },
})
</script>
