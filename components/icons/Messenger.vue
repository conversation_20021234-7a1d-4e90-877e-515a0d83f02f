<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M15.597 14.538L18.247 10.618C18.509 10.232 18.012 9.813 17.632 10.089L14.774 12.104C14.6783 12.1707 14.5645 12.2064 14.448 12.2064C14.3314 12.2064 14.2176 12.1707 14.122 12.104L12.006 10.627C11.373 10.19 10.468 10.35 10.043 10.962L7.39296 14.882C7.13096 15.268 7.62796 15.688 8.00796 15.411L10.866 13.396C10.9616 13.3293 11.0754 13.2936 11.192 13.2936C11.3085 13.2936 11.4223 13.3293 11.518 13.396L13.634 14.848C14.267 15.31 15.172 15.15 15.597 14.538Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.82 2.75C10.2341 2.75 7.75414 3.77723 5.92566 5.60571C4.09718 7.43419 3.06996 9.91414 3.06996 12.5C3.06817 13.8147 3.33312 15.1161 3.84877 16.3255C4.36442 17.5349 5.12006 18.6271 6.06996 19.536V22C6.06989 22.1189 6.09807 22.236 6.15218 22.3419C6.20629 22.4477 6.28478 22.5391 6.38117 22.6087C6.47757 22.6782 6.58911 22.7239 6.70661 22.7418C6.8241 22.7598 6.94419 22.7496 7.05696 22.712L9.83696 21.785C10.8009 22.0947 11.8074 22.2516 12.82 22.25C18.205 22.25 22.57 17.885 22.57 12.5C22.57 7.115 18.205 2.75 12.82 2.75ZM4.56996 12.5C4.57063 10.7496 5.12803 9.04475 6.16156 7.63202C7.19508 6.2193 8.65116 5.17191 10.3192 4.64133C11.9873 4.11074 13.7809 4.12446 15.4406 4.68049C17.1004 5.23653 18.5403 6.30606 19.5521 7.73442C20.5639 9.16279 21.0951 10.876 21.069 12.6262C21.0429 14.3764 20.4608 16.073 19.4069 17.4705C18.353 18.8681 16.8819 19.8942 15.2063 20.4005C13.5307 20.9068 11.7375 20.8671 10.086 20.287C9.92889 20.2318 9.75791 20.2304 9.59996 20.283L7.56996 20.959V19.209C7.57009 19.1033 7.54789 18.9988 7.50481 18.9023C7.46173 18.8058 7.39873 18.7195 7.31996 18.649C6.45382 17.876 5.76102 16.9285 5.28706 15.8687C4.81311 14.809 4.56872 13.6609 4.56996 12.5Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
