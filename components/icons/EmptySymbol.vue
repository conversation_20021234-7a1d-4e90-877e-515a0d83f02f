<template>
    <svg
        width="90"
        height="90"
        viewBox="0 0 90 90"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        :class="classWrapper"
    >
        <g clip-path="url(#clip0_21949_17086)">
            <path
                d="M9.63808 32.7861C13.0336 8.99031 40.1174 -3.8643 60.8949 8.45113C78.4925 18.8803 82.9664 42.3529 70.4623 58.5761L88.7338 76.7128L90.0004 79.0039V80.7548C89.4345 82.6412 88.2753 83.8002 86.3891 84.3662H84.6645L82.6156 83.3418L64.0746 64.9902C63.8037 64.9108 61.9456 66.338 61.4877 66.6074C55.3584 70.3422 48.8379 71.9119 42.557 71.7265C44.3837 68.3321 45.4212 64.4499 45.4213 60.3252C45.4213 47.0171 34.6326 36.2285 21.3246 36.2285C16.9512 36.2285 12.8495 37.3929 9.31386 39.4296C9.21473 37.277 9.31566 35.058 9.63808 32.7861ZM52.0023 13.0869C50.5741 12.7904 48.1752 12.3586 46.7738 12.5742C43.5669 13.0323 42.5698 17.1829 45.2377 19.0693C46.5043 19.9586 47.7174 19.6082 49.0648 19.7968C55.0203 20.6323 60.0866 25.1055 61.2455 31.0878C61.6767 33.3515 61.0838 36.9627 64.3715 37.4209C70.5967 38.2563 68.4943 29.6061 67.2816 26.3183C64.8562 19.7969 58.8202 14.5422 52.0023 13.0869Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M20.5626 41.0469C16.6196 41.0469 12.8737 42.2298 9.62072 44.3985C6.36772 46.5671 3.80476 49.7215 2.32612 53.3688C0.847484 57.0162 0.453175 61.0578 1.24178 64.9022C2.03039 68.7467 3.90333 72.2954 6.66345 75.1541C9.42357 77.9142 12.9723 79.8857 16.8168 80.5758C20.6612 81.3644 24.6042 80.9701 28.2515 79.3929C31.8988 77.9142 34.9547 75.3512 37.1234 72.0982C39.292 68.8452 40.4749 65.0008 40.4749 61.0578C40.4749 55.7347 38.4048 50.7073 34.659 46.9614C30.8145 43.117 25.7872 41.0469 20.5626 41.0469ZM20.5626 75.1541C17.9997 75.1541 15.5353 74.4641 13.3666 73.1826C11.1979 71.9011 9.42357 69.9296 8.14208 67.6623C6.95918 65.3951 6.36772 62.8321 6.4663 60.2692C6.56488 57.7062 7.35348 55.2418 8.83212 53.0731L28.3501 72.7883C25.9843 74.3655 23.3228 75.1541 20.5626 75.1541ZM32.2931 68.8453L12.7752 49.1301C15.5353 47.3557 18.6897 46.4685 21.9427 46.8628C25.1957 47.1586 28.153 48.6372 30.5188 51.003C32.786 53.3688 34.2647 56.3261 34.5604 59.6777C34.9547 62.8321 34.0675 66.0851 32.2931 68.8453Z"
                :class="fillColor"
            />
        </g>
        <defs>
            <clipPath id="clip0_21949_17086">
                <rect width="90" height="90" fill="white" />
            </clipPath>
        </defs>
    </svg>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-slate-600',
    },
})
</script>
