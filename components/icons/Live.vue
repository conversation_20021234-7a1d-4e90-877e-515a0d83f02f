<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M4.08301 15.9166C0.833008 12.6666 0.833008 7.33325 4.08301 4.08325M6.49967 13.4999C4.58301 11.5833 4.58301 8.41659 6.49967 6.41658M13.4997 6.49992C15.4163 8.41659 15.4163 11.5833 13.4997 13.5833M15.9163 4.08325C19.1663 7.33325 19.1663 12.5833 15.9163 15.8333M11.6663 9.99992C11.6663 10.9204 10.9201 11.6666 9.99967 11.6666C9.0792 11.6666 8.33301 10.9204 8.33301 9.99992C8.33301 9.07944 9.0792 8.33325 9.99967 8.33325C10.9201 8.33325 11.6663 9.07944 11.6663 9.99992Z"
                :class="strokelColor"
                stroke-width="1.66667"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    strokelColor: {
        type: String,
        default: 'stroke-white',
    },
})
</script>
