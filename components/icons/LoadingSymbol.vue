<template>
    <svg
        width="90"
        height="90"
        viewBox="0 0 90 90"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        :class="['animate-spin', classWrapper]"
    >
        <path
            d="M46.2399 85.9625C38.2553 84.9498 38.9709 72.7478 47.1221 72.8266C55.2597 72.9076 55.7998 84.916 47.9997 85.9625C47.5969 86.0165 46.645 86.0143 46.2376 85.9625H46.2399Z"
            :class="fillColor"
        />
        <path
            d="M45.887 4.06763C59.4955 2.68585 60.6905 22.9265 47.7594 23.7029C34.8305 24.4793 33.631 5.31214 45.887 4.06763Z"
            :class="fillColor"
        />
        <path
            d="M22.505 14.7845C36.289 12.9211 36.5703 33.8616 23.1058 32.6644C12.72 31.7417 12.6907 16.11 22.505 14.7845Z"
            :class="fillColor"
        />
        <path
            d="M8.50878 52.4569C0.953984 44.9741 12.141 33.3145 19.9051 40.6645C27.8627 48.1968 16.2661 60.1377 8.50878 52.4569Z"
            :class="fillColor"
        />
        <path
            d="M22.6611 62.3147C33.6861 60.7304 34.3432 76.8436 24.2072 76.9832C15.0163 77.1115 14.3772 63.5029 22.6611 62.3147Z"
            :class="fillColor"
        />
        <path
            d="M68.9362 63.9143C75.0012 62.9669 78.2824 70.5352 73.4461 74.1854C68.5356 77.8919 61.8698 72.0228 65.1622 66.5451C65.8801 65.3501 67.5387 64.1326 68.934 63.9143H68.9362Z"
            :class="fillColor"
        />
        <path
            d="M78.7008 41.8278C85.855 40.6216 86.888 50.6767 80.5439 51.5093C74.0063 52.3667 73.1557 42.7618 78.7008 41.8278Z"
            :class="fillColor"
        />
        <path
            d="M69.7473 19.5829C74.9571 19.2138 75.6209 27.0859 70.8005 27.689C64.7422 28.4497 64.3844 19.961 69.7473 19.5829Z"
            :class="fillColor"
        />
    </svg>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-slate-600',
    },
})
</script>
