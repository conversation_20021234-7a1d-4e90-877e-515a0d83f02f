<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M14.1974 7.5H12.8724V3.33333C12.8724 2.875 12.4974 2.5 12.0391 2.5H8.70573C8.2474 2.5 7.8724 2.875 7.8724 3.33333V7.5H6.5474C5.80573 7.5 5.43073 8.4 5.95573 8.925L9.78073 12.75C10.1057 13.075 10.6307 13.075 10.9557 12.75L14.7807 8.925C15.3057 8.4 14.9391 7.5 14.1974 7.5ZM4.53906 15.8333C4.53906 16.2917 4.91406 16.6667 5.3724 16.6667H15.3724C15.8307 16.6667 16.2057 16.2917 16.2057 15.8333C16.2057 15.375 15.8307 15 15.3724 15H5.3724C4.91406 15 4.53906 15.375 4.53906 15.8333Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
