<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="19"
            height="18"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.50001 3.5625C2.98224 3.5625 2.56251 3.98223 2.56251 4.5V4.94098L9.02616 9.04833C9.02659 9.0486 9.02701 9.04886 9.02744 9.04913C9.16918 9.1376 9.33291 9.18451 9.50001 9.18451C9.66711 9.18451 9.83084 9.1376 9.97258 9.04913C9.97301 9.04886 9.97344 9.0486 9.97386 9.04833L16.4375 4.94098V4.5C16.4375 3.98223 16.0178 3.5625 15.5 3.5625H3.50001ZM17.5625 5.24051V4.5C17.5625 3.36091 16.6391 2.4375 15.5 2.4375H3.50001C2.36092 2.4375 1.43751 3.36091 1.43751 4.5V5.24051C1.43742 5.24649 1.43742 5.25246 1.43751 5.25843V13.5C1.43751 14.6391 2.36092 15.5625 3.50001 15.5625H15.5C16.6391 15.5625 17.5625 14.6391 17.5625 13.5V5.25843C17.5626 5.25246 17.5626 5.24649 17.5625 5.24051ZM16.4375 6.2739L10.5712 10.0017C10.2501 10.2028 9.87888 10.3095 9.50001 10.3095C9.12114 10.3095 8.74993 10.2028 8.42886 10.0017L8.42582 9.99977L2.56251 6.2739V13.5C2.56251 14.0178 2.98224 14.4375 3.50001 14.4375H15.5C16.0178 14.4375 16.4375 14.0178 16.4375 13.5V6.2739Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
