<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M5.7466 7.76354C5.46609 7.60107 5.13128 7.60107 4.85138 7.76323L2.11388 9.34344C1.83408 9.50547 1.6665 9.79572 1.6665 10.1194C1.6665 10.4424 1.83357 10.732 2.11388 10.8943L4.85081 12.4742C4.99136 12.5557 5.14553 12.5963 5.29943 12.5963C5.45258 12.5963 5.60541 12.556 5.74402 12.4757C6.02604 12.3151 6.19435 12.025 6.19435 11.6996V8.5382C6.19438 8.21454 6.02664 7.92476 5.7466 7.76354Z"
                :class="fillColor"
            />
            <path
                d="M8.29912 6.19439H11.4605C11.7852 6.19439 12.0751 6.02674 12.2357 5.74657C12.3975 5.46559 12.3973 5.13074 12.2355 4.85138L10.6553 2.11388C10.4932 1.83411 10.2032 1.6665 9.88027 1.6665C9.55664 1.6665 9.26669 1.83357 9.10433 2.11388L7.52439 4.85091C7.36229 5.13078 7.36209 5.46559 7.52442 5.74758C7.68655 6.02735 7.97614 6.19439 8.29912 6.19439Z"
                :class="fillColor"
            />
            <path
                d="M17.8849 9.10533L15.1479 7.52442C14.8673 7.36196 14.5325 7.36199 14.253 7.52392C13.972 7.68571 13.8043 7.97583 13.8043 8.30006V11.4605C13.8043 11.7847 13.972 12.0748 14.2537 12.237C14.3933 12.3171 14.5463 12.3571 14.6996 12.3571C14.8532 12.3571 15.007 12.3169 15.1474 12.2364L17.8848 10.6553C18.1656 10.4934 18.3332 10.2038 18.3332 9.8803C18.3332 9.55496 18.1648 9.26481 17.8849 9.10533Z"
                :class="fillColor"
            />
            <path
                d="M11.6996 13.8053H8.53822C8.21521 13.8053 7.92559 13.9723 7.76353 14.2521C7.60106 14.5325 7.60106 14.8674 7.76319 15.1473L9.3434 17.8857C9.50543 18.1655 9.79538 18.3332 10.1184 18.3332C10.442 18.3332 10.7319 18.1661 10.8944 17.8857L12.4743 15.1479C12.6367 14.8674 12.6367 14.5325 12.4743 14.2522C12.3122 13.9723 12.0226 13.8053 11.6996 13.8053Z"
                :class="fillColor"
            />
            <path
                d="M9.88024 12.9724C11.4707 12.9724 12.76 11.6831 12.76 10.0927C12.76 8.50226 11.4707 7.21296 9.88024 7.21296C8.28981 7.21296 7.00051 8.50226 7.00051 10.0927C7.00051 11.6831 8.28981 12.9724 9.88024 12.9724Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
