<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="19"
            height="18"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M17.4269 2.10138C17.1615 1.85817 16.7842 1.77712 16.4422 1.88981L1.93421 6.67227C1.53882 6.8026 1.2707 7.15554 1.25114 7.57136C1.23161 7.98721 1.46541 8.36378 1.84678 8.53068L5.43473 10.1005C5.45223 10.1817 6.48222 14.9589 6.51725 15.1212C6.56221 15.3298 6.66807 15.5156 6.89233 15.5727C7.11918 15.6305 7.27944 15.5065 7.4487 15.3798C7.54289 15.3093 10.1113 13.3861 10.1113 13.3861L13.224 15.9333C13.4053 16.0817 13.6269 16.1591 13.8528 16.1591C13.9604 16.1591 14.069 16.1416 14.1746 16.1057C14.5023 15.9947 14.7451 15.7273 14.8243 15.3905L17.7236 3.0638C17.806 2.71333 17.6924 2.3446 17.4269 2.10138ZM7.68063 11C7.67886 11.0042 7.67712 11.009 7.67547 11.0146L7.07529 13.1116L6.40549 10.0051L11.0112 7.44613L7.7911 10.8131C7.74038 10.8661 7.70306 10.9303 7.68063 11ZM7.87112 13.8529L8.14373 12.9004L8.40447 11.9894L9.33875 12.754L7.87112 13.8529ZM16.7803 2.84188L13.881 15.1686C13.8795 15.1747 13.8776 15.1832 13.8634 15.188C13.8494 15.1928 13.8425 15.1873 13.8377 15.1833L10.4317 12.3961C10.4316 12.396 10.4315 12.3959 10.4314 12.3959L8.85329 11.1045L13.9233 5.80328C14.0889 5.63013 14.103 5.36197 13.9566 5.17232C13.8102 4.98263 13.5471 4.92846 13.3377 5.04486L5.83053 9.21592L2.23524 7.64284C2.22428 7.63807 2.21826 7.6354 2.21913 7.61687C2.22 7.5984 2.22625 7.5963 2.23759 7.59257L16.7456 2.81014C16.7526 2.80785 16.7606 2.80521 16.7722 2.81584C16.7839 2.82654 16.782 2.83467 16.7803 2.84188Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
