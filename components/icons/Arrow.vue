<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M6.9107 4.4107C7.23614 4.08527 7.76378 4.08527 8.08921 4.4107L13.0892 9.4107C13.4147 9.73614 13.4147 10.2638 13.0892 10.5892L8.08921 15.5892C7.76378 15.9147 7.23614 15.9147 6.9107 15.5892C6.58527 15.2638 6.58527 14.7361 6.9107 14.4107L11.3214 9.99996L6.9107 5.58921C6.58527 5.26378 6.58527 4.73614 6.9107 4.4107Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-900',
    },
})
</script>
