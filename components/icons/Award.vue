<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="28"
            height="33"
            viewBox="0 0 28 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M7.05713 0.332275C6.09659 0.332275 5.55173 1.43239 6.13386 2.19643L10.5171 7.94946L8.45529 10.6556C7.87316 11.4197 8.41802 12.5198 9.37856 12.5198H13.4244C13.6287 12.5198 13.8263 12.466 13.9993 12.3674C14.1722 12.466 14.3699 12.5198 14.5741 12.5198H18.62C19.5805 12.5198 20.1254 11.4197 19.5432 10.6556L17.4814 7.94946L21.8647 2.19643C22.4468 1.43239 21.9019 0.332275 20.9414 0.332275H16.8956C16.5333 0.332275 16.1918 0.501401 15.9723 0.789547L13.9993 3.37915L12.0262 0.789547C11.8067 0.501401 11.4652 0.332275 11.103 0.332275H7.05713Z"
                :class="fillColorSecondary"
            />
            <path
                d="M0.650146 18.9051C0.650146 11.5331 6.62635 5.55688 13.9984 5.55688C21.3704 5.55688 27.3466 11.5331 27.3466 18.9051C27.3466 26.2771 21.3704 32.2533 13.9984 32.2533C6.62635 32.2533 0.650146 26.2771 0.650146 18.9051Z"
                :class="fillColorPrimary"
            />
            <path
                d="M13.5571 13.88C13.6961 13.4522 14.3013 13.4522 14.4403 13.88L15.4581 17.0124C15.5202 17.2037 15.6985 17.3332 15.8996 17.3332H19.1932C19.643 17.3332 19.83 17.9087 19.4661 18.1731L16.8015 20.109C16.6388 20.2273 16.5707 20.4368 16.6329 20.6281L17.6507 23.7605C17.7896 24.1883 17.3001 24.544 16.9362 24.2796L14.2716 22.3437C14.1089 22.2254 13.8885 22.2254 13.7258 22.3437L11.0612 24.2796C10.6974 24.544 10.2078 24.1883 10.3468 23.7605L11.3645 20.6281C11.4267 20.4368 11.3586 20.2273 11.1959 20.109L8.5313 18.1731C8.16744 17.9087 8.35444 17.3332 8.80421 17.3332H12.0978C12.2989 17.3332 12.4772 17.2037 12.5394 17.0124L13.5571 13.88Z"
                fill="white"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
