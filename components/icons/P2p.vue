<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.07893 2.29169C2.64406 2.29169 2.2915 2.64424 2.2915 3.07912V5.90398C2.2915 6.33885 2.64406 6.6914 3.07893 6.6914H8.72865C9.16352 6.6914 9.51608 6.33885 9.51608 5.90398V3.07912C9.51608 2.64425 9.16352 2.29169 8.72865 2.29169H3.07893ZM6.52879 7.9414H8.72865C9.85388 7.9414 10.7661 7.0292 10.7661 5.90398V3.07912C10.7661 1.95389 9.85388 1.04169 8.72865 1.04169H3.07893C1.95371 1.04169 1.0415 1.95389 1.0415 3.07912V5.90398C1.0415 7.0292 1.95371 7.9414 3.07893 7.9414H5.27879V9.51626H4.49136C4.14618 9.51626 3.86636 9.79608 3.86636 10.1413C3.86636 10.4864 4.14618 10.7663 4.49136 10.7663H5.90278C5.90312 10.7663 5.90346 10.7663 5.90379 10.7663C5.90413 10.7663 5.90446 10.7663 5.9048 10.7663H7.31622C7.6614 10.7663 7.94122 10.4864 7.94122 10.1413C7.94122 9.79608 7.6614 9.51626 7.31622 9.51626H6.52879V7.9414ZM12.1997 4.3503C12.1997 4.00513 12.4795 3.7253 12.8247 3.7253H14.2371C15.3624 3.7253 16.2746 4.63751 16.2746 5.76273V7.17516C16.2746 7.52034 15.9947 7.80016 15.6496 7.80016C15.3044 7.80016 15.0246 7.52034 15.0246 7.17516V5.76273C15.0246 5.32786 14.672 4.9753 14.2371 4.9753H12.8247C12.4795 4.9753 12.1997 4.69548 12.1997 4.3503ZM11.271 10.4838C10.8362 10.4838 10.4836 10.8363 10.4836 11.2712V14.0961C10.4836 14.5309 10.8362 14.8835 11.271 14.8835H16.9207C17.3556 14.8835 17.7082 14.5309 17.7082 14.0961V11.2712C17.7082 10.8363 17.3556 10.4838 16.9207 10.4838H11.271ZM14.7209 16.1335H16.9207C18.046 16.1335 18.9582 15.2213 18.9582 14.0961V11.2712C18.9582 10.146 18.046 9.23378 16.9207 9.23378H11.271C10.1458 9.23378 9.23359 10.146 9.23359 11.2712V14.0961C9.23359 15.2213 10.1458 16.1335 11.271 16.1335H13.4709V17.7084H12.6835C12.3383 17.7084 12.0585 17.9882 12.0585 18.3334C12.0585 18.6785 12.3383 18.9584 12.6835 18.9584H15.5083C15.8535 18.9584 16.1333 18.6785 16.1333 18.3334C16.1333 17.9882 15.8535 17.7084 15.5083 17.7084H14.7209V16.1335ZM4.35012 12.1999C4.6953 12.1999 4.97512 12.4797 4.97512 12.8249V14.2373C4.97512 14.6722 5.32768 15.0247 5.76255 15.0247H7.17498C7.52016 15.0247 7.79998 15.3046 7.79998 15.6497C7.79998 15.9949 7.52016 16.2747 7.17498 16.2747H5.76255C4.63732 16.2747 3.72512 15.3625 3.72512 14.2373V12.8249C3.72512 12.4797 4.00494 12.1999 4.35012 12.1999Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-green-200',
    },
})
</script>
