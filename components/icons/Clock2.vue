<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M11.1672 0.981898C11.614 0.449455 12.4078 0.380005 12.9403 0.826777L15.3504 2.84916C15.8829 3.29593 15.9523 4.08974 15.5056 4.62219L14.2921 6.06837C13.8453 6.60082 13.0515 6.67027 12.519 6.22349L10.1089 4.20111C9.57642 3.75434 9.50697 2.96053 9.95374 2.42808L11.1672 0.981898Z"
                :class="fillColorSecondary"
            />
            <path
                d="M0.82962 4.6221C0.382848 4.08966 0.452298 3.29585 0.98474 2.84907L3.39492 0.826691C3.92737 0.379918 4.72118 0.449368 5.16795 0.981811L6.38037 2.42672C6.82715 2.95916 6.7577 3.75297 6.22525 4.19975L3.81507 6.22213C3.28263 6.6689 2.48882 6.59945 2.04204 6.06701L0.82962 4.6221Z"
                :class="fillColorSecondary"
            />
            <path
                d="M0.965332 8.60068C0.965332 4.60412 4.20519 1.36426 8.20175 1.36426C12.1983 1.36426 15.4382 4.60412 15.4382 8.60068C15.4382 12.5972 12.1983 15.8371 8.20175 15.8371C4.20519 15.8371 0.965332 12.5972 0.965332 8.60068Z"
                :class="fillColorPrimary"
            />
            <path
                d="M8.14908 4.39941C7.73532 4.39941 7.3999 4.7408 7.3999 5.16191V9.63692C7.3999 10.058 7.73532 10.3994 8.14908 10.3994H11.4507C11.8645 10.3994 12.1999 10.058 12.1999 9.63692C12.1999 9.2158 11.8645 8.87442 11.4507 8.87442H9.36255C9.10613 8.87442 8.89826 8.66655 8.89826 8.41013V5.16191C8.89826 4.7408 8.56284 4.39941 8.14908 4.39941Z"
                fill="white"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
