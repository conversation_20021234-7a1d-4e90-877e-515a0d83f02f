<template>
    <svg :width="w" :height="h" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M12.0005 5.75061C10.0201 5.75061 8.08425 6.33865 6.43843 7.44018C4.7975 8.53843 3.51854 10.0978 2.7626 11.9216C2.7458 11.9725 2.7458 12.0275 2.7626 12.0784C3.51854 13.9022 4.7975 15.4616 6.43843 16.5598C8.08425 17.6613 10.0201 18.2494 12.0005 18.2494C13.9809 18.2494 15.9168 17.6613 17.5626 16.5598C19.2035 15.4616 20.4825 13.9022 21.2384 12.0784C21.2552 12.0275 21.2552 11.9725 21.2384 11.9216C20.4825 10.0978 19.2035 8.53843 17.5626 7.44018C15.9168 6.33865 13.9809 5.75061 12.0005 5.75061ZM5.60412 6.19361C7.49681 4.92685 9.72302 4.25061 12.0005 4.25061C14.278 4.25061 16.5042 4.92685 18.3969 6.19361C20.2896 7.46037 21.7635 9.26059 22.6319 11.366C22.6353 11.3743 22.6385 11.3826 22.6416 11.391C22.7875 11.7839 22.7875 12.2161 22.6416 12.609C22.6385 12.6174 22.6353 12.6257 22.6319 12.6339C21.7635 14.7394 20.2896 16.5396 18.3969 17.8064C16.5042 19.0731 14.278 19.7494 12.0005 19.7494C9.72302 19.7494 7.49681 19.0731 5.60412 17.8064C3.71143 16.5396 2.23749 14.7394 1.36916 12.6339C1.36575 12.6257 1.36249 12.6174 1.35938 12.609C1.21354 12.2161 1.21354 11.7839 1.35938 11.391C1.36249 11.3826 1.36575 11.3743 1.36916 11.366C2.23749 9.26059 3.71143 7.46037 5.60412 6.19361ZM12.0005 9.74999C10.7579 9.74999 9.75051 10.7574 9.75051 12C9.75051 13.2426 10.7579 14.25 12.0005 14.25C13.2431 14.25 14.2505 13.2426 14.2505 12C14.2505 10.7574 13.2431 9.74999 12.0005 9.74999ZM8.25051 12C8.25051 9.92893 9.92944 8.24999 12.0005 8.24999C14.0716 8.24999 15.7505 9.92893 15.7505 12C15.7505 14.0711 14.0716 15.75 12.0005 15.75C9.92944 15.75 8.25051 14.0711 8.25051 12Z"
            :fill="color"
        />
    </svg>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    color: {
        type: String,
        default: '#333',
    },
})
</script>
