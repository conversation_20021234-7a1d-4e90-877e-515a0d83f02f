<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="16"
            viewBox="0 0 20 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <g clip-path="url(#clip0_11655_70246)">
                <g filter="url(#filter0_b_11655_70246)">
                    <path
                        d="M0.400391 2.66667C0.400391 1.19391 1.62841 0 3.14325 0H16.8575C18.3724 0 19.6004 1.19391 19.6004 2.66667V13.3333C19.6004 14.8061 18.3724 16 16.8575 16H3.14325C1.62841 16 0.400391 14.8061 0.400391 13.3333V2.66667Z"
                        :class="fillColorSecondary"
                    />
                </g>
                <path
                    d="M15.4874 2.33203H4.51596C3.75854 2.33203 3.14453 2.92898 3.14453 3.66536V4.33203H16.8588V3.66536C16.8588 2.92898 16.2448 2.33203 15.4874 2.33203Z"
                    fill="white"
                />
                <g filter="url(#filter1_b_11655_70246)">
                    <path
                        d="M0.400391 5.66536C0.400391 4.92898 1.0144 4.33203 1.77182 4.33203H18.229C18.9864 4.33203 19.6004 4.92898 19.6004 5.66536V11.9987C19.6004 14.2078 17.7584 15.9987 15.4861 15.9987H4.51468C2.24242 15.9987 0.400391 14.2078 0.400391 11.9987V5.66536Z"
                        :class="fillColorSecondary"
                    />
                </g>
                <path
                    d="M3.14453 8.66536C3.14453 7.92898 3.75854 7.33203 4.51596 7.33203H15.4874C16.2448 7.33203 16.8588 7.92898 16.8588 8.66536V9.33203C16.8588 10.0684 16.2448 10.6654 15.4874 10.6654H4.51596C3.75854 10.6654 3.14453 10.0684 3.14453 9.33203V8.66536Z"
                    fill="white"
                />
                <path
                    d="M8.93716 9.7112C8.60338 9.5149 8.24411 9.33203 7.85401 9.33203H1.77182C1.0144 9.33203 0.400391 9.92898 0.400391 10.6654V13.332C0.400391 14.8048 1.62841 15.9987 3.14325 15.9987H16.8575C18.3724 15.9987 19.6004 14.8048 19.6004 13.332V10.6654C19.6004 9.92898 18.9864 9.33203 18.229 9.33203H12.1468C11.7567 9.33203 11.3974 9.5149 11.0636 9.7112C10.7533 9.89368 10.3895 9.9987 10.0004 9.9987C9.6113 9.9987 9.24744 9.89368 8.93716 9.7112Z"
                    :class="fillColorPrimary"
                />
                <path
                    d="M7.94263 12.6667C7.94263 12.2985 8.24963 12 8.62834 12H11.3712C11.7499 12 12.0569 12.2985 12.0569 12.6667C12.0569 13.0349 11.7499 13.3333 11.3712 13.3333H8.62834C8.24963 13.3333 7.94263 13.0349 7.94263 12.6667Z"
                    fill="white"
                />
            </g>
            <defs>
                <filter
                    id="filter0_b_11655_70246"
                    x="-5.59961"
                    y="-6"
                    width="31.2"
                    height="28"
                    filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB"
                >
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feGaussianBlur in="BackgroundImageFix" stdDeviation="3" />
                    <feComposite
                        in2="SourceAlpha"
                        operator="in"
                        result="effect1_backgroundBlur_11655_70246"
                    />
                    <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_backgroundBlur_11655_70246"
                        result="shape"
                    />
                </filter>
                <filter
                    id="filter1_b_11655_70246"
                    x="-5.59961"
                    y="-1.66797"
                    width="31.2"
                    height="23.6666"
                    filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB"
                >
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feGaussianBlur in="BackgroundImageFix" stdDeviation="3" />
                    <feComposite
                        in2="SourceAlpha"
                        operator="in"
                        result="effect1_backgroundBlur_11655_70246"
                    />
                    <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_backgroundBlur_11655_70246"
                        result="shape"
                    />
                </filter>
                <clipPath id="clip0_11655_70246">
                    <rect width="19.2" height="16" fill="white" transform="translate(0.400391)" />
                </clipPath>
            </defs>
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
