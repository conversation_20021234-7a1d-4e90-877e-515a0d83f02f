<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M4.39083 17.2077C5.19241 18.4472 6.27816 19.4777 7.55791 20.2134C8.83766 20.9492 10.2745 21.369 11.749 21.438C13.2236 21.507 14.6933 21.2232 16.0362 20.6101C17.379 19.9971 18.5563 19.0726 19.4701 17.9133C20.3839 16.7539 21.008 15.3933 21.2904 13.9445C21.5729 12.4956 21.5056 11.0002 21.0941 9.58252C20.6827 8.16486 19.9389 6.86582 18.9246 5.79328C17.9104 4.72075 16.6549 3.90568 15.2624 3.41576L14.1583 6.55372C15.0516 6.86801 15.8571 7.39088 16.5077 8.07892C17.1584 8.76696 17.6355 9.6003 17.8995 10.5097C18.1634 11.4192 18.2066 12.3785 18.0254 13.308C17.8442 14.2374 17.4439 15.1103 16.8576 15.854C16.2714 16.5977 15.5162 17.1908 14.6548 17.584C13.7933 17.9773 12.8505 18.1594 11.9045 18.1151C10.9586 18.0708 10.0369 17.8015 9.21588 17.3295C8.39491 16.8576 7.6984 16.1965 7.18418 15.4013L4.39083 17.2077Z"
                :class="fillColor"
            />
            <path
                d="M17.8958 7.13319C18.5849 6.52577 18.6586 5.46037 17.938 4.89062C17.3314 4.41091 16.6654 4.00758 15.9548 3.6914C14.7359 3.14907 13.4143 2.87595 12.0803 2.89067C10.7462 2.9054 9.43104 3.20762 8.22439 3.77673C7.01774 4.34585 5.948 5.16847 5.0881 6.18851C4.22821 7.20854 3.59839 8.40201 3.24157 9.68754C2.88476 10.9731 2.80935 12.3204 3.02049 13.6377C3.23162 14.955 3.72433 16.2113 4.46503 17.321C5.20573 18.4306 6.177 19.3674 7.3126 20.0676L9.0585 17.2361C8.32999 16.7869 7.70692 16.1859 7.23176 15.4741C6.75659 14.7623 6.44052 13.9564 6.30507 13.1113C6.16963 12.2662 6.218 11.4019 6.4469 10.5772C6.6758 9.75254 7.07984 8.98692 7.63146 8.33256C8.18309 7.6782 8.86934 7.15048 9.64341 6.78539C10.4175 6.4203 11.2612 6.22642 12.117 6.21698C12.9728 6.20753 13.8206 6.38274 14.6025 6.73065C14.8757 6.85222 15.1387 6.99389 15.3893 7.15415C16.1633 7.64899 17.2067 7.74062 17.8958 7.13319Z"
                :class="fillColor"
            />
            <path
                d="M5.44199 15.7128C4.62891 16.1403 4.30563 17.1582 4.8712 17.882C5.9634 19.2799 7.44156 20.3452 9.13926 20.9347C11.3645 21.7074 13.8004 21.6049 15.9528 20.6479C18.1053 19.6909 19.8132 17.9511 20.7302 15.7813C21.6473 13.6115 21.7048 11.1742 20.891 8.96361L17.7693 10.1127C18.2913 11.5308 18.2544 13.0944 17.6662 14.4863C17.0779 15.8783 15.9822 16.9943 14.6014 17.6083C13.2206 18.2222 11.658 18.2879 10.2305 17.7922C9.33197 17.4802 8.52933 16.9605 7.88249 16.2846C7.24736 15.621 6.25507 15.2854 5.44199 15.7128Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
