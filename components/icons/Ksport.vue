<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M15.5191 8.13849H19.4136L19.6126 5.74892C19.6126 5.74892 20.0107 4.45457 17.5702 4.3633H10.7333C10.7333 4.3633 5.7397 4.31352 4.54539 9.67345L4.19922 13.4072H8.336L8.6389 9.86429C8.6389 9.86429 9.06297 6.99348 11.9189 6.6616H15.7701L15.5191 8.13849Z"
                :class="fillColor"
            />
            <path
                d="M11.0749 10.48H18.9574C18.9574 10.48 19.5671 19.6034 12.3292 19.6034H6.14516C6.14516 19.6034 3.63672 19.9906 3.63672 18.0969V15.8665H7.5039V17.272H11.9633C11.9633 17.272 14.4892 17.171 14.4892 13.2406H10.7265L11.0749 10.48Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
