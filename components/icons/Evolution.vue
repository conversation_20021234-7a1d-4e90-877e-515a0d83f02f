<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M15.5882 10.6225C16.372 10.6496 17.2319 10.6267 18.1813 10.5455C18.0445 8.47906 17.4225 6.76241 16.4904 5.43198C16.5347 5.79633 16.5598 6.16694 16.5598 6.54483C16.5598 8.02413 16.2064 9.4139 15.5882 10.6225Z"
                :class="fillColor"
            />
            <path
                d="M15.2599 11.2085C13.8618 13.4842 11.471 14.9843 8.75472 14.9843C8.56119 14.9843 8.36957 14.9739 8.17989 14.9593C7.48662 16.1086 7.03888 17.3849 6.54492 18.5852C11.4171 21.3981 17.7085 18.4322 18.1813 11.271C17.0971 11.1783 16.1294 11.1617 15.2599 11.2085Z"
                :class="fillColor"
            />
            <path
                d="M6.54492 3.23222C7.89969 6.52811 8.91453 10.3966 15.5882 10.6225C16.2064 9.4139 16.5598 8.02413 16.5598 6.54483C16.5598 6.16694 16.5347 5.79633 16.4904 5.43198C14.193 2.15587 10.0103 1.23137 6.54492 3.23222Z"
                :class="fillColor"
            />
            <path
                d="M15.2607 11.2086C11.2446 11.4231 9.3583 13.0065 8.1807 14.9594C8.37039 14.974 8.562 14.9844 8.75554 14.9844C11.4718 14.9844 13.8626 13.4833 15.2607 11.2086Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
