<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M9.4124 10.1265C9.4124 10.2527 9.4124 10.3788 9.4124 10.505C9.4124 13.1788 9.40638 15.8557 9.42142 18.5296C9.42142 18.8557 9.32217 18.9142 9.03644 18.908C8.34466 18.8896 7.65289 18.8865 6.96112 18.908C6.66336 18.9172 6.58516 18.8403 6.58516 18.5296C6.59719 14.1634 6.59117 9.79729 6.59418 5.43115C6.59418 5.09577 6.44981 4.64654 6.66035 4.4527C6.84081 4.28654 7.26791 4.40346 7.58672 4.40039C7.99577 4.39731 8.40783 4.4127 8.81687 4.39423C9.04847 4.385 9.18081 4.46193 9.30713 4.665C11.0696 7.48345 12.8382 10.2988 14.5375 13.1757C14.6548 13.0803 14.5947 12.9542 14.5947 12.8527C14.5977 10.1634 14.6007 7.4773 14.5947 4.78808C14.5947 4.49577 14.6398 4.37885 14.9616 4.39116C15.6654 4.41885 16.3722 4.4127 17.076 4.39116C17.3708 4.38193 17.455 4.45885 17.455 4.77269C17.443 8.20652 17.446 11.6434 17.449 15.0773C17.449 16.2434 17.443 17.4126 17.455 18.5788C17.455 18.8249 17.4069 18.9172 17.1452 18.908C16.5046 18.8865 15.8639 18.8865 15.2233 18.908C14.9736 18.9172 14.8383 18.8342 14.7029 18.6188C12.9585 15.788 11.1869 12.9788 9.4124 10.1296V10.1265Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
