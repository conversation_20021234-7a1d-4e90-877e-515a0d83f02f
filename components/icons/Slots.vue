<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M22.8914 9.30588L19.2967 3.53033C19.1653 3.31944 18.9346 3.19122 18.6862 3.19122H5.31384C5.06556 3.19122 4.83478 3.31944 4.70343 3.53033L1.10869 9.30588C0.936129 9.58316 0.971836 9.94169 1.19592 10.1792L11.4769 21.083C11.6127 21.227 11.8021 21.3087 12 21.3087C12.198 21.3087 12.3873 21.227 12.5232 21.0827L22.8041 10.1792C23.028 9.94143 23.064 9.58316 22.8914 9.30588ZM12.0001 19.5418L2.62208 9.59562L5.71331 4.62912H18.287L21.378 9.59562L12.0001 19.5418Z"
                :class="fillColor"
            />
            <path d="M22.281 8.96678H1.7191V10.4047H22.281V8.96678Z" :class="fillColor" />
            <path
                d="M8.38517 9.61697L9.4938 4.05063L8.08345 3.76975L6.93314 9.5453C6.90607 9.6819 6.91901 9.82331 6.97078 9.95273L11.3324 20.8568L12.6675 20.3229L8.38517 9.61697Z"
                :class="fillColor"
            />
            <path
                d="M17.1127 9.48755L15.9145 3.87975L14.5085 4.18027L15.6609 9.5731L11.3334 20.3214L12.667 20.8582L17.0768 9.90645C17.13 9.77347 17.1427 9.6275 17.1127 9.48755Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
