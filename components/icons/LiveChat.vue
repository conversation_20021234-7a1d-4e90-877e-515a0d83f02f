<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M11.5962 10.497C11.5962 10.7138 11.5319 10.9258 11.4114 11.1061C11.291 11.2863 11.1198 11.4269 10.9195 11.5098C10.7192 11.5928 10.4988 11.6145 10.2862 11.5722C10.0735 11.5299 9.8782 11.4255 9.7249 11.2722C9.5716 11.1189 9.4672 10.9235 9.42491 10.7109C9.38261 10.4982 9.40432 10.2778 9.48729 10.0775C9.57025 9.87718 9.71075 9.70597 9.89101 9.58551C10.0713 9.46505 10.2832 9.40075 10.5 9.40075C10.7907 9.40075 11.0695 9.51625 11.2751 9.72184C11.4807 9.92743 11.5962 10.2063 11.5962 10.497ZM6.48077 9.40075C6.26397 9.40075 6.05204 9.46505 5.87178 9.58551C5.69152 9.70597 5.55102 9.87718 5.46805 10.0775C5.38509 10.2778 5.36338 10.4982 5.40568 10.7109C5.44797 10.9235 5.55237 11.1189 5.70567 11.2722C5.85897 11.4255 6.05429 11.5299 6.26692 11.5722C6.47955 11.6145 6.69995 11.5928 6.90025 11.5098C7.10054 11.4269 7.27174 11.2863 7.39219 11.1061C7.51264 10.9258 7.57692 10.7138 7.57692 10.497C7.57692 10.2063 7.46144 9.92743 7.25587 9.72184C7.0503 9.51625 6.77149 9.40075 6.48077 9.40075ZM14.5192 9.40075C14.3024 9.40075 14.0905 9.46505 13.9102 9.58551C13.73 9.70597 13.5895 9.87718 13.5065 10.0775C13.4236 10.2778 13.4018 10.4982 13.4441 10.7109C13.4864 10.9235 13.5908 11.1189 13.7441 11.2722C13.8974 11.4255 14.0927 11.5299 14.3054 11.5722C14.518 11.6145 14.7384 11.5928 14.9387 11.5098C15.139 11.4269 15.3102 11.2863 15.4306 11.1061C15.5511 10.9258 15.6154 10.7138 15.6154 10.497C15.6154 10.2063 15.4999 9.92743 15.2943 9.72184C15.0888 9.51625 14.8099 9.40075 14.5192 9.40075ZM20 10.497C20.0004 12.1373 19.5761 13.7498 18.7684 15.1774C17.9608 16.6051 16.7974 17.7993 15.3913 18.6439C13.9853 19.4884 12.3845 19.9545 10.7449 19.9968C9.10533 20.0391 7.48271 19.6562 6.035 18.8852L2.92466 19.9221C2.66715 20.008 2.3908 20.0205 2.1266 19.9581C1.86239 19.8957 1.62078 19.761 1.42883 19.5691C1.23687 19.3771 1.10217 19.1354 1.03982 18.8712C0.977474 18.607 0.989938 18.3306 1.07582 18.0731L2.1126 14.9624C1.43494 13.6883 1.05645 12.2764 1.00584 10.8341C0.955238 9.39176 1.23385 7.95686 1.82053 6.6383C2.40722 5.31975 3.28655 4.15218 4.39179 3.22424C5.49703 2.2963 6.79912 1.63237 8.19923 1.28284C9.59935 0.933318 11.0607 0.907385 12.4723 1.20701C13.8839 1.50664 15.2088 2.12395 16.3462 3.0121C17.4837 3.90024 18.4039 5.03587 19.037 6.33278C19.6701 7.62969 19.9994 9.0538 20 10.497ZM18.5385 10.497C18.5381 9.26384 18.2541 8.04727 17.7084 6.94143C17.1627 5.83559 16.3699 4.87013 15.3914 4.11974C14.4129 3.36935 13.2749 2.85415 12.0655 2.61399C10.856 2.37383 9.60753 2.41515 8.41661 2.73477C7.22568 3.05438 6.12424 3.64371 5.19749 4.45717C4.27075 5.27062 3.54355 6.2864 3.07215 7.4259C2.60075 8.56541 2.3978 9.79809 2.47898 11.0286C2.56016 12.2591 2.92332 13.4544 3.54034 14.5221C3.59212 14.6117 3.62428 14.7114 3.63467 14.8144C3.64506 14.9174 3.63346 15.0214 3.60062 15.1196L2.46154 18.5362L5.87788 17.3971C5.9523 17.3717 6.03037 17.3587 6.10899 17.3587C6.23734 17.3589 6.36336 17.3929 6.47437 17.4573C7.6964 18.1644 9.0831 18.5372 10.4949 18.5381C11.9067 18.539 13.2939 18.168 14.5168 17.4624C15.7398 16.7569 16.7553 15.7416 17.4613 14.5189C18.1673 13.2961 18.5388 11.909 18.5385 10.497Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-white',
    },
})
</script>
