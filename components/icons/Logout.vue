<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M5.82857 5.04286C5.46016 5.04286 5.1225 5.17377 4.88646 5.38358C4.65339 5.59076 4.54286 5.85091 4.54286 6.1V18.9C4.54286 19.1491 4.65339 19.4092 4.88646 19.6164C5.1225 19.8262 5.46017 19.9571 5.82857 19.9571H12C12.426 19.9571 12.7714 20.3025 12.7714 20.7286C12.7714 21.1546 12.426 21.5 12 21.5H5.82857C5.1058 21.5 4.39699 21.2456 3.86144 20.7696C3.32294 20.2909 3 19.6208 3 18.9V6.1C3 5.37915 3.32294 4.7091 3.86144 4.23043C4.39699 3.75439 5.1058 3.5 5.82857 3.5H12C12.426 3.5 12.7714 3.84538 12.7714 4.27143C12.7714 4.69748 12.426 5.04286 12 5.04286H5.82857Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M16.5974 7.84023C16.8986 7.53897 17.3871 7.53897 17.6883 7.84023L21.8026 11.9545C22.1039 12.2558 22.1039 12.7442 21.8026 13.0455L17.6883 17.1598C17.3871 17.461 16.8986 17.461 16.5974 17.1598C16.2961 16.8585 16.2961 16.3701 16.5974 16.0688L20.1662 12.5L16.5974 8.9312C16.2961 8.62994 16.2961 8.14149 16.5974 7.84023Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.14286 12.5C8.14286 12.074 8.48824 11.7286 8.91429 11.7286H21.2571C21.6832 11.7286 22.0286 12.074 22.0286 12.5C22.0286 12.926 21.6832 13.2714 21.2571 13.2714H8.91429C8.48824 13.2714 8.14286 12.926 8.14286 12.5Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
