<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M13.2554 1.25H6.74373C5.05885 1.25 3.69141 2.61744 3.69141 4.30233V15.6977C3.69141 17.3826 5.05885 18.75 6.74373 18.75H13.2554C14.9402 18.75 16.3077 17.3826 16.3077 15.6977V4.30233C16.3077 2.61744 14.9402 1.25 13.2554 1.25ZM15.0868 15.6977C15.0868 16.707 14.2647 17.5291 13.2554 17.5291H6.74373C5.73443 17.5291 4.91234 16.707 4.91234 15.6977V4.30233C4.91234 3.29302 5.73443 2.47093 6.74373 2.47093H13.2554C14.2647 2.47093 15.0868 3.29302 15.0868 4.30233V15.6977ZM12.2623 9.08837C12.4984 9.56046 12.5391 10.0977 12.3681 10.5942C12.1077 11.3674 11.4077 11.8884 10.61 11.9291V12.4337C10.61 12.7674 10.3333 13.0442 9.99955 13.0442C9.66582 13.0442 9.38908 12.7674 9.38908 12.4337V11.9128C8.55071 11.7988 7.84257 11.2291 7.57396 10.407C7.46815 10.0895 7.63908 9.73953 7.95652 9.63372C8.27396 9.52791 8.62396 9.69884 8.72978 10.0163C8.86815 10.4314 9.25071 10.7081 9.69024 10.7081H10.5042C10.8298 10.7081 11.1147 10.5047 11.2123 10.1953C11.2775 10.0081 11.2612 9.80465 11.1716 9.62558C11.0821 9.44651 10.9275 9.31628 10.7402 9.25116L8.87629 8.63256C7.84257 8.2907 7.28908 7.16744 7.63094 6.14186C7.89141 5.3686 8.58327 4.84767 9.38908 4.80698V4.30233C9.38908 3.9686 9.66582 3.69186 9.99955 3.69186C10.3333 3.69186 10.61 3.9686 10.61 4.30233V4.82326C11.4484 4.93721 12.1565 5.50698 12.4251 6.32907C12.5309 6.64651 12.36 6.99651 12.0426 7.10232C11.717 7.20814 11.3751 7.03721 11.2693 6.71977C11.1309 6.30465 10.7484 6.02791 10.3088 6.02791H9.49489C9.16931 6.02791 8.88443 6.2314 8.78676 6.5407C8.65652 6.9314 8.86815 7.35465 9.25885 7.48488L11.1228 8.10349C11.6193 8.26628 12.0263 8.62442 12.2623 9.08837ZM12.2379 15.6977C12.2379 16.0314 11.9612 16.3081 11.6275 16.3081H8.37164C8.03792 16.3081 7.76117 16.0314 7.76117 15.6977C7.76117 15.364 8.03792 15.0872 8.37164 15.0872H11.6275C11.9612 15.0872 12.2379 15.364 12.2379 15.6977Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-green-200',
    },
})
</script>
