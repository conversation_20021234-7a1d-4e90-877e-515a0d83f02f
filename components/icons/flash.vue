<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M4.87619 9.88069L10.7077 2.06368C11.2822 1.29363 12.4603 1.71978 12.4603 2.69761V7.42882C12.4603 7.99691 12.8993 8.45743 13.4408 8.45743H15.3504C16.1676 8.45743 16.6262 9.44477 16.1225 10.12L10.291 17.937C9.71654 18.707 8.53837 18.2809 8.53837 17.303V12.5718C8.53837 12.0037 8.09939 11.5432 7.55788 11.5432H5.64833C4.83105 11.5432 4.37249 10.5559 4.87619 9.88069Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
