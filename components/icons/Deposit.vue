<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M19.9956 7.1649C20.5533 7.1649 21.027 7.3539 21.4166 7.72956C21.8051 8.10522 21.9999 8.56371 21.9999 9.10271V11.9995L21.9976 13.1137H14.2499V10.8795L10.7499 14.2815L14.2499 17.8339V15.447H21.9976V15.853L21.9999 19.7286C22.0019 19.9875 21.9532 20.2443 21.8564 20.4846C21.7108 20.8393 21.4615 21.1418 21.1412 21.3527C20.8208 21.5635 20.4444 21.6728 20.061 21.6664H4.07084C3.53221 21.6693 3.01259 21.4675 2.6172 21.1017C2.42831 20.927 2.275 20.7175 2.1657 20.4846C2.0549 20.248 1.9983 19.9898 2.00004 19.7286V9.10271C2.00004 8.56371 2.19487 8.10522 2.58336 7.72956C2.97303 7.3539 3.44669 7.1649 4.00435 7.1649H19.9956ZM15.8657 3.00229C16.2694 3.02329 16.6112 3.09095 16.89 3.20412C17.2132 3.36045 17.492 3.56578 17.7265 3.82244L19.004 6.18608H9.00461C9.79794 5.78825 10.5458 5.40442 11.2516 5.03576C11.8676 4.7231 12.4766 4.4116 13.0797 4.09894C13.6817 3.78628 14.1507 3.54478 14.489 3.37561C15.0024 3.10495 15.462 2.98129 15.8657 3.00229Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
