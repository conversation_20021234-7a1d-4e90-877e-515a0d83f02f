<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M2.3999 5.314L4.57665 4.00795V9.23214L7.18874 6.62005L9.80083 9.23214V4.00795L11.9776 5.314V9.66749L10.4539 11.4089H9.80083L7.18874 8.79679L4.57665 11.4089H4.1413L2.3999 9.66749V5.314Z"
                :class="fillColor"
            />
            <path
                d="M14.5891 5.09629H12.4124V9.66746L14.5891 11.4089V5.09629Z"
                :class="fillColor"
            />
            <path
                d="M15.0244 11.4089V4.87862L18.9426 8.79676V4.87862L21.1193 6.62002V10.5382L20.4663 11.4089H18.9426L16.7658 8.79676V11.4089H15.0244Z"
                :class="fillColor"
            />
            <path
                d="M5.01164 14.8917H5.44699V14.4563H8.92978L4.14095 22.2926V22.7279H7.62374V22.2926L7.18839 21.8572L11.9772 14.021V13.5856L11.1065 12.2796H2.8349V12.7149L5.01164 14.8917Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13.719 12.2796L11.5422 15.7624V16.1977L12.4129 18.3744H16.7664L15.8957 20.1158H11.1069V19.6805H10.6715L9.36548 21.8572V22.2926L9.80083 22.7279H16.3311L17.6371 21.4219L21.1199 14.021V13.5856L19.8139 12.2796H13.719ZM17.6371 16.1977L18.2901 14.8917V14.4563H15.025L14.1543 15.7624L14.372 16.1977H17.6371Z"
                :class="fillColor"
            />
            <path
                d="M13.4138 1.07943C13.4573 1.0204 13.5455 1.0204 13.589 1.07943L14.3228 2.07516C14.3293 2.08396 14.3371 2.09173 14.3459 2.09822L15.3416 2.83198C15.4006 2.87548 15.4006 2.96372 15.3416 3.00722L14.3459 3.74098C14.3371 3.74746 14.3293 3.75524 14.3228 3.76403L13.589 4.75977C13.5455 4.8188 13.4573 4.8188 13.4138 4.75977L12.68 3.76403C12.6736 3.75524 12.6658 3.74746 12.657 3.74098L11.6613 3.00722C11.6022 2.96372 11.6022 2.87548 11.6613 2.83198L12.657 2.09822C12.6658 2.09173 12.6736 2.08396 12.68 2.07516L13.4138 1.07943Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
