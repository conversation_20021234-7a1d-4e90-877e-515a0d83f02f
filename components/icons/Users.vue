<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M13.3337 17.5V15.8333C13.3337 14.9493 12.9825 14.1014 12.3573 13.4763C11.7322 12.8512 10.8844 12.5 10.0003 12.5H5.00033C4.11627 12.5 3.26842 12.8512 2.6433 13.4763C2.01818 14.1014 1.66699 14.9493 1.66699 15.8333V17.5M18.3337 17.5V15.8333C18.3331 15.0948 18.0873 14.3773 17.6348 13.7936C17.1823 13.2099 16.5488 12.793 15.8337 12.6083M13.3337 2.60833C14.0507 2.79192 14.6862 3.20892 15.14 3.79359C15.5939 4.37827 15.8402 5.09736 15.8402 5.8375C15.8402 6.57764 15.5939 7.29673 15.14 7.88141C14.6862 8.46608 14.0507 8.88308 13.3337 9.06667M10.8337 5.83333C10.8337 7.67428 9.34127 9.16667 7.50033 9.16667C5.65938 9.16667 4.16699 7.67428 4.16699 5.83333C4.16699 3.99238 5.65938 2.5 7.50033 2.5C9.34127 2.5 10.8337 3.99238 10.8337 5.83333Z"
                :class="strokelColor"
                stroke-width="1.66667"
                stroke-linecap="round"
                stroke-linejoin="round"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    strokelColor: {
        type: String,
        default: 'stroke-white',
    },
})
</script>
