<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="19"
            height="19"
            viewBox="0 0 19 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M0 3.06667C0 1.37299 1.37299 0 3.06667 0H15.3333C17.027 0 18.4 1.37299 18.4 3.06667V15.3333C18.4 17.027 17.027 18.4 15.3333 18.4H3.06667C1.37299 18.4 0 17.027 0 15.3333V3.06667Z"
                :class="fillColor"
            />
            <path
                d="M9.19936 9.5462C7.48048 9.5462 6.08658 8.13822 6.08658 6.40198V5.77313C6.08658 4.03689 7.48048 2.62891 9.19936 2.62891C10.9182 2.62891 12.3121 4.03689 12.3121 5.77313V6.40198C12.3121 8.13822 10.9182 9.5462 9.19936 9.5462Z"
                fill="white"
            />
            <path
                d="M15.7708 14.3247C15.7708 13.0796 14.9842 11.9845 13.8427 11.6574C12.5981 11.2997 10.8987 10.9297 9.19936 10.9297C7.49999 10.9297 5.80062 11.2997 4.55599 11.6574C3.41453 11.9845 2.62793 13.0796 2.62793 14.3247C2.62793 15.1239 3.27582 15.7718 4.07503 15.7718H14.3237C15.1229 15.7718 15.7708 15.1239 15.7708 14.3247Z"
                fill="white"
            />
            <path
                d="M9.19936 9.5462C7.48048 9.5462 6.08658 8.13822 6.08658 6.40198V5.77313C6.08658 4.03689 7.48048 2.62891 9.19936 2.62891C10.9182 2.62891 12.3121 4.03689 12.3121 5.77313V6.40198C12.3121 8.13822 10.9182 9.5462 9.19936 9.5462Z"
                fill="white"
            />
            <path
                d="M15.7708 14.3247C15.7708 13.0796 14.9842 11.9845 13.8427 11.6574C12.5981 11.2997 10.8987 10.9297 9.19936 10.9297C7.49999 10.9297 5.80062 11.2997 4.55599 11.6574C3.41453 11.9845 2.62793 13.0796 2.62793 14.3247C2.62793 15.1239 3.27582 15.7718 4.07503 15.7718H14.3237C15.1229 15.7718 15.7708 15.1239 15.7708 14.3247Z"
                fill="white"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
