<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M5.88926 15.4785C5.78941 15.3342 5.82424 15.2139 5.82424 15.1009C5.82424 11.5053 5.82192 7.91215 5.82424 4.31658C5.82192 3.50607 6.39778 2.89038 7.11297 2.90962C7.25925 2.91443 7.38464 2.95291 7.51468 3.03228C9.02399 3.9462 10.5356 4.85531 12.0519 5.7524C12.2145 5.8486 12.2841 5.95683 12.2725 6.13721C12.2725 6.15164 12.2725 6.16607 12.2725 6.18291C12.3004 6.93168 12.0001 7.49206 11.3716 7.86405C10.9234 8.1286 10.4776 8.39556 10.0294 8.66012C10.0062 8.67215 9.9737 8.66734 9.87618 8.67936C9.87618 8.34265 9.8785 8.01316 9.87618 7.68367C9.87618 7.55379 9.92726 7.38303 9.80652 7.30607C9.3305 7.00303 8.84752 6.71202 8.35989 6.43544C8.20896 6.35126 8.202 6.50519 8.202 6.60379C8.202 8.04443 8.19735 9.48506 8.202 10.9257C8.202 11.2504 8.38776 11.1013 8.51779 11.0243C9.20047 10.6178 9.88082 10.2042 10.5612 9.79531C11.836 9.0281 13.1131 8.26329 14.3832 7.48645C14.5806 7.3662 14.7315 7.35898 14.9335 7.48405C16.5892 8.49177 18.2564 9.47784 19.9097 10.4904C20.8547 11.07 20.8083 12.2677 19.8261 12.8618C17.4762 14.2832 15.1216 15.6973 12.7671 17.1139C11.0581 18.1433 9.32353 19.1294 7.64703 20.2116C6.93649 20.671 5.87765 20.0144 5.8405 19.2472C5.80567 18.5497 5.83353 17.8523 5.82656 17.1524C5.82656 16.9888 5.90783 16.9215 6.02858 16.8494C6.66249 16.4718 7.29641 16.0966 7.92103 15.7021C8.13234 15.5699 8.202 15.6107 8.202 15.8513C8.202 16.0581 8.202 16.2673 8.202 16.4742C8.202 16.604 8.13466 16.7748 8.24844 16.8542C8.3924 16.9528 8.51315 16.7844 8.62693 16.7147C10.0178 15.8849 11.4017 15.0456 12.788 14.211C14.0767 13.4342 15.3654 12.6621 16.6542 11.8853C17.0512 11.6472 17.0512 11.6424 16.6472 11.3971C16.0899 11.0604 15.521 10.7429 14.9777 10.387C14.7455 10.2354 14.5783 10.2523 14.3554 10.387C11.8824 11.8853 9.40481 13.3764 6.9272 14.8676C6.58587 15.072 6.23988 15.274 5.89855 15.4737L5.88926 15.4785Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
