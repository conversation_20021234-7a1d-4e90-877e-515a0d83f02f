<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.0108 3.39546C11.5033 3.39546 11.0039 3.52322 10.5587 3.76697C10.1136 4.01072 9.7369 4.36262 9.4635 4.79021C9.30538 5.0375 9.00942 5.15939 8.72302 5.09518C8.22628 4.98381 7.70947 4.99967 7.2205 5.14128C6.73152 5.28289 6.28622 5.54568 5.92588 5.90526C5.56554 6.26485 5.30182 6.7096 5.15919 7.19828C5.01655 7.68695 4.99961 8.20373 5.10994 8.7007C5.17369 8.98787 5.05048 9.28419 4.80191 9.4415C4.37103 9.71418 4.01612 10.0914 3.77018 10.5381C3.52424 10.9848 3.39528 11.4864 3.39528 11.9964C3.39528 12.5063 3.52424 13.0079 3.77018 13.4546C4.01612 13.9013 4.37103 14.2785 4.80191 14.5512C5.05048 14.7085 5.17369 15.0048 5.10994 15.292C4.99961 15.789 5.01655 16.3058 5.15919 16.7944C5.30182 17.2831 5.56554 17.7279 5.92588 18.0874C6.28622 18.447 6.73152 18.7098 7.2205 18.8514C7.70947 18.993 8.22628 19.0089 8.72302 18.8975C9.01 18.8332 9.30651 18.9557 9.46437 19.2039C9.73742 19.6331 10.1144 19.9865 10.5603 20.2313C11.0062 20.4762 11.5067 20.6045 12.0154 20.6045C12.5242 20.6045 13.0247 20.4762 13.4706 20.2313C13.9165 19.9865 14.2935 19.6331 14.5665 19.2039C14.7241 18.9562 15.0199 18.8336 15.3064 18.8972C15.8034 19.0075 16.3202 18.9906 16.8089 18.848C17.2975 18.7053 17.7423 18.4416 18.1019 18.0813C18.4615 17.7209 18.7243 17.2756 18.8659 16.7867C19.0075 16.2977 19.0233 15.7809 18.912 15.2841C18.8478 14.9977 18.9697 14.7018 19.2169 14.5437C19.6445 14.2702 19.9964 13.8936 20.2402 13.4484C20.4839 13.0033 20.6117 12.5039 20.6117 11.9964C20.6117 11.4888 20.4839 10.9895 20.2402 10.5443C19.9964 10.0991 19.6445 9.72246 19.2169 9.44906C18.9695 9.29086 18.8476 8.99468 18.9121 8.70817C19.0239 8.21067 19.0081 7.69297 18.8662 7.20322C18.7242 6.71347 18.4607 6.26757 18.1001 5.90701C17.7396 5.54645 17.2937 5.28293 16.8039 5.14098C16.3142 4.99902 15.7965 4.98323 15.299 5.09509C15.0125 5.1595 14.7163 5.03763 14.5581 4.79021C14.2847 4.36262 13.908 4.01072 13.4629 3.76697C13.0177 3.52322 12.5183 3.39546 12.0108 3.39546ZM9.88854 2.54298C10.5392 2.18673 11.269 2 12.0108 2C12.7526 2 13.4824 2.18673 14.1331 2.54298C14.6452 2.82338 15.0952 3.20168 15.4585 3.65479C16.0389 3.5882 16.6283 3.63718 17.1924 3.80068C17.9082 4.00816 18.5599 4.3933 19.0869 4.92027C19.6139 5.44725 19.999 6.09894 20.2065 6.81473C20.37 7.37882 20.419 7.96828 20.3524 8.54866C20.8055 8.91194 21.1838 9.36199 21.4642 9.87409C21.8204 10.5247 22.0071 11.2546 22.0071 11.9964C22.0071 12.7381 21.8204 13.468 21.4642 14.1186C21.1837 14.6308 20.8053 15.0809 20.3521 15.4442C20.4183 16.0235 20.3693 16.6118 20.2062 17.1748C19.9993 17.8895 19.6152 18.5403 19.0897 19.067C18.5641 19.5936 17.9141 19.9791 17.1999 20.1875C16.6372 20.3518 16.0491 20.402 15.4697 20.3371C15.1063 20.7924 14.6556 21.1727 14.1422 21.4546C13.4905 21.8124 12.759 22 12.0154 22C11.2719 22 10.5404 21.8124 9.88868 21.4546C9.37562 21.1729 8.92505 20.7928 8.56185 20.3378C7.9829 20.4038 7.39499 20.3548 6.8323 20.1918C6.11765 19.9848 5.46683 19.6008 4.94017 19.0752C4.41352 18.5497 4.02809 17.8996 3.81962 17.1854C3.6555 16.6231 3.60524 16.0354 3.66998 15.4563C3.2128 15.0931 2.83087 14.6419 2.54775 14.1276C2.18831 13.4748 1.99982 12.7416 1.99982 11.9964C1.99982 11.2511 2.18831 10.5179 2.54775 9.86507C2.83087 9.35085 3.2128 8.8996 3.66997 8.5364C3.60524 7.95735 3.6555 7.36958 3.81962 6.80728C4.02809 6.09306 4.41352 5.44304 4.94017 4.91749C5.46683 4.39194 6.11765 4.00787 6.8323 3.8009C7.39534 3.63784 7.98363 3.58886 8.56293 3.65501C8.92623 3.20181 9.37635 2.82342 9.88854 2.54298Z"
                :class="fillColor"
            />
            <path
                d="M9.75625 13.8794C9.7559 13.9941 9.7174 14.1056 9.64636 14.1979C9.57531 14.2901 9.47545 14.3581 9.36131 14.392C9.30899 14.4069 9.25459 14.4141 9.2 14.4133C9.10823 14.4136 9.01785 14.3918 8.93718 14.3498C8.8565 14.3078 8.78812 14.2471 8.73832 14.1731L7.53127 12.4377V13.8794C7.53127 14.021 7.47266 14.1568 7.36835 14.2569C7.26403 14.3571 7.12255 14.4133 6.97502 14.4133C6.8275 14.4133 6.68602 14.3571 6.5817 14.2569C6.47738 14.1568 6.41878 14.021 6.41878 13.8794V10.6757C6.41913 10.561 6.45763 10.4494 6.52867 10.3572C6.59971 10.265 6.69958 10.197 6.81371 10.1631C6.92824 10.1298 7.05094 10.1331 7.16331 10.1726C7.27568 10.2121 7.37164 10.2856 7.43671 10.382L8.64376 12.1173V10.6757C8.64376 10.5341 8.70236 10.3982 8.80668 10.2981C8.911 10.198 9.05248 10.1417 9.2 10.1417C9.34753 10.1417 9.48901 10.198 9.59333 10.2981C9.69764 10.3982 9.75625 10.5341 9.75625 10.6757V13.8794ZM12.5375 11.7436C12.685 11.7436 12.8265 11.7998 12.9308 11.9C13.0351 12.0001 13.0937 12.1359 13.0937 12.2775C13.0937 12.4191 13.0351 12.555 12.9308 12.6551C12.8265 12.7552 12.685 12.8115 12.5375 12.8115H11.425V13.3454H12.5375C12.685 13.3454 12.8265 13.4017 12.9308 13.5018C13.0351 13.602 13.0937 13.7378 13.0937 13.8794C13.0937 14.021 13.0351 14.1568 12.9308 14.2569C12.8265 14.3571 12.685 14.4133 12.5375 14.4133H10.8687C10.7212 14.4133 10.5797 14.3571 10.4754 14.2569C10.3711 14.1568 10.3125 14.021 10.3125 13.8794V10.6757C10.3125 10.5341 10.3711 10.3982 10.4754 10.2981C10.5797 10.198 10.7212 10.1417 10.8687 10.1417H12.5375C12.685 10.1417 12.8265 10.198 12.9308 10.2981C13.0351 10.3982 13.0937 10.5341 13.0937 10.6757C13.0937 10.8173 13.0351 10.9531 12.9308 11.0532C12.8265 11.1534 12.685 11.2096 12.5375 11.2096H11.425V11.7436H12.5375ZM16.9813 13.9648C16.9606 14.081 16.9011 14.1876 16.8118 14.2685C16.7225 14.3494 16.6083 14.4002 16.4863 14.4133H16.4312C16.3178 14.4124 16.2074 14.3789 16.1142 14.317C16.0209 14.2551 15.9492 14.1677 15.9083 14.0663L15.5968 13.2653L15.2853 14.0663C15.2434 14.1782 15.1638 14.2734 15.0591 14.337C14.9544 14.4005 14.8306 14.4287 14.7074 14.417C14.5841 14.4054 14.4684 14.3546 14.3786 14.2727C14.2889 14.1907 14.2301 14.0824 14.2118 13.9648L13.6555 10.7611C13.6319 10.6209 13.6673 10.4774 13.7539 10.3623C13.8405 10.2471 13.9712 10.1697 14.1172 10.1471C14.2633 10.1244 14.4127 10.1584 14.5327 10.2415C14.6526 10.3246 14.7333 10.45 14.7569 10.5902L14.9683 11.829L15.0739 11.5567C15.1129 11.4536 15.184 11.3645 15.2776 11.3016C15.3711 11.2387 15.4826 11.2049 15.5968 11.2049C15.7111 11.2049 15.8225 11.2387 15.9161 11.3016C16.0096 11.3645 16.0807 11.4536 16.1197 11.5567L16.2254 11.829L16.4367 10.5902C16.4604 10.45 16.541 10.3246 16.661 10.2415C16.7204 10.2003 16.7876 10.1708 16.8589 10.1546C16.9302 10.1384 17.0041 10.1358 17.0764 10.1471C17.1487 10.1583 17.2181 10.1831 17.2804 10.22C17.3427 10.2569 17.3969 10.3053 17.4397 10.3623C17.4826 10.4193 17.5134 10.4839 17.5302 10.5523C17.5471 10.6207 17.5498 10.6917 17.5381 10.7611L16.9813 13.9648Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
