<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M11.4547 4.90413L3.42529 10.162C3.44247 10.1663 3.46484 10.1695 3.49337 10.1695H19.6876C19.7161 10.1695 19.7385 10.1663 19.7557 10.162L11.7263 4.90412C11.6846 4.8818 11.6379 4.87007 11.5905 4.87007C11.5431 4.87007 11.4964 4.88179 11.4547 4.90413ZM10.7499 3.7289C11.0047 3.57904 11.2949 3.5 11.5905 3.5C11.8861 3.5 12.1763 3.57903 12.4311 3.72891C12.4405 3.73447 12.4498 3.74025 12.459 3.74625L20.5578 9.04954C21.1322 9.42816 21.3064 10.0818 21.0846 10.64C20.8713 11.1768 20.3327 11.5396 19.6876 11.5396H3.49337C2.84821 11.5396 2.30967 11.1768 2.09637 10.64C1.87456 10.0818 2.04873 9.42816 2.62317 9.04955L2.62488 9.04842L10.722 3.74625C10.7312 3.74024 10.7405 3.73446 10.7499 3.7289Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M2 19.0748C2 18.3183 2.61336 17.7048 3.37007 17.7048H19.8109C20.5675 17.7048 21.181 18.3182 21.181 19.0748V21.1299C21.181 21.8865 20.5675 22.5 19.8109 22.5H3.37007C2.61336 22.5 2 21.8865 2 21.1299V19.0748ZM19.8109 19.0748H3.37007V21.1299H19.8109V19.0748Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M4.74014 10.1694C5.11847 10.1694 5.42517 10.4761 5.42517 10.8544V18.3898C5.42517 18.7681 5.11847 19.0748 4.74014 19.0748C4.3618 19.0748 4.0551 18.7681 4.0551 18.3898V10.8544C4.0551 10.4761 4.3618 10.1694 4.74014 10.1694Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M9.307 10.1694C9.68533 10.1694 9.99204 10.4761 9.99204 10.8544V18.3898C9.99204 18.7681 9.68533 19.0748 9.307 19.0748C8.92867 19.0748 8.62197 18.7681 8.62197 18.3898V10.8544C8.62197 10.4761 8.92867 10.1694 9.307 10.1694Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13.874 10.1694C14.2523 10.1694 14.559 10.4761 14.559 10.8544V18.3898C14.559 18.7681 14.2523 19.0748 13.874 19.0748C13.4956 19.0748 13.1889 18.7681 13.1889 18.3898V10.8544C13.1889 10.4761 13.4956 10.1694 13.874 10.1694Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M18.4408 10.1694C18.8192 10.1694 19.1259 10.4761 19.1259 10.8544V18.3898C19.1259 18.7681 18.8192 19.0748 18.4408 19.0748C18.0625 19.0748 17.7558 18.7681 17.7558 18.3898V10.8544C17.7558 10.4761 18.0625 10.1694 18.4408 10.1694Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
