<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="68"
            height="68"
            viewBox="0 0 68 68"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M46.9168 9.82233C43.7955 9.82233 40.9031 10.8127 38.5294 12.4999C34.7665 15.1745 32.3074 19.6002 32.3074 24.6077C32.3074 32.7096 38.7451 39.2886 46.7264 39.3918C46.7897 39.3926 46.8532 39.393 46.9168 39.393C54.9858 39.393 61.5262 32.7739 61.5262 24.6077C61.5262 16.4414 54.9701 9.82233 46.9168 9.82233ZM38.2173 18.786L49.0494 29.7486L52.6691 33.4119C51.2902 34.3289 49.6924 34.9359 47.9669 35.1128C47.6217 35.1482 47.2714 35.1663 46.9168 35.1663C41.1644 35.1663 36.4837 30.4293 36.4837 24.6077C36.4837 22.4545 37.1141 20.4448 38.2173 18.786ZM55.6162 30.4133L47.9587 22.6637L41.1644 15.7875C41.6018 15.4966 42.0612 15.2369 42.5397 15.0123C43.8716 14.387 45.3515 14.033 46.9168 14.033C52.6691 14.033 57.3498 18.7701 57.3498 24.5917C57.3498 26.7449 56.7036 28.7546 55.6162 30.4133Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M46.1169 42.3028C45.4008 46.3652 45.4136 49.5445 45.7197 51.1114C45.8424 51.7329 45.7991 52.3757 45.5941 52.9751C45.3892 53.5745 45.0298 54.1092 44.5523 54.5255C44.0748 54.9417 43.4961 55.2248 42.8743 55.346C42.2526 55.4673 41.6098 55.4226 41.0109 55.2163L32.3248 52.2113C31.2669 53.9977 29.6245 55.3639 27.6754 56.0788C25.7262 56.7938 23.59 56.8135 21.6279 56.1347C19.6659 55.456 17.9985 54.1204 16.9078 52.3538C15.8171 50.5873 15.37 48.4982 15.6422 46.44L6.95607 43.4351C6.35739 43.2275 5.82404 42.8657 5.4098 42.3862C4.99555 41.9067 4.71508 41.3265 4.59664 40.704C4.47821 40.0815 4.52601 39.4388 4.73523 38.8407C4.94445 38.2426 5.30768 37.7102 5.7883 37.2972C7.6735 35.6711 11.4226 30.5241 14.0248 23.0022C15.587 18.4863 18.8793 14.7759 23.1772 12.6873C27.0738 10.7938 31.5077 10.3739 35.6671 11.4762C31.7716 14.663 29.2851 19.5079 29.2851 24.9334C29.2851 34.3482 36.7721 42.0144 46.1169 42.3028ZM38.5294 12.4999C34.7665 15.1745 32.3074 19.6002 32.3074 24.6077C32.3074 32.7096 38.7451 39.2886 46.7264 39.3918C46.7897 39.3926 46.8532 39.393 46.9168 39.393C54.9858 39.393 61.5262 32.7739 61.5262 24.6077C61.5262 16.4414 54.9701 9.82233 46.9168 9.82233C43.7955 9.82233 40.9031 10.8127 38.5294 12.4999ZM22.8054 52.7312C21.7494 52.3655 20.8327 51.6815 20.1815 50.7734C19.5303 49.8652 19.1766 48.7776 19.169 47.6601L28.798 50.9912C28.1015 51.8651 27.1513 52.5017 26.0781 52.8133C25.0049 53.1249 23.8616 53.0962 22.8054 52.7312ZM42.5397 15.0123C43.8716 14.387 45.3515 14.033 46.9168 14.033C52.6691 14.033 57.3498 18.7701 57.3498 24.5917C57.3498 26.7449 56.7036 28.7546 55.6162 30.4133L47.9587 22.6637L41.1644 15.7875C41.6018 15.4966 42.0612 15.2369 42.5397 15.0123ZM49.0494 29.7486L38.2173 18.786C37.1141 20.4448 36.4837 22.4545 36.4837 24.6077C36.4837 30.4293 41.1644 35.1663 46.9168 35.1663C47.2714 35.1663 47.6217 35.1482 47.9669 35.1128C49.6924 34.9359 51.2902 34.3289 52.6691 33.4119L49.0494 29.7486Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-gray-400',
    },
})
</script>
