<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M20.8192 17.7962C20.8192 18.7859 20.5182 19.5672 19.9163 20.1404C19.3143 20.7135 18.5143 21 17.5164 21H6.70521C5.70747 21 4.9075 20.7135 4.3053 20.1404C3.7031 19.5673 3.40211 18.7859 3.40234 17.7962C3.40234 17.3591 3.41677 16.9324 3.44563 16.5161C3.47449 16.0997 3.53222 15.6503 3.61882 15.1677C3.70541 14.6851 3.81463 14.2378 3.94647 13.8256C4.07831 13.4134 4.25564 13.0114 4.47846 12.6196C4.70128 12.2277 4.95688 11.8937 5.24527 11.6176C5.53365 11.3414 5.88624 11.1208 6.30304 10.9557C6.71983 10.7906 7.17957 10.7082 7.68223 10.7084C7.75635 10.7084 7.92953 10.7971 8.20178 10.9744C8.47403 11.1518 8.78126 11.3497 9.12346 11.5681C9.46565 11.7865 9.91096 11.9844 10.4594 12.1617C11.0078 12.3391 11.5582 12.4277 12.1108 12.4277C12.6634 12.4277 13.2138 12.3391 13.7622 12.1617C14.3107 11.9844 14.756 11.7865 15.0982 11.5681C15.4403 11.3497 15.7476 11.1518 16.0198 10.9744C16.2921 10.7971 16.4653 10.7084 16.5394 10.7084C17.0425 10.7084 17.5023 10.7908 17.9186 10.9557C18.3349 11.1206 18.6875 11.3412 18.9763 11.6176C19.2652 11.894 19.5208 12.228 19.7432 12.6196C19.9655 13.0112 20.1429 13.4132 20.2752 13.8256C20.4075 14.238 20.5167 14.6854 20.6028 15.1677C20.6889 15.6501 20.7467 16.0995 20.776 16.5161C20.8053 16.9326 20.8197 17.3594 20.8193 17.7962H20.8192ZM16.8607 6.74992C16.8607 8.06122 16.3968 9.18064 15.469 10.1082C14.5413 11.0357 13.4219 11.4996 12.1108 11.4998C10.7997 11.5001 9.68028 11.0362 8.75251 10.1082C7.82474 9.18014 7.36086 8.06073 7.36086 6.74992C7.36086 5.43911 7.82474 4.31969 8.75251 3.39165C9.68028 2.46362 10.7997 1.99974 12.1108 2C13.4219 2.00026 14.5413 2.46415 15.469 3.39165C16.3968 4.31916 16.8607 5.43858 16.8607 6.74992Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
