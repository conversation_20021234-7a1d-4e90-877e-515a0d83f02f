<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M19.6319 14.1952C19.6319 15.6886 19.6329 17.1823 19.6349 18.6764C19.6349 18.7972 19.6018 18.8649 19.495 18.9263C17.1518 20.2756 14.81 21.6281 12.4699 22.9836C12.3935 23.0278 12.3392 23.0299 12.2617 22.9849C9.92978 21.6349 7.59642 20.2872 5.26164 18.9418C5.1392 18.8713 5.09082 18.7983 5.09082 18.6524C5.0962 15.8958 5.09768 13.1396 5.09528 10.3838C5.09528 10.1525 5.09888 9.92126 5.1061 9.6902C5.1148 9.4054 5.3183 9.20555 5.59416 9.20767C5.86175 9.2098 6.06588 9.40688 6.07395 9.68319C6.08095 9.92381 6.07395 10.1649 6.07713 10.4053C6.07968 10.7058 6.28594 10.9275 6.55968 10.9271C6.83341 10.9267 7.03967 10.7054 7.04031 10.404C7.04222 9.66683 7.03904 8.92967 7.04201 8.1925C7.04349 7.82934 7.33527 7.59954 7.66609 7.69278C7.87468 7.75161 8.00964 7.93277 8.01325 8.1702C8.01686 8.42081 8.01325 8.67142 8.01474 8.92181C8.01771 9.24569 8.20784 9.45764 8.49261 9.45955C8.78121 9.46125 8.98068 9.24187 8.98068 8.91523C8.98174 7.91323 8.98068 6.91101 8.98174 5.90901C8.98174 5.55944 9.25293 5.33347 9.57823 5.40525C9.79956 5.4541 9.94555 5.63886 9.94852 5.89244C9.95277 6.25859 9.9498 6.62473 9.9498 6.99087C9.9498 7.2848 9.94831 7.57873 9.9498 7.87266C9.95128 8.16659 10.1522 8.38237 10.4239 8.38704C10.6997 8.39171 10.9143 8.1736 10.9162 7.87585C10.9193 7.39417 10.9145 6.91229 10.9191 6.43061C10.9217 6.19169 11.0859 6.00777 11.3164 5.96614C11.4238 5.94554 11.5351 5.96272 11.6314 6.01477C11.7277 6.06681 11.803 6.15054 11.8448 6.25179C11.8706 6.32392 11.8835 6.40006 11.8829 6.4767C11.888 6.67888 11.8829 6.88022 11.8853 7.08368C11.8897 7.39587 12.0952 7.61271 12.3789 7.60825C12.6566 7.604 12.8512 7.3895 12.8521 7.07964C12.8538 6.46374 12.8506 5.84636 12.8538 5.22961C12.8532 5.11541 12.8929 5.00467 12.966 4.91696C13.039 4.82924 13.1407 4.77019 13.2531 4.75024C13.3654 4.7303 13.4812 4.75073 13.58 4.80794C13.6787 4.86515 13.7541 4.95545 13.7928 5.06289C13.8142 5.13658 13.8239 5.21321 13.8214 5.28993C13.8235 5.95977 13.8214 6.6294 13.8244 7.29903C13.8244 7.39417 13.8305 7.49463 13.8611 7.58319C13.8975 7.68655 13.9691 7.77379 14.0634 7.82966C14.1576 7.88553 14.2684 7.90646 14.3765 7.8888C14.597 7.85334 14.7786 7.6605 14.7878 7.43049C14.7975 7.18519 14.7912 6.93926 14.7918 6.69354C14.7918 6.36371 14.9828 6.15346 15.2799 6.15516C15.5735 6.15707 15.7645 6.37115 15.7645 6.70118C15.7656 7.3517 15.7633 8.002 15.766 8.65252C15.766 8.88464 15.8952 9.06071 16.0979 9.12676C16.281 9.18644 16.5102 9.12973 16.6142 8.96577C16.6778 8.86617 16.709 8.73704 16.7237 8.61726C16.7496 8.40488 16.8722 8.23264 17.0774 8.17127C17.2684 8.1135 17.4923 8.17827 17.5994 8.35264C17.6569 8.4512 17.6886 8.56265 17.6917 8.67673C17.7028 9.05221 17.6917 9.42833 17.6975 9.80403C17.7021 10.1294 17.9086 10.3384 18.2029 10.3288C18.3218 10.3238 18.4346 10.2747 18.5195 10.1912C18.6043 10.1076 18.6551 9.99557 18.6621 9.87667C18.6664 9.79023 18.66 9.70315 18.6647 9.61671C18.6693 9.49249 18.7212 9.37475 18.8098 9.2876C18.8984 9.20046 19.0169 9.15051 19.1411 9.14799C19.3957 9.14226 19.6079 9.33552 19.6327 9.59909C19.6378 9.65176 19.6359 9.70528 19.6359 9.75795C19.6359 11.2369 19.6359 12.716 19.6359 14.1952H19.6319ZM15.772 17.3781V11.952H13.5073V13.7626H11.213V11.9493H8.94991V17.3757H11.2205V15.6555H13.5097V17.3781H15.772Z"
                :class="fillColor"
            />
            <path
                d="M10.9174 3.99824C10.9174 3.77206 10.9153 3.54566 10.9174 3.31863C10.921 3.02491 11.1207 2.81423 11.3957 2.80892C11.6707 2.80361 11.8808 3.01748 11.8838 3.30737C11.8876 3.76951 11.8876 4.23172 11.8838 4.69399C11.8813 4.98177 11.6816 5.18119 11.4068 5.18246C11.1248 5.18438 10.923 4.98304 10.9187 4.6889C10.9147 4.45974 10.9174 4.22867 10.9174 3.99824Z"
                :class="fillColor"
            />
            <path
                d="M13.8229 2.71335C13.8229 2.93975 13.8248 3.16593 13.8229 3.39296C13.8197 3.6939 13.6137 3.90522 13.3314 3.90118C13.0566 3.89715 12.8553 3.68541 12.8538 3.39148C12.8519 2.9384 12.8519 2.48582 12.8538 2.03374C12.8553 1.7413 13.0571 1.52934 13.3319 1.52403C13.6128 1.51978 13.8178 1.73195 13.8233 2.03374V2.71335H13.8229Z"
                :class="fillColor"
            />
            <path
                d="M15.275 5.55752C15.2112 5.55917 15.1477 5.54807 15.0882 5.52485C15.0288 5.50163 14.9745 5.46678 14.9287 5.42232C14.8829 5.37787 14.8464 5.3247 14.8213 5.26595C14.7963 5.2072 14.7832 5.14403 14.7828 5.08015C14.7825 5.01627 14.7948 4.95297 14.8192 4.89394C14.8436 4.83491 14.8796 4.78135 14.9249 4.73639C14.9702 4.69143 15.0241 4.65597 15.0833 4.6321C15.1425 4.60823 15.2059 4.59642 15.2697 4.59736C15.5379 4.59418 15.7624 4.81101 15.7643 5.07543C15.7662 5.33984 15.5438 5.55752 15.275 5.55752Z"
                :class="fillColor"
            />
            <path
                d="M11.4055 0.982467C11.4691 0.982454 11.5322 0.995151 11.5908 1.01981C11.6495 1.04448 11.7027 1.08061 11.7473 1.1261C11.7918 1.17159 11.8269 1.22552 11.8504 1.28473C11.8738 1.34394 11.8853 1.40725 11.884 1.47094C11.8805 1.59753 11.8276 1.7177 11.7365 1.80564C11.6455 1.89358 11.5236 1.94227 11.3971 1.94124C11.2706 1.94021 11.1495 1.88955 11.0599 1.80014C10.9703 1.71073 10.9193 1.58971 10.9179 1.46308C10.918 1.39937 10.9307 1.33631 10.9553 1.27756C10.98 1.21881 11.016 1.16554 11.0614 1.12084C11.1067 1.07613 11.1605 1.04088 11.2196 1.01713C11.2786 0.993377 11.3418 0.981596 11.4055 0.982467Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
