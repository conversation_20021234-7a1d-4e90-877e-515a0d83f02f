<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M4.36328 2.90918H10.928C12.0608 2.90918 13.0476 3.02932 13.8886 3.2696C14.7382 3.5013 15.4419 3.83168 15.9996 4.26075C16.566 4.68124 16.9908 5.18325 17.274 5.76678C17.5572 6.35032 17.6988 6.98534 17.6988 7.67185C17.6988 8.16957 17.6001 8.64155 17.4027 9.08778C17.2139 9.52543 16.9436 9.93305 16.5918 10.3106C16.2485 10.6796 15.828 11.01 15.3303 11.3018C14.8412 11.585 14.3005 11.821 13.7084 12.0097L19.9256 20.3637H15.2659L9.40914 12.4731H8.64968V20.3637H4.36328V2.90918ZM8.64968 10.9414H9.57647C10.0999 10.9414 10.5848 10.8684 11.031 10.7225C11.4773 10.5767 11.8634 10.3707 12.1895 10.1047C12.5156 9.83007 12.7688 9.4954 12.949 9.10065C13.1378 8.70591 13.2321 8.25968 13.2321 7.76195C13.2321 7.29856 13.1592 6.87378 13.0133 6.48762C12.876 6.10146 12.6829 5.77107 12.4341 5.49647C12.1852 5.21328 11.8849 4.99446 11.533 4.83999C11.1812 4.68553 10.7993 4.60829 10.3874 4.60829H8.64968V10.9414Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
