<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.08058 4.08058C4.25269 2.90848 5.8424 2.25 7.5 2.25C8.45514 2.25 9.31239 2.38639 10.147 2.74989C10.7904 3.03013 11.3924 3.4332 12 3.96975C12.6076 3.4332 13.2096 3.03013 13.853 2.74989C14.6876 2.38639 15.5449 2.25 16.5 2.25C18.1576 2.25 19.7473 2.90848 20.9194 4.08058C22.0915 5.25269 22.75 6.8424 22.75 8.5C22.75 11.1289 21.0154 13.0749 19.5276 14.533L12.5303 21.5303C12.2374 21.8232 11.7626 21.8232 11.4697 21.5303L4.47414 14.5348C2.97096 13.0812 1.25 11.1372 1.25 8.5C1.25 6.8424 1.90848 5.25269 3.08058 4.08058ZM7.5 3.75C6.24022 3.75 5.03204 4.25045 4.14124 5.14124C3.25045 6.03204 2.75 7.24022 2.75 8.5C2.75 10.4601 4.02553 12.0149 5.52127 13.4608L5.53041 13.4696L12 19.9393L18.4751 13.4643C19.9667 12.0027 21.25 10.4495 21.25 8.5C21.25 7.24022 20.7496 6.03204 19.8588 5.14124C18.968 4.25045 17.7598 3.75 16.5 3.75C15.6951 3.75 15.0524 3.86361 14.452 4.12511C13.8467 4.38872 13.2376 4.82308 12.5303 5.53033C12.2374 5.82322 11.7626 5.82322 11.4697 5.53033C10.7624 4.82308 10.1533 4.38872 9.54802 4.12511C8.94761 3.86361 8.30486 3.75 7.5 3.75Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
