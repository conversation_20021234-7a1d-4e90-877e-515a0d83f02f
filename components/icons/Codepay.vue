<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M2.31787 6.84894C2.66305 6.84894 2.94287 6.56912 2.94287 6.22394V4.92185C2.94287 3.82878 3.82897 2.94269 4.92204 2.94269H6.22412C6.5693 2.94269 6.84912 2.66287 6.84912 2.31769C6.84912 1.97251 6.5693 1.69269 6.22412 1.69269H4.92204C3.13861 1.69269 1.69287 3.13843 1.69287 4.92185V6.22394C1.69287 6.56912 1.97269 6.84894 2.31787 6.84894Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M17.6825 6.84894C18.0276 6.84894 18.3075 6.56912 18.3075 6.22394V4.92185C18.3075 3.13843 16.8617 1.69269 15.0783 1.69269H13.7762C13.431 1.69269 13.1512 1.97251 13.1512 2.31769C13.1512 2.66287 13.431 2.94269 13.7762 2.94269H15.0783C16.1714 2.94269 17.0575 3.82878 17.0575 4.92185V6.22394C17.0575 6.56912 17.3373 6.84894 17.6825 6.84894Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1.69288 15.0781C1.69288 16.8615 3.13862 18.3073 4.92204 18.3073H6.22413C6.5693 18.3073 6.84913 18.0274 6.84913 17.6823C6.84913 17.3371 6.5693 17.0573 6.22413 17.0573H4.92204C3.82897 17.0573 2.94288 16.1712 2.94288 15.0781V13.776C2.94288 13.4308 2.66305 13.151 2.31788 13.151C1.9727 13.151 1.69288 13.4308 1.69288 13.776V15.0781Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13.1512 17.6823C13.1512 18.0275 13.431 18.3073 13.7762 18.3073H15.0783C16.8617 18.3073 18.3075 16.8615 18.3075 15.0781V13.776C18.3075 13.4308 18.0276 13.151 17.6825 13.151C17.3373 13.151 17.0575 13.4308 17.0575 13.776V15.0781C17.0575 16.1712 16.1714 17.0573 15.0783 17.0573H13.7762C13.431 17.0573 13.1512 17.3371 13.1512 17.6823Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M4.16683 7.39581C4.16683 8.4601 5.02963 9.3229 6.09391 9.3229H7.396C8.46028 9.3229 9.32308 8.4601 9.32308 7.39581V6.09373C9.32308 5.02944 8.46028 4.16665 7.396 4.16665H6.09391C5.02963 4.16665 4.16683 5.02944 4.16683 6.09373V7.39581ZM6.09391 8.0729C5.71998 8.0729 5.41683 7.76974 5.41683 7.39581V6.09373C5.41683 5.7198 5.71998 5.41665 6.09391 5.41665H7.396C7.76993 5.41665 8.07308 5.7198 8.07308 6.09373V7.39581C8.07308 7.76974 7.76993 8.0729 7.396 8.0729H6.09391Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M10.6772 7.39581C10.6772 8.46009 11.54 9.3229 12.6043 9.3229H13.9064C14.9707 9.3229 15.8335 8.46009 15.8335 7.39581V6.09373C15.8335 5.02945 14.9707 4.16665 13.9064 4.16665H12.6043C11.54 4.16665 10.6772 5.02945 10.6772 6.09373V7.39581ZM12.6043 8.0729C12.2304 8.0729 11.9272 7.76975 11.9272 7.39581V6.09373C11.9272 5.71979 12.2304 5.41665 12.6043 5.41665H13.9064C14.2804 5.41665 14.5835 5.71979 14.5835 6.09373V7.39581C14.5835 7.76975 14.2804 8.0729 13.9064 8.0729H12.6043Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M4.16683 13.9062C4.16683 14.9705 5.02963 15.8333 6.09391 15.8333H7.396C8.46028 15.8333 9.32308 14.9705 9.32308 13.9062V12.6041C9.32308 11.5399 8.46028 10.6771 7.396 10.6771H6.09391C5.02963 10.6771 4.16683 11.5399 4.16683 12.6041V13.9062ZM6.09391 14.5833C5.71998 14.5833 5.41683 14.2802 5.41683 13.9062V12.6041C5.41683 12.2302 5.71998 11.9271 6.09391 11.9271H7.396C7.76993 11.9271 8.07308 12.2302 8.07308 12.6041V13.9062C8.07308 14.2802 7.76993 14.5833 7.396 14.5833H6.09391Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13.2814 15.2083C13.2814 15.5535 13.5612 15.8333 13.9064 15.8333H15.2085C15.5537 15.8333 15.8335 15.5535 15.8335 15.2083C15.8335 14.8631 15.5537 14.5833 15.2085 14.5833H14.5314V13.9062C14.5314 13.5611 14.2516 13.2812 13.9064 13.2812H11.9272V11.3021C11.9272 10.9569 11.6474 10.6771 11.3022 10.6771C10.9571 10.6771 10.6772 10.9569 10.6772 11.3021V13.9062C10.6772 14.2514 10.9571 14.5312 11.3022 14.5312H13.2814V15.2083Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M15.2085 13.2291C15.5537 13.2291 15.8335 12.9493 15.8335 12.6041V11.3021C15.8335 10.9569 15.5537 10.6771 15.2085 10.6771C14.8633 10.6771 14.5835 10.9569 14.5835 11.3021V12.6041C14.5835 12.9493 14.8633 13.2291 15.2085 13.2291Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M11.3022 15.8333C11.6474 15.8333 11.9272 15.5535 11.9272 15.2083V13.9062C11.9272 13.5611 11.6474 13.2812 11.3022 13.2812C10.9571 13.2812 10.6772 13.5611 10.6772 13.9062V15.2083C10.6772 15.5535 10.9571 15.8333 11.3022 15.8333Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13.2814 11.3021C13.2814 11.6472 13.5612 11.9271 13.9064 11.9271H15.2085C15.5537 11.9271 15.8335 11.6472 15.8335 11.3021C15.8335 10.9569 15.5537 10.6771 15.2085 10.6771L13.9064 10.6771C13.5612 10.6771 13.2814 10.9569 13.2814 11.3021Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-green-200',
    },
})
</script>
