<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clip-path="url(#clip0_9940_6833)">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M8.61864 9.01271C5.10134 9.01271 2.25 11.8641 2.25 15.3814C2.25 18.8987 5.10134 21.75 8.61864 21.75C12.1359 21.75 14.9873 18.8987 14.9873 15.3814C14.9873 11.8641 12.1359 9.01271 8.61864 9.01271ZM0.75 15.3814C0.75 11.0356 4.27291 7.51271 8.61864 7.51271C12.9644 7.51271 16.4873 11.0356 16.4873 15.3814C16.4873 19.7271 12.9644 23.25 8.61864 23.25C4.27291 23.25 0.75 19.7271 0.75 15.3814Z"
                    :class="fillColor"
                />
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M8.61864 12.572C8.04998 12.572 7.58898 13.033 7.58898 13.6017C7.58898 14.1704 8.04998 14.6314 8.61864 14.6314C9.18731 14.6314 9.6483 14.1704 9.6483 13.6017C9.6483 13.033 9.18731 12.572 8.61864 12.572ZM6.08898 13.6017C6.08898 12.2046 7.22155 11.072 8.61864 11.072C10.0157 11.072 11.1483 12.2046 11.1483 13.6017C11.1483 14.9988 10.0157 16.1314 8.61864 16.1314C7.22155 16.1314 6.08898 14.9988 6.08898 13.6017Z"
                    :class="fillColor"
                />
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M8.61864 16.1314C8.04998 16.1314 7.58898 16.5924 7.58898 17.161C7.58898 17.7297 8.04998 18.1907 8.61864 18.1907C9.18731 18.1907 9.6483 17.7297 9.6483 17.161C9.6483 16.5924 9.18731 16.1314 8.61864 16.1314ZM6.08898 17.161C6.08898 15.7639 7.22155 14.6314 8.61864 14.6314C10.0157 14.6314 11.1483 15.7639 11.1483 17.161C11.1483 18.5581 10.0157 19.6907 8.61864 19.6907C7.22155 19.6907 6.08898 18.5581 6.08898 17.161Z"
                    :class="fillColor"
                />
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M15.3814 2.25C13.0102 2.25 10.9408 3.5454 9.84369 5.47072C9.63862 5.83061 9.18063 5.95611 8.82074 5.75104C8.46085 5.54597 8.33535 5.08799 8.54042 4.7281C9.8934 2.35366 12.4496 0.75 15.3814 0.75C19.7271 0.75 23.25 4.27289 23.25 8.61864C23.25 11.5504 21.6463 14.1066 19.2719 15.4596C18.912 15.6647 18.454 15.5391 18.249 15.1793C18.0439 14.8194 18.1694 14.3614 18.5293 14.1563C20.4546 13.0592 21.75 10.9898 21.75 8.61864C21.75 5.10132 18.8987 2.25 15.3814 2.25Z"
                    :class="fillColor"
                />
            </g>
            <defs>
                <clipPath id="clip0_9940_6833">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 20,
    },
    h: {
        type: Number,
        default: 20,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
