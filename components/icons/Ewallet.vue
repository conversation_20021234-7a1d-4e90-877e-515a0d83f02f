<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M4.49136 3.56287C3.27641 3.56287 2.2915 4.54777 2.2915 5.76272V6.55016H5.36242L6.59179 5.32079C6.83587 5.07671 7.2316 5.07671 7.47568 5.32079C7.71975 5.56486 7.71975 5.96059 7.47568 6.20467L6.06325 7.6171C5.94604 7.73431 5.78707 7.80016 5.62131 7.80016H2.2915V9.37502H8.44617C8.79134 9.37502 9.07117 9.65484 9.07117 10C9.07117 10.3452 8.79134 10.625 8.44617 10.625H2.2915V12.1999H5.62131C5.78707 12.1999 5.94604 12.2657 6.06325 12.3829L7.47568 13.7954C7.71975 14.0394 7.71975 14.4352 7.47568 14.6792C7.2316 14.9233 6.83587 14.9233 6.59179 14.6792L5.36242 13.4499H2.2915V14.2373C2.2915 15.4523 3.27641 16.4372 4.49136 16.4372H15.5083C16.3686 16.4372 17.1147 15.9434 17.4766 15.221C17.6312 14.9124 18.0068 14.7876 18.3154 14.9422C18.624 15.0968 18.7488 15.4723 18.5942 15.781C18.0286 16.91 16.8597 17.6872 15.5083 17.6872H4.49136C2.58605 17.6872 1.0415 16.1426 1.0415 14.2373V5.76272C1.0415 3.85741 2.58605 2.31287 4.49136 2.31287H15.5083C17.4136 2.31287 18.9582 3.85741 18.9582 5.76272V8.58759H18.3332H17.7082C17.7082 8.15268 17.3556 7.80016 16.9207 7.80016H14.0959C12.8809 7.80016 11.896 8.785 11.896 10C11.896 11.2149 12.881 12.1999 14.0959 12.1999H16.9207C17.3556 12.1999 17.7082 11.8473 17.7082 11.4124V8.58759H18.3332H18.9582V11.4124C18.9582 12.5376 18.046 13.4499 16.9207 13.4499H14.0959C12.1906 13.4499 10.646 11.9053 10.646 10C10.646 8.09462 12.1906 6.55016 14.0959 6.55016H16.9207C17.1999 6.55016 17.4659 6.60629 17.7082 6.70789V5.76272C17.7082 4.54777 16.7233 3.56287 15.5083 3.56287H4.49136ZM14.0959 9.9188C14.051 9.9188 14.0147 9.95516 14.0147 10C14.0147 10.0449 14.051 10.0812 14.0959 10.0812C14.1407 10.0812 14.1771 10.0449 14.1771 10C14.1771 9.95516 14.1407 9.9188 14.0959 9.9188ZM12.7647 10C12.7647 9.2648 13.3607 8.6688 14.0959 8.6688C14.8311 8.6688 15.4271 9.2648 15.4271 10C15.4271 10.7352 14.8311 11.3312 14.0959 11.3312C13.3607 11.3312 12.7647 10.7352 12.7647 10Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-green-200',
    },
})
</script>
