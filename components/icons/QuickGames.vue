<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.3659 1.74606L12.0006 2.25L11.6353 1.74606C11.8532 1.5881 12.1479 1.5881 12.3659 1.74606ZM17.175 11.9196C17.1831 11.7403 17.1873 11.5599 17.1873 11.3786C17.1873 8.29893 15.9786 5.89045 14.7845 4.26351C14.187 3.44952 13.5895 2.82572 13.1394 2.40381C12.9141 2.19261 12.725 2.03127 12.5902 1.92129C12.5228 1.86628 12.4689 1.82405 12.4307 1.7948C12.4117 1.78017 12.3966 1.76878 12.3856 1.76065L12.3725 1.75091L12.3683 1.74787L12.3669 1.74681C12.3666 1.74664 12.3659 1.74606 12.0006 2.25C11.6353 1.74606 11.6355 1.74589 11.6353 1.74606L11.6342 1.74681L11.6328 1.74787L11.6286 1.75091L11.6155 1.76065C11.6046 1.76878 11.5895 1.78017 11.5704 1.7948C11.5323 1.82405 11.4784 1.86628 11.411 1.92129C11.2762 2.03127 11.087 2.19261 10.8617 2.40381C10.4116 2.82572 9.8141 3.44952 9.21665 4.26351C8.02251 5.89045 6.81385 8.29893 6.81385 11.3786C6.81385 11.5599 6.81804 11.7403 6.82618 11.9197C5.32755 12.828 4.32422 14.4751 4.32422 16.3579V17.6027C4.32422 17.8057 4.42325 17.996 4.58955 18.1125C4.75586 18.229 4.96852 18.257 5.15933 18.1876L8.09653 17.1195C8.2744 17.532 8.46225 17.9268 8.65423 18.3015C8.76073 18.5093 8.97462 18.64 9.20816 18.64H9.43995C9.42462 18.7623 9.41672 18.887 9.41672 19.0135C9.41672 19.4605 9.59347 19.9185 9.79155 20.3055C9.99702 20.7069 10.264 21.1048 10.5202 21.4498C10.778 21.7968 11.0344 22.1029 11.2256 22.3215C11.3215 22.4312 11.4017 22.5198 11.4585 22.5814C11.4869 22.6123 11.5095 22.6365 11.5253 22.6533L11.5438 22.6729L11.549 22.6783L11.5513 22.6807C11.5514 22.6808 11.5514 22.6809 12.0006 22.25L11.5513 22.6807C11.6687 22.8031 11.831 22.8724 12.0006 22.8724C12.1702 22.8724 12.3324 22.8032 12.4498 22.6809L12.0006 22.25C12.4498 22.6809 12.4497 22.6809 12.4498 22.6809L12.4506 22.68L12.4522 22.6783L12.4574 22.6729L12.4759 22.6533C12.4917 22.6365 12.5143 22.6123 12.5427 22.5814C12.5995 22.5198 12.6798 22.4312 12.7757 22.3216C12.9669 22.1029 13.2232 21.7968 13.481 21.4498C13.7372 21.1049 14.0042 20.707 14.2097 20.3055C14.4078 19.9185 14.5846 19.4605 14.5846 19.0135C14.5846 18.887 14.5766 18.7623 14.5613 18.64H14.793C15.0266 18.64 15.2404 18.5093 15.3469 18.3015C15.5389 17.9268 15.7268 17.5319 15.9047 17.1195L18.8419 18.1876C19.0327 18.257 19.2454 18.229 19.4117 18.1125C19.578 17.996 19.677 17.8057 19.677 17.6027V16.3579C19.677 14.4751 18.6736 12.828 17.175 11.9196ZM15.9104 12.2075C15.9092 12.2191 15.9083 12.2306 15.9077 12.2422C15.7992 13.6017 15.4462 14.9061 14.9806 16.0946C14.9783 16.1001 14.9761 16.1057 14.974 16.1114C14.7989 16.5568 14.6081 16.9858 14.4084 17.3952H13.7537C13.7447 17.395 13.7357 17.395 13.7267 17.3952H10.2745C10.2656 17.395 10.2566 17.395 10.2476 17.3952H9.59279C9.39373 16.9871 9.20345 16.5594 9.02882 16.1155C9.02571 16.1071 9.02243 16.0987 9.01899 16.0905C8.55452 14.9042 8.20248 13.6025 8.09375 12.2458C8.09314 12.2319 8.09205 12.2179 8.09047 12.2039C8.06962 11.9308 8.05866 11.6556 8.05866 11.3786C8.05866 9.70028 8.46133 8.24118 9.02338 7.02178H14.9778C15.5398 8.24118 15.9425 9.70028 15.9425 11.3786C15.9425 11.6568 15.9314 11.9333 15.9104 12.2075ZM14.3065 5.77697C14.1347 5.50042 13.958 5.24132 13.781 5.00006C13.2373 4.2594 12.6938 3.69231 12.2881 3.31199C12.1817 3.21228 12.085 3.12561 12.0006 3.05222C11.9161 3.12561 11.8194 3.21228 11.7131 3.31199C11.3073 3.69231 10.7638 4.2594 10.2202 5.00006C10.0431 5.24132 9.8664 5.50042 9.69465 5.77697H14.3065ZM6.97232 13.3424C6.11362 14.0659 5.56903 15.1486 5.56903 16.3579V16.7141L7.64295 15.9599C7.35375 15.1353 7.11759 14.2573 6.97232 13.3424ZM10.7017 18.64C10.6756 18.7591 10.6615 18.8841 10.6615 19.0135C10.6615 19.1623 10.73 19.4068 10.8997 19.7383C11.062 20.0555 11.2853 20.3922 11.5195 20.7075C11.6864 20.9321 11.854 21.1396 12.0006 21.3134C12.1472 21.1396 12.3149 20.9321 12.4817 20.7075C12.7159 20.3922 12.9393 20.0555 13.1016 19.7383C13.2713 19.4068 13.3397 19.1623 13.3397 19.0135C13.3397 18.8841 13.3257 18.7591 13.2995 18.64H10.7017ZM16.3582 15.9599C16.6474 15.1352 16.8836 14.2573 17.0288 13.3423C17.8876 14.0658 18.4322 15.1485 18.4322 16.3579V16.7141L16.3582 15.9599ZM12.0006 9.51141C11.1985 9.51141 10.5483 10.1616 10.5483 10.9637C10.5483 11.7658 11.1985 12.416 12.0006 12.416C12.8026 12.416 13.4529 11.7658 13.4529 10.9637C13.4529 10.1616 12.8026 9.51141 12.0006 9.51141ZM9.30347 10.9637C9.30347 9.47414 10.511 8.2666 12.0006 8.2666C13.4901 8.2666 14.6977 9.47414 14.6977 10.9637C14.6977 12.4532 13.4901 13.6608 12.0006 13.6608C10.511 13.6608 9.30347 12.4532 9.30347 10.9637Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
