<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M11.5531 9.04907C11.5531 6.86653 9.77744 5.0909 7.59489 5.0909H3.63672V18.5566H7.59489C9.77744 18.5566 11.5531 16.781 11.5531 14.5984C11.5531 13.5502 11.1434 12.5616 10.4159 11.8237C11.1434 11.0859 11.5531 10.0972 11.5531 9.04907ZM7.59489 16.1896H6.00373V13.0072H7.59489C8.47224 13.0072 9.18605 13.721 9.18605 14.5984C9.18605 15.4758 8.47224 16.1896 7.59489 16.1896ZM7.59489 10.6402H6.00373V7.45791H7.59489C8.47224 7.45791 9.18605 8.1717 9.18605 9.04907C9.18605 9.92645 8.47224 10.6402 7.59489 10.6402Z"
                :class="fillColor"
            />
            <path
                d="M21.0901 9.049C21.0901 6.86645 19.3145 5.09082 17.1319 5.09082H13.1737V18.5565H17.1319C19.3145 18.5565 21.0901 16.7809 21.0901 14.5983C21.0901 13.5502 20.6804 12.5615 19.9529 11.8237C20.6804 11.0858 21.0901 10.0972 21.0901 9.049ZM17.1319 16.1895H15.5408V13.0072H17.1319C18.0093 13.0072 18.7231 13.721 18.7231 14.5983C18.7231 15.4757 18.0093 16.1895 17.1319 16.1895ZM17.1319 10.6402H15.5408V7.45784H17.1319C18.0093 7.45784 18.7231 8.17162 18.7231 9.049C18.7231 9.92637 18.0093 10.6402 17.1319 10.6402Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
