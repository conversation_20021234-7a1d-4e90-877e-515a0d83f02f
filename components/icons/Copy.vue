<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.33317 2.29169C2.76168 2.29169 2.2915 2.76186 2.2915 3.33335V11.6667C2.2915 12.2382 2.76168 12.7084 3.33317 12.7084C3.67835 12.7084 3.95817 12.9882 3.95817 13.3334C3.95817 13.6785 3.67835 13.9584 3.33317 13.9584C2.07133 13.9584 1.0415 12.9285 1.0415 11.6667V3.33335C1.0415 2.07151 2.07133 1.04169 3.33317 1.04169H11.6665C12.9283 1.04169 13.9582 2.07151 13.9582 3.33335C13.9582 3.67853 13.6783 3.95835 13.3332 3.95835C12.988 3.95835 12.7082 3.67853 12.7082 3.33335C12.7082 2.76186 12.238 2.29169 11.6665 2.29169H3.33317ZM8.33317 7.29169C7.75787 7.29169 7.2915 7.75806 7.2915 8.33335V16.6667C7.2915 17.242 7.75787 17.7084 8.33317 17.7084H16.6665C17.2418 17.7084 17.7082 17.242 17.7082 16.6667V8.33335C17.7082 7.75806 17.2418 7.29169 16.6665 7.29169H8.33317ZM6.0415 8.33335C6.0415 7.0677 7.06752 6.04169 8.33317 6.04169H16.6665C17.9322 6.04169 18.9582 7.0677 18.9582 8.33335V16.6667C18.9582 17.9323 17.9322 18.9584 16.6665 18.9584H8.33317C7.06752 18.9584 6.0415 17.9323 6.0415 16.6667V8.33335Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
