<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M11.875 1.5C14.713 1.5 17.3812 2.60521 19.388 4.612C21.3948 6.6188 22.5 9.28696 22.5 12.125C22.5 14.963 21.3948 17.6312 19.388 19.638C17.3812 21.6448 14.713 22.75 11.875 22.75C9.03696 22.75 6.3688 21.6448 4.36196 19.638C2.35521 17.6312 1.25 14.963 1.25 12.125C1.25 9.28696 2.35521 6.6188 4.36196 4.612C6.3688 2.60521 9.03696 1.5 11.875 1.5ZM18.8682 11.4936L21.233 11.4906C21.1488 10.2369 20.8173 9.04997 20.2868 7.97818L18.2402 9.16212C18.5765 9.88171 18.7942 10.6672 18.8682 11.4936ZM17.6152 8.08526L19.662 6.90124C18.9793 5.887 18.104 5.01289 17.0889 4.33156L15.9075 6.37978C16.5698 6.84599 17.1482 7.4236 17.6152 8.08526ZM16.0112 3.70792C14.9388 3.17883 13.7515 2.84888 12.4976 2.76628V5.13105C13.3241 5.20402 14.1099 5.42071 14.8298 5.75606L16.0112 3.70792ZM11.2524 5.13105V2.76628C9.98188 2.84996 8.77976 3.18763 7.6963 3.72909L8.88796 5.77112C9.61665 5.42718 10.4136 5.2051 11.2524 5.13105ZM7.81334 6.40003L6.62168 4.35796C5.61019 5.04427 4.73957 5.92303 4.06197 6.94087L6.11459 8.11444C6.57835 7.45033 7.15352 6.86953 7.81334 6.40003ZM5.49477 9.19433L3.44207 8.02072C2.91717 9.09484 2.5919 10.2834 2.51404 11.538L4.87869 11.529C4.94854 10.702 5.16224 9.91541 5.49477 9.19433ZM4.88342 12.7741L2.51853 12.7831C2.60585 14.0364 2.94045 15.2224 3.47378 16.293L5.51735 15.104C5.17917 14.3852 4.95949 13.6002 4.88342 12.7741ZM6.14497 16.1794L4.10119 17.3685C4.78626 18.381 5.66398 19.2527 6.68095 19.9315L7.85713 17.8803C7.19365 17.4158 6.61363 16.8398 6.14497 16.1794ZM8.93627 18.5015L7.76009 20.5527C8.83363 21.079 10.0218 21.4058 11.2761 21.4852L11.2702 19.1205C10.4433 19.0496 9.65699 18.8349 8.93627 18.5015ZM11.875 6.34828C8.6897 6.34828 6.09824 8.93966 6.09824 12.125C6.09824 15.3103 8.6897 17.9018 11.875 17.9018C15.0603 17.9018 17.6517 15.3103 17.6517 12.125C17.6517 8.9397 15.0603 6.34828 11.875 6.34828ZM12.5153 19.1174L12.5213 21.4822C13.7748 21.3965 14.9612 21.0634 16.0324 20.5315L14.8459 18.4864C14.1267 18.8236 13.3415 19.0423 12.5153 19.1174ZM15.922 17.8601L17.1087 19.9054C18.122 19.2216 18.9949 18.345 19.675 17.329L17.6253 16.1502C17.1599 16.813 16.5831 17.3923 15.922 17.8601ZM18.8697 12.7387C18.7978 13.5654 18.5821 14.3514 18.2477 15.0718L20.2973 16.2506C20.8251 15.1776 21.1534 13.9899 21.2345 12.7357L18.8697 12.7387ZM8.20671 10.7048H9.1005V13.0463H9.79689V13.8359H7.42806V13.0463H8.20671V11.648C8.1738 11.6919 8.12258 11.7367 8.05319 11.7823C7.98367 11.8281 7.90597 11.8701 7.82014 11.9085C7.73423 11.9469 7.64645 11.9779 7.55693 12.0017C7.46732 12.0255 7.3878 12.0373 7.3184 12.0373V11.2258C7.37684 11.2258 7.45275 11.2057 7.54597 11.1655C7.63919 11.1253 7.73145 11.0768 7.82288 11.0202C7.91419 10.9635 7.99645 10.906 8.06962 10.8474C8.14271 10.789 8.18836 10.7414 8.20671 10.7048ZM12.9333 11.563C12.8601 11.3601 12.7596 11.1846 12.6317 11.0365C12.5037 10.8884 12.353 10.7724 12.1794 10.6883C12.0057 10.6043 11.8165 10.5622 11.6118 10.5622C11.4071 10.5622 11.2171 10.6043 11.0416 10.6883C10.8661 10.7725 10.7144 10.8885 10.5864 11.0365C10.4584 11.1846 10.3589 11.3601 10.2876 11.563C10.2163 11.7658 10.1807 11.9879 10.1807 12.2292C10.1807 12.4704 10.2163 12.6925 10.2876 12.8954C10.3589 13.0983 10.4584 13.2738 10.5864 13.4218C10.7144 13.5699 10.8661 13.685 11.0416 13.7673C11.217 13.8495 11.407 13.8907 11.6118 13.8907C11.8165 13.8907 12.0057 13.8495 12.1794 13.7673C12.3529 13.685 12.5037 13.5698 12.6317 13.4218C12.7596 13.2738 12.8601 13.0983 12.9333 12.8954C13.0064 12.6925 13.043 12.4704 13.043 12.2292C13.043 11.9879 13.0064 11.7658 12.9333 11.563ZM12.0066 12.8488C11.9042 13.006 11.7726 13.0846 11.6118 13.0846C11.4436 13.0846 11.3093 13.006 11.2088 12.8488C11.1082 12.6917 11.058 12.4851 11.058 12.2292C11.058 11.9734 11.1082 11.7658 11.2088 11.6068C11.3093 11.4478 11.4436 11.3683 11.6118 11.3683C11.7726 11.3683 11.9042 11.4477 12.0066 11.6068C12.1089 11.7658 12.1602 11.9734 12.1602 12.2292C12.1602 12.4851 12.1089 12.6917 12.0066 12.8488ZM15.9985 11.0365C16.1264 11.1846 16.2269 11.3601 16.3001 11.563C16.3732 11.7658 16.4098 11.9879 16.4098 12.2292C16.4098 12.4704 16.3732 12.6925 16.3001 12.8954C16.2269 13.0983 16.1264 13.2738 15.9985 13.4218C15.8705 13.5698 15.7197 13.685 15.5462 13.7673C15.3725 13.8495 15.1833 13.8907 14.9786 13.8907C14.7738 13.8907 14.5838 13.8495 14.4084 13.7673C14.2328 13.685 14.0811 13.5699 13.9532 13.4218C13.8252 13.2738 13.7257 13.0983 13.6544 12.8954C13.5831 12.6925 13.5474 12.4704 13.5474 12.2292C13.5474 11.9879 13.5831 11.7658 13.6544 11.563C13.7257 11.3601 13.8252 11.1846 13.9532 11.0365C14.0811 10.8885 14.2328 10.7725 14.4084 10.6883C14.5838 10.6043 14.7738 10.5622 14.9786 10.5622C15.1833 10.5622 15.3725 10.6043 15.5462 10.6883C15.7198 10.7724 15.8705 10.8884 15.9985 11.0365ZM14.9786 13.0846C15.1394 13.0846 15.271 13.006 15.3734 12.8488C15.4757 12.6917 15.527 12.4851 15.527 12.2292C15.527 11.9734 15.4757 11.7658 15.3734 11.6068C15.271 11.4477 15.1394 11.3683 14.9786 11.3683C14.8104 11.3683 14.6761 11.4478 14.5756 11.6068C14.475 11.7658 14.4248 11.9734 14.4248 12.2292C14.4248 12.4851 14.475 12.6917 14.5756 12.8488C14.6761 13.006 14.8104 13.0846 14.9786 13.0846Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
