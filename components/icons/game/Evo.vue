<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M20.3633 4.36328L4.36328 8.32913V11.7043L18.6612 8.32913L20.3633 4.36328Z"
                :class="fillColor"
            />
            <path
                d="M4.36328 12.5481V15.6702L16.1931 14.1513L17.4697 10.6918L4.36328 12.5481Z"
                :class="fillColor"
            />
            <path d="M4.36328 16.4296V19.636H13.8952L15.0867 16.4296H4.36328Z" :class="fillColor" />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
