<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M20.7623 1.45508L5.83325 5.53499L3.63672 20.0066L20.2187 23.2051L20.7623 1.45508ZM17.4895 20.4447C17.8245 19.6912 18.6573 17.3291 18.925 16.4815L18.1462 16.3437C17.6492 17.3117 17.0332 18.1435 16.5192 18.5952C15.8595 19.1612 15.0962 19.2584 13.106 19.0017L13.094 19.0001C11.8047 18.8361 10.9088 18.7221 10.4524 18.3919C10.0154 18.0908 9.90886 17.6226 10.0087 16.6606L10.4169 12.7809L12.9315 12.656C15.2745 12.5438 15.4483 12.6725 15.6058 14.3504H16.3905L16.937 9.69389L16.1346 9.78874C15.6198 11.42 15.4616 11.6164 12.9995 11.7963L10.4976 11.9785L10.9945 7.26326C11.0582 6.66106 11.1188 6.58653 11.9634 6.39985L14.0534 5.9339C16.0614 5.483 16.8009 5.47698 17.133 5.92863C17.4392 6.36748 17.6566 6.85602 17.786 7.85342L18.6151 7.61479C18.6353 7.24721 18.6552 6.84541 18.6751 6.44446C18.7243 5.45201 18.7732 4.46474 18.8259 4.01438L6.54972 6.98022L6.50165 7.48908C8.55914 7.14734 8.76548 7.17594 8.59168 8.90727L7.83214 16.5755C7.67831 18.1443 7.46235 18.276 5.18891 18.1255L5.14453 18.6178L17.4895 20.4447Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
