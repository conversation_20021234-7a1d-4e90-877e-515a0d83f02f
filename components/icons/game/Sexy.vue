<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M14.1796 10.0003C14.4567 9.99484 14.7322 10.0511 14.9852 10.1652C15.2404 10.2803 15.4665 10.4512 15.6467 10.6654C15.8177 10.8686 15.8417 11.1576 15.7064 11.3861C15.4812 11.7668 15.1408 12.1679 14.7566 12.4798C14.3894 12.778 13.8872 13.0716 13.3439 13.0716C13.3347 13.0716 13.3255 13.0714 13.3163 13.071C12.5117 13.0355 11.7538 12.6836 11.2075 12.0918C11.0684 11.9411 11.0121 11.7319 11.0567 11.5318C11.1013 11.3317 11.2412 11.1662 11.4311 11.0889C11.4721 11.0722 11.573 11.0167 11.7776 10.9004C11.7808 10.8986 11.7839 10.8968 11.7871 10.895C11.9682 10.7921 12.1972 10.6619 12.4447 10.5355C12.9171 10.2944 13.5746 10.0027 14.1796 10.0003ZM14.2466 11.2507C14.2317 11.25 14.2168 11.2498 14.2019 11.2502L14.187 11.2503C13.91 11.2503 13.4889 11.406 13.0131 11.6489C12.963 11.6744 12.9136 11.7003 12.865 11.7261C13.0217 11.78 13.1863 11.8124 13.3545 11.8214C13.4724 11.8167 13.6904 11.7353 13.9687 11.5094C14.0658 11.4305 14.1595 11.3426 14.2466 11.2507Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M5.98976 10.0003C5.71257 9.99484 5.43714 10.0511 5.18409 10.1652C4.92891 10.2803 4.70283 10.4512 4.52257 10.6654C4.35157 10.8686 4.32763 11.1576 4.46288 11.3861C4.68815 11.7668 5.02855 12.1679 5.41271 12.4798C5.77996 12.778 6.2821 13.0716 6.82544 13.0716C6.83462 13.0716 6.84379 13.0714 6.85296 13.071C7.65757 13.0355 8.41556 12.6836 8.96186 12.0918C9.10092 11.9411 9.15726 11.7319 9.11264 11.5318C9.06803 11.3317 8.92812 11.1662 8.73824 11.0889C8.69721 11.0722 8.59635 11.0167 8.39169 10.9004C8.38855 10.8986 8.38538 10.8968 8.38221 10.895C8.2011 10.7921 7.97207 10.6619 7.72457 10.5355C7.25223 10.2944 6.59474 10.0027 5.98976 10.0003ZM5.92275 11.2507C5.9376 11.25 5.95249 11.2498 5.9674 11.2502L5.98231 11.2503C6.25934 11.2503 6.68042 11.406 7.15623 11.6489C7.20632 11.6744 7.25571 11.7003 7.30429 11.7261C7.14762 11.78 6.983 11.8124 6.81478 11.8214C6.6969 11.8167 6.47887 11.7353 6.20066 11.5094C6.10347 11.4305 6.00981 11.3426 5.92275 11.2507Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1.75113 7.22758C2.18585 6.85974 2.75618 6.66665 3.33335 6.66665H5.83335C5.83992 6.66665 5.84648 6.66675 5.85304 6.66696C7.35196 6.7142 8.80155 7.15698 10 7.93968C11.1985 7.15698 12.6481 6.7142 14.147 6.66696C14.1536 6.66675 14.1601 6.66665 14.1667 6.66665H16.6667C17.2439 6.66665 17.8142 6.85974 18.2489 7.22758C18.6868 7.59809 18.9584 8.12522 18.9584 8.7019V11.5224C18.9584 12.6601 18.4234 13.7253 17.5167 14.4925C16.6156 15.255 15.4136 15.6707 14.177 15.673C12.7596 15.7195 11.4167 16.1793 10.3743 16.9588C10.1524 17.1248 9.84766 17.1248 9.62572 16.9588C8.58332 16.1793 7.24045 15.7195 5.823 15.673C4.58643 15.6707 3.38441 15.255 2.48336 14.4925C1.57665 13.7253 1.04169 12.6601 1.04169 11.5224V8.7019C1.04169 8.12522 1.31325 7.59809 1.75113 7.22758ZM3.33335 7.91665C3.02647 7.91665 2.74895 8.02071 2.55856 8.18182C2.37131 8.34026 2.29169 8.53054 2.29169 8.7019V11.5224C2.29169 12.2548 2.6347 12.9832 3.29079 13.5383C3.95003 14.0961 4.86343 14.4231 5.83335 14.4231C5.83992 14.4231 5.84648 14.4232 5.85304 14.4234C7.35196 14.4706 8.80155 14.9134 10 15.6961C11.1985 14.9134 12.6481 14.4706 14.147 14.4234C14.1536 14.4232 14.1601 14.4231 14.1667 14.4231C15.1366 14.4231 16.05 14.0961 16.7093 13.5383C17.3653 12.9832 17.7084 12.2548 17.7084 11.5224V8.7019C17.7084 8.53054 17.6287 8.34026 17.4415 8.18182C17.2511 8.02071 16.9736 7.91665 16.6667 7.91665H14.1767C12.7594 7.9632 11.4167 8.42296 10.3743 9.20243C10.1524 9.36839 9.84766 9.36839 9.62572 9.20243C8.58339 8.42296 7.24064 7.9632 5.8233 7.91665H3.33335Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M7.2654 3.57557C7.48156 3.40929 7.74713 3.33331 8.00002 3.33331H8.75897L8.76792 3.33357C9.19723 3.34587 9.62185 3.44409 10 3.62474C10.3782 3.44409 10.8028 3.34587 11.2321 3.33357L11.2411 3.33331H11.25H12C12.2529 3.33331 12.5185 3.40929 12.7346 3.57557C12.9544 3.74464 13.125 4.01432 13.125 4.34293V5.11216C13.125 5.59378 12.8754 6.0102 12.515 6.28746C12.1609 6.55985 11.7087 6.69661 11.2603 6.69867C10.9078 6.71013 10.5874 6.81473 10.3514 6.97518L10 7.21406L9.64864 6.97518C9.41262 6.81473 9.09225 6.71013 8.73974 6.69867C8.2913 6.69661 7.83918 6.55985 7.48507 6.28746C7.12463 6.0102 6.87502 5.59378 6.87502 5.11216V4.34293C6.87502 4.01432 7.04561 3.74464 7.2654 3.57557ZM11.2596 4.58331C10.9074 4.59489 10.5873 4.69945 10.3514 4.8598L10 5.09868L9.64864 4.8598C9.41278 4.69945 9.09269 4.59489 8.74046 4.58331H8.12502V5.11216C8.12502 5.14057 8.1388 5.21329 8.24721 5.29668C8.35923 5.38285 8.53879 5.4487 8.75002 5.4487H8.75897L8.76792 5.44895C9.19723 5.46125 9.62185 5.55948 10 5.74012C10.3782 5.55948 10.8028 5.46125 11.2321 5.44895L11.2411 5.4487H11.25C11.4613 5.4487 11.6408 5.38285 11.7528 5.29668C11.8612 5.21329 11.875 5.14058 11.875 5.11216V4.58331H11.2596Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
