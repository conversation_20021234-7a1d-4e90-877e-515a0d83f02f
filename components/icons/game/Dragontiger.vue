<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.46902 18.7191C8.46902 20.6692 10.0498 22.25 11.9998 22.25C13.9498 22.25 15.5305 20.6692 15.5305 18.7191V15.5146L17.5327 14.5135C17.7039 14.4279 17.8248 14.2667 17.859 14.0784L19.0281 7.6429C19.0664 7.43203 18.9909 7.21657 18.8293 7.0758L18.43 7.53423C18.8293 7.0758 18.8293 7.0758 18.8293 7.0758L18.8275 7.07429L18.8248 7.07193L18.8164 7.06473L18.7878 7.0407C18.7636 7.02055 18.7292 6.99233 18.6848 6.95719C18.5961 6.88693 18.4678 6.78889 18.3029 6.67238C17.9735 6.43959 17.4964 6.13165 16.8971 5.82415C15.7019 5.21088 13.9981 4.58795 11.9998 4.58795C10.0015 4.58795 8.29767 5.21088 7.10244 5.82415C6.50315 6.13165 6.02605 6.43959 5.69667 6.67238C5.53181 6.78889 5.40345 6.88693 5.31476 6.95719C5.2704 6.99233 5.23592 7.02055 5.21172 7.0407L5.18316 7.06473L5.17474 7.07193L5.17201 7.07429L5.17102 7.07515C5.17102 7.07515 5.17028 7.0758 5.5696 7.53423L5.17028 7.0758C5.00868 7.21657 4.93313 7.43203 4.97144 7.6429L6.14056 14.0784C6.17477 14.2667 6.29566 14.4279 6.46684 14.5135L8.46902 15.5146V18.7191ZM6.23308 7.78516C6.28184 7.7489 6.33703 7.70874 6.39842 7.66536C6.6894 7.45971 7.11748 7.18307 7.6575 6.90599C8.74088 6.35011 10.2522 5.80388 11.9998 5.80388C13.7474 5.80388 15.2587 6.35011 16.3421 6.90599C16.8821 7.18307 17.3102 7.45971 17.6011 7.66536C17.6625 7.70874 17.7177 7.7489 17.7665 7.78516L16.717 13.5619L14.6507 14.5951C14.4447 14.6981 14.3146 14.9086 14.3146 15.1389V18.7191C14.3146 19.9976 13.2782 21.0341 11.9998 21.0341C10.7213 21.0341 9.68492 19.9976 9.68492 18.7191V15.1389C9.68492 14.9086 9.55482 14.6981 9.34885 14.5951L7.28253 13.5619L6.23308 7.78516Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M11.9999 12.2783C12.2087 12.2783 12.403 12.1711 12.5143 11.9944C13.0703 11.1117 13.9879 10.5198 15.0213 10.3775L15.1761 10.3562C16.2638 10.2064 17.2659 9.68434 18.012 8.87883L20.6061 6.07799C20.8343 5.83165 20.8195 5.44699 20.5732 5.21883C20.3269 4.99067 19.9422 5.0054 19.7141 5.25174L17.12 8.05256C16.5649 8.65183 15.8194 9.04021 15.0103 9.15163L14.8555 9.17295C13.7493 9.32528 12.7418 9.86078 11.9999 10.6717C11.258 9.86078 10.2505 9.32529 9.14427 9.17295L8.98952 9.15163C8.18038 9.04022 7.43481 8.65183 6.87975 8.05256L4.28567 5.25174C4.05752 5.0054 3.67287 4.99067 3.42653 5.21883C3.1802 5.44699 3.16547 5.83165 3.39362 6.07799L5.98771 8.87882C6.7338 9.68433 7.73595 10.2064 8.82359 10.3562L8.97835 10.3775C10.0118 10.5198 10.9295 11.1117 11.4855 11.9944C11.5968 12.1711 11.791 12.2783 11.9999 12.2783Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M16.6927 7.05361C17.0245 7.10555 17.3355 6.87873 17.3874 6.547C17.4741 5.99338 17.9075 5.1944 18.3895 4.46678C18.6202 4.11849 18.8451 3.81114 19.0125 3.59073C19.096 3.48075 19.1648 3.39299 19.2122 3.33325C19.236 3.30339 19.2543 3.28057 19.2665 3.26552L19.28 3.24886L19.2831 3.24508L19.2837 3.24433C19.4332 3.06273 19.4645 2.81119 19.3641 2.59848C19.2637 2.38573 19.0496 2.25 18.8144 2.25C16.7153 2.25 15.2953 2.9204 14.3927 3.62253C13.9453 3.97054 13.6316 4.32102 13.4271 4.58982C13.3247 4.7243 13.2494 4.83876 13.198 4.92294C13.1723 4.96505 13.1525 4.99966 13.1382 5.0255C13.1311 5.03842 13.1254 5.04917 13.121 5.05756L13.1154 5.06839L13.1133 5.07246L13.1124 5.07414C13.1124 5.07414 13.1117 5.0756 13.6558 5.34686L13.1117 5.0756C12.9619 5.37609 13.084 5.74114 13.3845 5.89096C13.6838 6.04016 14.0471 5.91962 14.198 5.62182L14.2031 5.61232C14.209 5.60171 14.2197 5.58284 14.2355 5.5569C14.2673 5.50496 14.3195 5.42498 14.3947 5.32612C14.5453 5.12825 14.7867 4.85651 15.1392 4.5823C15.6552 4.18091 16.4244 3.76213 17.5267 3.57159"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M7.30735 7.05361C7.63907 7.00168 7.86589 6.69065 7.81395 6.35892C7.68169 5.51408 7.10136 4.51556 6.62427 3.7953C6.57351 3.71866 6.52307 3.64396 6.47342 3.57159C7.57573 3.76213 8.34489 4.18091 8.86092 4.5823C9.21344 4.85651 9.45485 5.12825 9.6054 5.32612C9.68062 5.42498 9.73283 5.50496 9.76457 5.5569C9.78042 5.58284 9.79111 5.60171 9.79696 5.61232L9.80205 5.62173L9.80135 5.62036L9.80085 5.61935L9.80205 5.62173C9.953 5.91953 10.3163 6.04016 10.6156 5.89096C10.9161 5.74114 11.0382 5.37609 10.8884 5.0756L10.3443 5.34686C10.8884 5.0756 10.8884 5.0756 10.8884 5.0756L10.8877 5.07414L10.8868 5.07246L10.8847 5.06839L10.8791 5.05756C10.8747 5.04917 10.869 5.03842 10.8619 5.0255C10.8476 4.99966 10.8279 4.96505 10.8021 4.92294C10.7507 4.83876 10.6754 4.7243 10.573 4.58982C10.3685 4.32102 10.0548 3.97054 9.60744 3.62253C8.70479 2.9204 7.28479 2.25 5.18575 2.25C4.9505 2.25 4.73636 2.38573 4.63596 2.59848C4.53557 2.81118 4.56688 3.0627 4.71632 3.2443C4.71632 3.2443 4.71643 3.24442 5.18575 2.85796L4.71632 3.2443L4.71697 3.24508L4.72006 3.24886L4.7336 3.26552C4.74579 3.28057 4.76415 3.30339 4.78787 3.33325C4.83533 3.39299 4.90409 3.48075 4.98761 3.59073C5.15501 3.81114 5.37989 4.11849 5.61059 4.46678C6.09256 5.1944 6.52602 5.99338 6.61269 6.547C6.66462 6.87873 6.97564 7.10555 7.30735 7.05361Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13.2782 12.979C13.4067 13.2892 13.7623 13.4365 14.0725 13.308L15.6927 12.6369C16.0029 12.5084 16.1502 12.1528 16.0217 11.8426C15.8932 11.5323 15.5375 11.385 15.2273 11.5135L13.6072 12.1847C13.297 12.3132 13.1497 12.6688 13.2782 12.979Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M10.7215 12.979C10.85 12.6688 10.7027 12.3132 10.3925 12.1847L8.77235 11.5135C8.46215 11.385 8.10652 11.5323 7.97802 11.8426C7.84953 12.1528 7.99683 12.5084 8.30704 12.6369L9.92721 13.308C10.2374 13.4365 10.593 13.2892 10.7215 12.979Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M21.7816 13.3372C21.7816 13.3372 21.7818 13.3366 21.2116 13.1258L21.7816 13.3372C21.8397 13.1799 21.8306 13.0052 21.756 12.8551C21.6813 12.705 21.5481 12.5924 21.3877 12.5439L21.0277 12.4349C21.4487 11.7542 21.6864 11.0829 21.821 10.5383C21.9124 10.1682 21.9571 9.85281 21.979 9.62664C21.9899 9.51339 21.9952 9.42201 21.9978 9.35671C21.999 9.32405 21.9996 9.29787 21.9998 9.27868L22 9.25521L22 9.24752L22 9.24471L22 9.24356C22 9.24356 21.9999 9.24259 21.392 9.24948L21.9999 9.24259C21.9978 9.05086 21.9053 8.87138 21.7504 8.75836C21.5955 8.64533 21.3964 8.61201 21.2131 8.66844L17.7456 9.73631C17.4247 9.83513 17.2447 10.1754 17.3435 10.4963C17.4423 10.8172 17.7826 10.9972 18.1035 10.8984L20.6731 10.107C20.6632 10.1523 20.6524 10.1989 20.6405 10.2467C20.4898 10.857 20.1851 11.6461 19.5715 12.381C19.4407 12.5376 19.3972 12.7494 19.4557 12.9449C19.5141 13.1404 19.6667 13.2935 19.862 13.3526L20.3607 13.5035C20.3556 13.5129 20.3504 13.5223 20.3452 13.5319C20.1334 13.9162 19.808 14.4204 19.3536 14.9051C18.4535 15.8649 17.0629 16.7394 14.9979 16.4827C14.6647 16.4413 14.361 16.6779 14.3196 17.0111C14.2782 17.3443 14.5147 17.648 14.8479 17.6894C17.3957 18.0061 19.1494 16.9004 20.2405 15.7368C20.7817 15.1597 21.1635 14.5663 21.4102 14.1185C21.5339 13.8939 21.6249 13.7036 21.6857 13.5673C21.7162 13.499 21.7392 13.4441 21.7551 13.4049C21.763 13.3853 21.7692 13.3696 21.7736 13.3581L21.779 13.3441L21.7807 13.3395L21.7816 13.3372Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.63929 13.5035C3.6444 13.5129 3.64958 13.5223 3.65483 13.5319C3.86656 13.9162 4.19197 14.4204 4.64642 14.9051C5.54646 15.8649 6.93705 16.7394 9.00214 16.4827C9.33533 16.4413 9.63901 16.6779 9.68043 17.0111C9.72184 17.3443 9.4853 17.648 9.15211 17.6894C6.60433 18.0061 4.85057 16.9004 3.75947 15.7368C3.21834 15.1597 2.83648 14.5663 2.58983 14.1185C2.46607 13.8939 2.37513 13.7036 2.31427 13.5673C2.28381 13.499 2.2608 13.4441 2.24492 13.4049C2.23697 13.3853 2.23079 13.3696 2.22635 13.3581L2.22098 13.3441L2.21926 13.3395L2.21863 13.3378C2.21863 13.3378 2.21817 13.3366 2.7884 13.1258L2.21817 13.3366C2.16006 13.1794 2.16942 13.0052 2.24404 12.8551C2.31865 12.705 2.45191 12.5924 2.61232 12.5439L2.97228 12.4349C2.55131 11.7542 2.31356 11.0829 2.17903 10.5383C2.08761 10.1682 2.0429 9.85281 2.02101 9.62664C2.01006 9.51339 2.00477 9.42201 2.00225 9.35671C2.00098 9.32405 2.00041 9.29787 2.00017 9.27868L2 9.25521L2.00002 9.24752L2.00004 9.24471L2.00005 9.24356C2.00005 9.24356 2.00006 9.24259 2.60797 9.24948L2.00006 9.24259C2.00223 9.05086 2.09473 8.87138 2.24962 8.75836C2.40451 8.64533 2.60365 8.61201 2.7869 8.66844L6.2544 9.73631C6.57529 9.83513 6.75531 10.1754 6.65649 10.4963C6.55767 10.8172 6.21743 10.9972 5.89654 10.8984L3.32686 10.107C3.33681 10.1523 3.34765 10.1989 3.35945 10.2467C3.5102 10.857 3.81493 11.6461 4.42854 12.381C4.55932 12.5376 4.60281 12.7494 4.54435 12.9449C4.48589 13.1404 4.33326 13.2935 4.13797 13.3526L3.63929 13.5035Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.4604 18.6473C12.6402 18.9309 13.0158 19.0151 13.2994 18.8353L15.2479 17.6003C15.5315 17.4206 15.6157 17.045 15.436 16.7614C15.2562 16.4778 14.8806 16.3936 14.597 16.5733L12.6485 17.8083C12.3649 17.988 12.2807 18.3636 12.4604 18.6473Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M11.539 18.6473C11.7188 18.3636 11.6346 17.988 11.351 17.8083L9.40244 16.5733C9.11884 16.3936 8.74323 16.4778 8.56349 16.7614C8.38375 17.045 8.46794 17.4206 8.75154 17.6003L10.7001 18.8353C10.9837 19.0151 11.3593 18.9309 11.539 18.6473Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
