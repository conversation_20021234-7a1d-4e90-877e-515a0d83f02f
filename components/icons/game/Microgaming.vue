<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M12.3645 2.90918C11.0011 2.91021 9.65687 3.23039 8.4394 3.84409C7.22194 4.45778 6.16505 5.34796 5.35334 6.44336C4.54163 7.53875 3.99764 8.80896 3.76491 10.1523C3.53218 11.4956 3.61717 12.8748 4.01308 14.1794C4.17098 13.9535 4.32803 13.7259 4.4834 13.4991C5.09481 12.6033 5.69222 11.6997 6.27563 10.7882L6.70563 10.1013C6.8512 9.87286 6.99286 9.64303 7.13059 9.4118C7.4153 8.95579 7.68405 8.4855 7.95364 8.0194C8.08885 7.78678 8.21567 7.54995 8.34669 7.31565C8.4777 7.08134 8.6062 6.84535 8.72798 6.60685C8.84975 6.36834 8.97993 6.13404 9.09415 5.89385L9.44101 5.17666L9.4662 5.12627C9.55948 4.93925 9.72241 4.7963 9.91998 4.72814C10.1175 4.65999 10.334 4.67207 10.5227 4.76179C10.831 4.91128 10.9737 5.23628 10.9737 5.55793C10.9737 5.97196 10.8897 6.40278 10.8654 6.82772C10.841 7.25266 10.81 7.68012 10.7932 8.10674C10.7596 8.95915 10.7386 9.81155 10.7503 10.6589C10.7621 11.5063 10.7965 12.3503 10.8864 13.1725C10.9287 13.5754 10.9915 13.976 11.0745 14.3725C11.1426 14.7288 11.2555 15.075 11.4104 15.403C11.4583 15.5052 11.5253 15.5973 11.6078 15.6742C11.6582 15.7087 11.6238 15.6919 11.649 15.6944C11.649 15.6986 11.67 15.6885 11.6817 15.6902L11.7086 15.681L11.722 15.6768H11.7279C11.7489 15.6675 11.7724 15.6583 11.7766 15.6549C11.7808 15.6516 11.7724 15.6549 11.7724 15.6549C11.7926 15.6423 11.8136 15.6297 11.8396 15.6104C11.9665 15.512 12.0833 15.4011 12.1881 15.2795C12.4451 14.9819 12.6803 14.6662 12.8919 14.3348C13.1153 13.9913 13.3295 13.6327 13.5353 13.2674C13.9476 12.535 14.3331 11.7775 14.7001 11.0083C14.8838 10.6248 15.0632 10.2382 15.2385 9.8485C15.4123 9.46135 15.5862 9.06496 15.7424 8.68201C15.808 8.52445 15.9231 8.39255 16.0704 8.30629C16.2177 8.22002 16.389 8.18409 16.5586 8.20393C16.7281 8.22376 16.8865 8.29829 17.0099 8.41623C17.1333 8.53416 17.2149 8.68908 17.2423 8.85753C17.2465 8.95915 17.2583 9.08764 17.27 9.20437L17.3095 9.56549C17.3389 9.80735 17.3742 10.0484 17.4145 10.2902C17.4945 10.771 17.5985 11.2475 17.7261 11.7179C17.8509 12.1881 18.0109 12.6482 18.2048 13.0944C18.3939 13.5393 18.6416 13.9571 18.9413 14.3364C18.974 14.3769 18.9904 14.428 18.9874 14.4799C18.9844 14.5318 18.9622 14.5808 18.925 14.6172C18.8879 14.6535 18.8386 14.6748 18.7866 14.6768C18.7346 14.6788 18.6838 14.6613 18.644 14.6278C18.2409 14.2795 17.8858 13.879 17.5883 13.437C17.2902 13.0007 17.0249 12.5429 16.7947 12.0673C16.6402 11.7523 16.5024 11.4315 16.3706 11.1074C16.2866 11.2972 16.1967 11.487 16.1069 11.6759C15.7272 12.467 15.33 13.2522 14.8941 14.0274C14.6758 14.4154 14.4473 14.7992 14.1979 15.1813C13.9435 15.5789 13.6606 15.9575 13.3513 16.3142C13.1725 16.5193 12.9726 16.7051 12.755 16.8684C12.6937 16.9138 12.624 16.9583 12.5518 17.002C12.5039 17.0289 12.4535 17.0549 12.404 17.0792L12.32 17.1154H12.3108L12.2646 17.1338L12.2327 17.1447L12.1697 17.1649C12.1268 17.1775 12.0857 17.1909 12.0412 17.2019C11.9468 17.2244 11.8507 17.239 11.7539 17.2455C11.5412 17.2612 11.3274 17.2347 11.1249 17.1674C10.937 17.1045 10.7616 17.0089 10.6067 16.8852C10.3618 16.6755 10.1618 16.4184 10.0188 16.1294C9.79685 15.6765 9.63506 15.1966 9.53759 14.7017C9.44223 14.2517 9.37046 13.7971 9.32259 13.3396C9.22433 12.4418 9.18653 11.5567 9.17477 10.674C9.16974 10.2541 9.17477 9.83003 9.17477 9.41012C9.01856 9.70994 8.85983 10.0097 8.69774 10.3062C8.56673 10.5506 8.43403 10.7941 8.29798 11.0343L7.89149 11.7582C7.34475 12.7198 6.7787 13.6679 6.20172 14.6085C5.82715 15.2174 5.45006 15.8195 5.06373 16.42C5.89198 17.6838 7.03418 18.7109 8.37856 19.4009C9.72293 20.0908 11.2234 20.4199 12.7331 20.3559C14.2429 20.2919 15.7101 19.8371 16.9913 19.0359C18.2725 18.2347 19.3237 17.1146 20.042 15.7852C20.7604 14.4559 21.1212 12.9628 21.0893 11.4521C21.0574 9.94142 20.6338 8.46491 19.8599 7.16707C19.0861 5.86923 17.9885 4.79454 16.6746 4.04817C15.3608 3.30181 13.8756 2.90934 12.3645 2.90918V2.90918Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
