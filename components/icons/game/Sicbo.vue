<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M14.5 13.4784C14.5 12.7494 14.21 12.0494 13.694 11.5344C13.179 11.0184 12.479 10.7284 11.75 10.7284C9.764 10.7284 6.736 10.7284 4.75 10.7284C4.02 10.7284 3.321 11.0184 2.805 11.5344C2.29 12.0494 2 12.7494 2 13.4784V20.4784C2 21.2084 2.29 21.9074 2.805 22.4234C3.321 22.9384 4.02 23.2284 4.75 23.2284H11.75C12.479 23.2284 13.179 22.9384 13.694 22.4234C14.21 21.9074 14.5 21.2084 14.5 20.4784V13.4784ZM13 13.4784V20.4784C13 20.8104 12.868 21.1284 12.634 21.3624C12.399 21.5974 12.081 21.7284 11.75 21.7284H4.75C4.418 21.7284 4.1 21.5974 3.866 21.3624C3.631 21.1284 3.5 20.8104 3.5 20.4784V13.4784C3.5 13.1474 3.631 12.8294 3.866 12.5944C4.1 12.3604 4.418 12.2284 4.75 12.2284H11.75C12.081 12.2284 12.399 12.3604 12.634 12.5944C12.868 12.8294 13 13.1474 13 13.4784Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M16.496 16.3894C16.644 16.4434 16.788 16.4944 16.927 16.5444C17.613 16.7914 18.369 16.7554 19.029 16.4454C19.689 16.1344 20.198 15.5744 20.445 14.8884C21.118 13.0194 22.143 10.1704 22.816 8.30136C23.063 7.61536 23.027 6.85936 22.716 6.19936C22.406 5.53936 21.846 5.03036 21.159 4.78336C19.291 4.11036 16.442 3.08536 14.573 2.41236C13.887 2.16536 13.13 2.20136 12.471 2.51236C11.811 2.82236 11.301 3.38236 11.054 4.06936C10.632 5.24236 10.07 6.80336 9.544 8.26536C9.404 8.65436 9.606 9.08436 9.996 9.22436C10.385 9.36436 10.815 9.16236 10.955 8.77336L12.466 4.57736C12.578 4.26536 12.809 4.01036 13.109 3.86936C13.409 3.72836 13.753 3.71136 14.065 3.82436L20.651 6.19436C20.963 6.30636 21.218 6.53836 21.359 6.83836C21.5 7.13836 21.517 7.48236 21.404 7.79336L19.034 14.3804C18.922 14.6924 18.69 14.9464 18.39 15.0884C18.09 15.2294 17.746 15.2454 17.435 15.1334L17.004 14.9784C16.614 14.8384 16.184 15.0404 16.044 15.4304C15.904 15.8194 16.107 16.2494 16.496 16.3894Z"
                :class="fillColor"
            />
            <path
                d="M8.25 17.9784C8.80228 17.9784 9.25 17.5306 9.25 16.9784C9.25 16.4261 8.80228 15.9784 8.25 15.9784C7.69772 15.9784 7.25 16.4261 7.25 16.9784C7.25 17.5306 7.69772 17.9784 8.25 17.9784Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M16.088 8.53736C15.569 8.35036 14.996 8.62036 14.809 9.14036C14.622 9.65936 14.892 10.2324 15.411 10.4194C15.93 10.6064 16.504 10.3364 16.691 9.81736C16.878 9.29836 16.608 8.72436 16.088 8.53736Z"
                :class="fillColor"
            />
            <path
                d="M10.75 15.4784C11.3023 15.4784 11.75 15.0306 11.75 14.4784C11.75 13.9261 11.3023 13.4784 10.75 13.4784C10.1977 13.4784 9.75 13.9261 9.75 14.4784C9.75 15.0306 10.1977 15.4784 10.75 15.4784Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M11.75 19.4784C11.75 18.9264 11.302 18.4784 10.75 18.4784C10.198 18.4784 9.75 18.9264 9.75 19.4784C9.75 20.0304 10.198 20.4784 10.75 20.4784C11.302 20.4784 11.75 20.0304 11.75 19.4784Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M18.196 13.0164C18.383 12.4964 18.113 11.9234 17.594 11.7364C17.075 11.5494 16.501 11.8194 16.315 12.3384C16.128 12.8584 16.398 13.4314 16.917 13.6184C17.436 13.8054 18.01 13.5354 18.196 13.0164Z"
                :class="fillColor"
            />
            <path
                d="M5.75 20.4784C6.30228 20.4784 6.75 20.0306 6.75 19.4784C6.75 18.9261 6.30228 18.4784 5.75 18.4784C5.19772 18.4784 4.75 18.9261 4.75 19.4784C4.75 20.0306 5.19772 20.4784 5.75 20.4784Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M6.75 14.4784C6.75 13.9264 6.302 13.4784 5.75 13.4784C5.198 13.4784 4.75 13.9264 4.75 14.4784C4.75 15.0304 5.198 15.4784 5.75 15.4784C6.302 15.4784 6.75 15.0304 6.75 14.4784Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M15.185 6.61836C15.372 6.09936 15.102 5.52536 14.583 5.33836C14.063 5.15236 13.49 5.42136 13.303 5.94136C13.116 6.46036 13.386 7.03336 13.905 7.22036C14.425 7.40736 14.998 7.13736 15.185 6.61836Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
