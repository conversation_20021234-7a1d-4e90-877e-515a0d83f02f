<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M16.8509 7.37525C16.7491 7.28934 16.6288 7.21804 16.5502 7.11495C15.9325 6.31902 15.2178 5.63391 14.3433 5.1335C12.0134 3.8028 9.66131 3.86851 7.30788 5.06563C6.70919 5.37636 6.16198 5.77754 5.68553 6.25502C5.62582 6.31386 5.57084 6.37786 5.47548 6.48052L6.0923 6.56643C6.06524 6.65234 6.04934 6.71891 6.024 6.7812C5.73922 7.57412 5.8393 8.28457 6.56307 8.85156C7.50805 9.59423 8.57115 10.1058 9.68923 10.5426C9.68708 10.2793 9.67076 10.2665 9.44397 10.2798C9.38555 10.2828 9.30866 10.2768 9.26915 10.242C8.39633 9.4688 7.59181 8.64066 7.0914 7.56467C6.57209 6.44788 6.9823 5.64078 7.91525 5.12705C8.79064 4.64512 9.75882 4.5257 10.736 4.47717C11.1548 4.45655 11.5809 4.39813 11.9903 4.55663C12.0225 4.56909 12.0538 4.58283 12.1466 4.62106C12.0263 4.65671 11.9589 4.68077 11.8889 4.69623C11.2291 4.84485 10.5904 5.04373 10.014 5.41313C9.16348 5.95735 8.80525 6.79194 9.02903 7.77557C9.0931 8.03221 9.17727 8.2834 9.28074 8.52683C9.3877 8.79271 9.41562 8.79615 9.74163 8.71926C9.7077 8.6703 9.67849 8.62605 9.64628 8.58353C9.61406 8.54101 9.57927 8.49762 9.52171 8.42847C9.89799 8.43835 10.2575 8.32495 10.5337 8.64624C10.5689 8.6892 10.6514 8.6849 10.7094 8.70938C10.7867 8.74117 10.9057 8.76136 10.9293 8.81849C11.0079 9.00877 11.1643 9.01264 11.3249 9.04485C11.6896 9.11787 11.9563 9.29312 12.0122 9.70419C12.0289 9.8236 12.1208 9.93399 12.184 10.0478C12.2471 10.1616 12.3193 10.2793 12.3987 10.4116C12.1436 10.5169 11.9159 10.5431 11.6788 10.4327C11.3197 10.2656 10.9581 10.1045 10.5706 9.92884C10.6016 10.2725 10.7854 10.4894 11.0298 10.659C11.1671 10.7493 11.313 10.8257 11.4654 10.8871C11.7983 11.0302 12.1118 11.2033 12.3347 11.4962C12.489 11.7027 12.5612 11.9591 12.5374 12.2158C12.5136 12.4724 12.3955 12.7111 12.2059 12.8858C11.9563 13.1194 11.6419 13.2934 11.3365 13.4566C10.8584 13.7118 10.334 13.8896 9.88166 14.1817C9.04364 14.7225 8.88986 15.7924 9.49895 16.4917C9.86792 16.9148 10.6681 17.0931 11.1595 16.8383C11.2848 16.7695 11.3859 16.6638 11.449 16.5355C11.5663 16.2855 11.7029 16.0201 11.7214 15.7542C11.7467 15.3925 12.0371 15.3912 12.2286 15.2138C12.4357 15.6468 12.324 16.027 12.1595 16.4045L12.2076 16.4303C12.3158 16.365 12.4224 16.2993 12.5766 16.2082C12.5139 16.8925 12.2802 17.4504 11.8313 17.9126C11.4056 18.3603 10.8664 18.684 10.2713 18.8494C13.0632 18.9981 15.3024 17.971 16.9364 15.664C16.6812 16.4217 15.2835 18.0097 13.7857 18.8426C12.4271 19.5994 10.9796 19.9735 9.42464 19.9194C5.17137 19.7725 1.75141 16.3646 1.51344 12.1208C1.27763 7.91388 4.16841 4.20656 8.27176 3.47635C12.0783 2.79682 15.698 4.96082 16.9016 7.31769C16.8848 7.33573 16.8676 7.35549 16.8509 7.37525ZM7.97195 18.2855C7.57291 17.8701 7.31948 17.3894 7.23357 16.828C7.07035 15.7645 7.60512 14.7023 8.58661 14.1709C9.04493 13.9227 9.53503 13.7324 9.99162 13.4811C10.2385 13.3404 10.4634 13.1643 10.6591 12.9584C10.8275 12.7865 10.7914 12.5786 10.6291 12.4C10.4383 12.1882 10.3224 12.1912 10.0329 12.2513C9.19355 12.4232 8.35166 12.595 7.50418 12.7178C6.71899 12.8316 5.92478 12.9077 5.13142 12.7664C4.50473 12.6547 3.92658 12.4476 3.52926 11.9051C3.52024 11.8927 3.49489 11.8922 3.45924 11.8802C3.47857 12.0997 3.49661 12.3059 3.51551 12.5121C3.53054 12.6753 3.61216 12.7225 3.76292 12.6551C3.92486 12.5821 4.05501 12.5963 4.19675 12.7346C4.33976 12.8634 4.50246 12.9685 4.67869 13.046C5.39688 13.3926 6.16446 13.4102 6.9286 13.2998C8.03251 13.14 9.12955 12.9347 10.2296 12.7492C10.2932 12.7384 10.3584 12.7294 10.4207 12.7195L10.4585 12.7848C10.4155 12.8081 10.3737 12.8338 10.3335 12.8617C8.9238 13.9248 7.3517 14.6361 5.63398 15.0309C5.24889 15.1237 4.84581 15.1102 4.46779 14.9918C4.41607 14.9807 4.36371 14.973 4.31101 14.9686C5.16321 16.5226 6.3745 17.6167 7.97195 18.2837V18.2855ZM3.70322 9.57833L4.33507 9.54311C4.4665 9.76948 4.56143 9.97909 4.69716 10.1582C4.98839 10.5448 5.38958 10.7961 5.81954 11.0014C7.00764 11.5684 8.28637 11.7673 9.57712 11.915C9.59774 11.915 9.62008 11.9038 9.69954 11.8811C9.49594 11.8059 9.35247 11.7432 9.20343 11.6998C8.11026 11.3815 7.02009 11.0508 6.01971 10.492C5.20659 10.0384 4.37759 9.58993 4.07219 8.538C3.94247 8.90568 3.83165 9.21753 3.70322 9.57833ZM11.9735 16.7563C11.3846 17.4264 10.6823 17.8301 9.74937 17.7198C10.5814 18.072 11.5942 17.8658 11.9735 16.7563Z"
                :class="fillColor"
            />
            <path
                d="M17.6537 11.8694C17.8607 11.5344 18.0355 11.2509 18.2121 10.9674C18.2911 10.8386 18.3813 10.7179 18.447 10.5834C18.5518 10.3686 18.7206 10.2913 18.9491 10.2948C19.3001 10.2999 19.651 10.2913 20.0015 10.2948C20.1866 10.2973 20.3129 10.2342 20.3305 10.037C20.3486 9.8343 20.1965 9.76815 20.0427 9.763C19.5569 9.7471 19.0698 9.74539 18.5823 9.75441C18.5205 9.75441 18.4427 9.84031 18.4019 9.90689C17.5191 11.3126 16.6395 12.7206 15.7633 14.1309C15.5794 14.4278 15.3462 14.5669 14.988 14.5549C14.3587 14.5343 13.7286 14.5489 13.098 14.5485H12.8064L14.893 11.2947C14.8343 11.2833 14.7749 11.2751 14.7152 11.2702C14.479 11.2702 14.2406 11.2488 14.0069 11.2741C13.714 11.3059 13.5671 11.1929 13.4395 10.927C12.9966 10.0048 12.5229 9.09808 12.0615 8.18531C12.0212 8.10499 11.9881 8.02123 11.9327 7.89967H12.6547C13.1912 7.89967 13.7286 7.90912 14.2651 7.89967C14.4575 7.89666 14.5601 7.97355 14.6435 8.14279C14.9343 8.7334 15.2392 9.31714 15.5399 9.90388C15.5648 9.95242 15.5988 9.99581 15.6469 10.0701C15.8616 9.71875 16.0601 9.39016 16.259 9.06114C16.4621 8.72438 16.6576 8.38247 16.8732 8.05344C16.9024 8.01303 16.9397 7.97919 16.9827 7.95411C17.0258 7.92902 17.0737 7.91326 17.1232 7.90783C18.2971 7.89881 19.4719 7.87304 20.6454 7.91342C21.4349 7.94048 22.0667 8.31546 22.3833 9.07316C22.7196 9.87768 22.3017 10.7621 21.4813 11.0585C21.4546 11.0679 21.4301 11.0817 21.373 11.1074C21.4349 11.1646 21.4821 11.211 21.5315 11.2543C22.0899 11.744 22.2746 12.4837 21.9812 13.1662C21.6174 14.0137 20.9551 14.5064 20.0415 14.5356C18.697 14.5785 17.3504 14.5467 16.0038 14.5455C15.953 14.54 15.9028 14.5305 15.8535 14.5171L16.5768 13.3836C16.7143 13.1658 16.8573 12.9514 16.99 12.7307C17.0443 12.6298 17.1258 12.5463 17.2253 12.4897C17.3249 12.4331 17.4383 12.4057 17.5527 12.4107C18.2112 12.4128 18.8697 12.4107 19.5286 12.4107C19.6527 12.4107 19.7863 12.4184 19.8026 12.2461C19.8181 12.0872 19.7597 11.9592 19.6059 11.9025C19.5239 11.8779 19.4384 11.8675 19.3529 11.8716C18.8074 11.8686 18.2645 11.8694 17.6537 11.8694Z"
                :class="fillColor"
            />
            <path
                d="M9.23648 15.5278C9.33312 15.3435 9.39068 15.1172 9.53458 14.9836C9.82309 14.7105 10.1993 14.5488 10.596 14.5274C10.934 14.5102 11.1887 14.6808 11.3262 14.993C11.4636 15.3053 11.4409 15.6395 11.1806 15.8633C10.9359 16.0804 10.6515 16.2478 10.343 16.3564C9.80218 16.5316 9.28502 16.1107 9.28931 15.5553L9.23648 15.5278Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
