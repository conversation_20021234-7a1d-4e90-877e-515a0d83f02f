<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M20.2244 3.50523L13.2919 2.28284C12.1253 2.07718 11.0088 2.85888 10.8031 4.02553L10.6382 4.96055C10.5561 4.9727 10.4742 4.9891 10.3928 5.0109L3.59324 6.83284C3.03896 6.98136 2.57565 7.33686 2.28875 7.8338C2.00184 8.33078 1.92559 8.90975 2.07414 9.46403L5.07321 20.6567C5.22172 21.211 5.57722 21.6743 6.0742 21.9612C6.40524 22.1523 6.77258 22.2499 7.14487 22.2499C7.33147 22.2499 7.51936 22.2254 7.70443 22.1758L14.504 20.3538C15.0583 20.2053 15.5216 19.8498 15.8085 19.3529C15.8937 19.2052 15.9599 19.0503 16.0076 18.8909L17.4662 19.1481C17.5918 19.1703 17.7168 19.181 17.8403 19.181C18.8627 19.181 19.7714 18.4464 19.955 17.4054L21.9671 5.99404C22.1728 4.82743 21.391 3.71097 20.2244 3.50523ZM14.8912 18.026C14.9587 18.2779 14.9241 18.5411 14.7936 18.767C14.6632 18.9929 14.4526 19.1544 14.2007 19.222L7.40108 21.0439C7.14913 21.1115 6.88597 21.0768 6.66008 20.9464C6.43419 20.8159 6.27259 20.6053 6.20509 20.3534L3.20603 9.16075C3.13853 8.90881 3.17318 8.64561 3.3036 8.41972C3.43403 8.19383 3.64461 8.03227 3.89656 7.96473L10.6961 6.14278C10.7803 6.12025 10.8657 6.10907 10.9505 6.10907C11.1197 6.10907 11.2867 6.15345 11.4371 6.24032C11.663 6.37075 11.8246 6.58133 11.8921 6.83327L14.8912 18.026ZM13.7176 9.11845C13.7219 9.08498 13.7264 9.05154 13.7322 9.01834C13.7955 8.65975 14.1084 8.40675 14.4607 8.40675C14.5032 8.40675 14.5463 8.41042 14.5895 8.41808C14.8621 8.46616 15.0835 8.66049 15.167 8.92525C15.2327 9.13357 15.4089 9.28802 15.624 9.32595C15.8392 9.36391 16.0575 9.27899 16.1905 9.10576C16.3596 8.88553 16.6339 8.77862 16.9066 8.82666C17.1013 8.861 17.2709 8.96908 17.3843 9.13099C17.4977 9.2929 17.5412 9.48926 17.5069 9.68394C17.2739 11.0056 15.7843 12.1302 15.044 12.6155C14.8583 12.3668 14.6131 12.0128 14.3828 11.601L13.7176 9.11845ZM20.813 5.79057L18.8009 17.202C18.7074 17.7323 18.1999 18.0875 17.6696 17.9941L16.0178 17.7029L15.0153 13.9618C15.0747 13.9507 15.1333 13.9303 15.1886 13.9001C15.3135 13.832 18.2521 12.2059 18.6609 9.88741C18.844 8.84924 18.1483 7.85567 17.1101 7.67263C16.6813 7.59704 16.2509 7.66958 15.8814 7.8661C15.6014 7.55505 15.2219 7.33967 14.793 7.26404C14.2902 7.17534 13.7827 7.28783 13.3644 7.58076C13.3477 7.59247 13.3315 7.60482 13.3153 7.61697L13.024 6.53C12.8755 5.97572 12.52 5.51241 12.023 5.2255C11.9507 5.18375 11.8765 5.14699 11.801 5.11422L11.9571 4.229C12.0506 3.69874 12.5581 3.3434 13.0884 3.43688L20.0209 4.65927C20.5512 4.75278 20.9066 5.26027 20.813 5.79057Z"
                :class="fillColor"
            />
            <path
                d="M9.56217 17.3944C9.67041 17.4896 9.80842 17.5404 9.94923 17.5404C9.99982 17.5404 10.0508 17.5339 10.1008 17.5205C10.2902 17.4697 10.4416 17.3277 10.5043 17.142L11.842 13.1811C11.9176 12.9572 11.8513 12.7098 11.6739 12.5537L8.53497 9.79234C8.38779 9.66289 8.18561 9.61554 7.99631 9.66628C7.80698 9.71703 7.65554 9.85905 7.59285 10.0448L6.25515 14.0056C6.17952 14.2295 6.24581 14.4769 6.42327 14.633L9.56217 17.3944ZM8.42158 11.2533L10.6069 13.1758L9.67561 15.9334L7.49027 14.0109L8.42158 11.2533Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
