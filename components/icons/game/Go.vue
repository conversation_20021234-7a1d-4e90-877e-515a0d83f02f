<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <g clip-path="url(#clip0_9935_10839)">
                <path
                    d="M4.98288 9.91416C3.9424 9.77712 2.87151 10.4715 2.45262 11.8458C2.03372 13.2201 2.02021 14.4967 3.06238 14.601C3.58389 14.6529 3.63752 14.601 3.71648 14.4448C3.79545 14.2885 4.09906 13.6295 4.14594 13.5365C4.19281 13.4436 4.25573 13.4493 5.11717 13.4493C5.97861 13.4493 6.20241 13.4012 6.10571 13.7281C6.00901 14.055 5.46048 15.2229 5.33633 15.4429C5.21218 15.6628 3.96943 16.0273 2.63842 15.9007C1.30741 15.7741 -0.667989 14.9798 0.223431 12.1421C1.11485 9.30446 3.67383 8.50187 5.18262 8.56122C6.6914 8.62058 7.59803 8.99242 7.59803 9.81683C7.59803 10.6413 7.03007 11.2134 6.44057 11.0275C6.15301 10.8939 6.22521 10.7975 6.03097 10.478C5.83672 10.1586 5.3207 9.9578 4.98288 9.91416Z"
                    :class="fillColor"
                />
                <path
                    d="M4.99597 12.3425C4.99597 12.3425 5.26201 11.9497 5.39502 11.7629C5.52804 11.5761 5.71848 11.5141 5.88739 11.7629C6.0563 12.0116 6.58246 12.6881 6.71421 12.8775C6.84596 13.0669 6.8544 13.2673 6.36921 13.2673H3.96943C3.71057 13.2673 3.51464 13.2193 3.65483 12.9923C3.79503 12.7654 3.89553 12.6117 3.97829 12.4839C4.06106 12.356 4.07879 12.3499 4.30556 12.3499C4.53232 12.3499 4.99597 12.3425 4.99597 12.3425Z"
                    :class="fillColor"
                />
                <path
                    d="M12.6729 12.1657C12.77 12.1142 12.6797 11.8746 12.4588 11.4622C12.238 11.0498 12.0057 10.8651 11.9251 10.9105C11.5691 10.3035 10.5827 10.0835 10.5016 10.0639C10.4991 10.0202 10.51 10.0023 10.3534 9.94122C9.96825 9.77974 9.28628 9.91634 9.16213 9.95518C9.03798 9.99403 9.05783 10.1402 9.05783 10.1402C8.45187 10.3061 7.99497 10.7381 7.82817 10.9022C7.71458 10.8241 7.57987 10.8717 7.28512 11.412C6.99038 11.9523 6.99418 12.0898 7.07863 12.1421C6.85229 13.1896 7.00135 13.8748 7.04654 13.9974C6.96546 14.0585 6.97095 14.1475 7.25767 14.6935C7.5444 15.2395 7.72682 15.3455 7.81845 15.2897C8.16345 15.7357 8.85345 15.964 9.05783 16.0753C9.05783 16.2258 9.30106 16.2258 9.76472 16.2315C10.2284 16.2372 10.5138 16.1756 10.5248 16.0643C11.4466 15.8247 11.8149 15.3844 11.912 15.2731C12.0159 15.329 12.2076 15.1618 12.469 14.742C12.7303 14.3221 12.735 14.0624 12.67 14.0026C12.9314 13.0778 12.7105 12.2491 12.6729 12.1657ZM9.89478 15.3665C8.69172 15.3665 7.71289 14.3191 7.71289 13.0303C7.71289 11.7415 8.69172 10.6936 9.89478 10.6936C11.0978 10.6936 12.0771 11.7419 12.0771 13.0303C12.0771 14.3186 11.0978 15.3665 9.89478 15.3665ZM9.89478 10.7931C8.74281 10.7931 7.80621 11.7969 7.80621 13.0303C7.80621 14.2636 8.74281 15.267 9.89478 15.267C11.0467 15.267 11.9833 14.2632 11.9833 13.0303C11.9833 11.7974 11.0459 10.7931 9.89478 10.7931ZM10.9927 13.7233C10.9032 13.8215 10.3863 14.2252 9.93067 13.5553C9.90682 13.7151 9.92276 13.8785 9.97699 14.0302C10.0312 14.1818 10.122 14.3167 10.2406 14.4221H9.51389C9.63266 14.3168 9.72349 14.1819 9.7778 14.0303C9.83211 13.8786 9.8481 13.7151 9.82426 13.5553C9.36862 14.2252 8.85303 13.8215 8.76182 13.7233C8.424 13.5169 8.47256 12.8108 9.00885 12.414C9.33547 12.171 9.62756 11.8819 9.87662 11.5551C10.1253 11.8819 10.4171 12.1708 10.7435 12.4136C11.2803 12.8108 11.3276 13.5169 10.991 13.7233H10.9927Z"
                    :class="fillColor"
                />
                <path
                    d="M18.825 9.32629L18.2642 8.51758H15.1272L14.153 9.38608L13.7729 11.5848L14.1817 12.0963L13.4845 12.6842L13.1433 14.8498L13.7577 15.8099H16.8826L17.8538 14.8847L18.2634 12.7025L17.747 12.1168L18.472 11.529L18.825 9.32629ZM16.5025 12.6327L16.1812 14.4094C16.1772 14.4309 16.1662 14.4502 16.1499 14.4642C16.1337 14.4781 16.1132 14.4858 16.0921 14.4858H15.101C15.0877 14.4857 15.0746 14.4827 15.0625 14.4768C15.0505 14.471 15.0398 14.4625 15.0313 14.4519C15.0228 14.4413 15.0167 14.429 15.0133 14.4156C15.01 14.4023 15.0095 14.3884 15.0119 14.3749L15.3341 12.6008C15.3379 12.5794 15.3488 12.56 15.365 12.546C15.3812 12.532 15.4017 12.5244 15.4228 12.5245H16.4143C16.4273 12.5245 16.4402 12.5275 16.452 12.5332C16.4638 12.5389 16.4743 12.5472 16.4827 12.5575C16.4911 12.5678 16.4973 12.5798 16.5008 12.5928C16.5043 12.6058 16.505 12.6194 16.5029 12.6327H16.5025ZM17.0202 9.81771L16.6985 11.5922C16.6945 11.6136 16.6835 11.6328 16.6673 11.6467C16.6512 11.6606 16.6308 11.6682 16.6098 11.6682H15.6187C15.6054 11.6682 15.5923 11.6652 15.5803 11.6594C15.5683 11.6536 15.5576 11.6451 15.5492 11.6346C15.5407 11.624 15.5346 11.6117 15.5313 11.5984C15.528 11.5851 15.5275 11.5712 15.53 11.5578L15.8518 9.78323C15.8557 9.76185 15.8666 9.74254 15.8828 9.72867C15.899 9.71479 15.9194 9.70723 15.9405 9.70729H16.9332C16.9464 9.70751 16.9593 9.71068 16.9711 9.7166C16.983 9.72251 16.9934 9.73102 17.0017 9.74153C17.0101 9.75204 17.0161 9.76431 17.0193 9.77746C17.0226 9.79062 17.0231 9.80435 17.0207 9.81771H17.0202Z"
                    :class="fillColor"
                />
                <path
                    d="M24 9.32629L23.4392 8.51758H20.3021L19.328 9.38608L18.9479 11.5848L19.3567 12.0963L18.6595 12.6842L18.3183 14.8498L18.9327 15.8099H22.0575L23.0288 14.8847L23.4384 12.7025L22.9219 12.1168L23.6474 11.529L24 9.32629ZM21.6775 12.6327L21.3561 14.4094C21.3522 14.4309 21.3412 14.4502 21.3249 14.4642C21.3086 14.4781 21.2882 14.4858 21.267 14.4858H20.276C20.2627 14.4857 20.2495 14.4827 20.2375 14.4768C20.2255 14.471 20.2148 14.4625 20.2063 14.4519C20.1978 14.4413 20.1917 14.429 20.1883 14.4156C20.1849 14.4023 20.1845 14.3884 20.1869 14.3749L20.5091 12.6008C20.5128 12.5794 20.5238 12.56 20.54 12.546C20.5562 12.532 20.5766 12.5244 20.5977 12.5245H21.5892C21.6023 12.5245 21.6151 12.5275 21.627 12.5332C21.6388 12.5389 21.6492 12.5472 21.6577 12.5575C21.6661 12.5678 21.6723 12.5798 21.6758 12.5928C21.6792 12.6058 21.68 12.6194 21.6779 12.6327H21.6775ZM22.1952 9.81771L21.8739 11.5922C21.8699 11.6136 21.8589 11.6328 21.8427 11.6467C21.8265 11.6606 21.8062 11.6682 21.7852 11.6682H20.7937C20.7804 11.6682 20.7673 11.6652 20.7553 11.6594C20.7432 11.6536 20.7326 11.6451 20.7241 11.6346C20.7157 11.624 20.7095 11.6117 20.7062 11.5984C20.7029 11.5851 20.7025 11.5712 20.705 11.5578L21.0268 9.78323C21.0306 9.76185 21.0416 9.74254 21.0578 9.72867C21.074 9.71479 21.0944 9.70723 21.1154 9.70729H22.1069C22.1202 9.70732 22.1333 9.71035 22.1453 9.71618C22.1572 9.72202 22.1678 9.7305 22.1763 9.74104C22.1848 9.75157 22.1909 9.7639 22.1942 9.77715C22.1976 9.7904 22.198 9.80424 22.1956 9.81771H22.1952Z"
                    :class="fillColor"
                />
            </g>
            <defs>
                <clipPath id="clip0_9935_10839">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
