<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M2.60714 13.9643C2.60714 8.63868 6.9244 4.32143 12.25 4.32143C17.5756 4.32143 21.8929 8.63868 21.8929 13.9643V19.5357C21.8929 19.8908 21.605 20.1786 21.25 20.1786C20.895 20.1786 20.6071 19.8908 20.6071 19.5357V13.9643C20.6071 9.34876 16.8655 5.60714 12.25 5.60714C7.63448 5.60714 3.89286 9.34876 3.89286 13.9643V19.5357C3.89286 19.8908 3.60504 20.1786 3.25 20.1786C2.89496 20.1786 2.60714 19.8908 2.60714 19.5357V13.9643Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M1.75 22.1071C1.75 21.7521 2.03782 21.4643 2.39286 21.4643H22.1071C22.4622 21.4643 22.75 21.7521 22.75 22.1071C22.75 22.4622 22.4622 22.75 22.1071 22.75H2.39286C2.03782 22.75 1.75 22.4622 1.75 22.1071Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.60714 2.39286C8.60714 2.03782 8.89496 1.75 9.25 1.75H15.25C15.605 1.75 15.8929 2.03782 15.8929 2.39286C15.8929 2.7479 15.605 3.03571 15.25 3.03571H9.25C8.89496 3.03571 8.60714 2.7479 8.60714 2.39286Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M6.17859 14.3928C6.17859 13.6433 6.7862 13.0357 7.53573 13.0357H11.8214C12.571 13.0357 13.1786 13.6433 13.1786 14.3928V18.6786C13.1786 19.4281 12.571 20.0357 11.8214 20.0357H7.53573C6.7862 20.0357 6.17859 19.4281 6.17859 18.6786V14.3928ZM7.53573 14.0357C7.33849 14.0357 7.17859 14.1956 7.17859 14.3928V18.6786C7.17859 18.8758 7.33849 19.0357 7.53573 19.0357H11.8214C12.0187 19.0357 12.1786 18.8758 12.1786 18.6786V14.3928C12.1786 14.1956 12.0187 14.0357 11.8214 14.0357H7.53573Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.1053 9.52988C12.2811 8.80127 13.0143 8.35316 13.7429 8.529L17.909 9.53441C18.6376 9.71024 19.0857 10.4434 18.9099 11.1721L17.9045 15.3382C17.7287 16.0668 16.9955 16.5149 16.2668 16.3391L14.1838 15.8364C13.9154 15.7716 13.7503 15.5014 13.815 15.233C13.8798 14.9646 14.15 14.7995 14.4184 14.8643L16.5014 15.367C16.6932 15.4132 16.8861 15.2953 16.9324 15.1036L17.9378 10.9375C17.9841 10.7457 17.8662 10.5528 17.6744 10.5065L13.5083 9.50109C13.3166 9.45481 13.1236 9.57274 13.0773 9.76448L12.5746 11.8475C12.5099 12.116 12.2397 12.2811 11.9713 12.2163C11.7029 12.1515 11.5378 11.8814 11.6026 11.6129L12.1053 9.52988Z"
                :class="fillColor"
            />
            <path
                d="M11.5295 15.2929C11.5295 15.6006 11.28 15.85 10.9723 15.85C10.6646 15.85 10.4152 15.6006 10.4152 15.2929C10.4152 14.9852 10.6646 14.7357 10.9723 14.7357C11.28 14.7357 11.5295 14.9852 11.5295 15.2929Z"
                :class="fillColor"
            />
            <path
                d="M11.5295 17.9168C11.5295 18.2245 11.28 18.4739 10.9723 18.4739C10.6646 18.4739 10.4152 18.2245 10.4152 17.9168C10.4152 17.6091 10.6646 17.3596 10.9723 17.3596C11.28 17.3596 11.5295 17.6091 11.5295 17.9168Z"
                :class="fillColor"
            />
            <path
                d="M8.90614 15.2929C8.90614 15.6006 8.6567 15.85 8.349 15.85C8.04129 15.85 7.79185 15.6006 7.79185 15.2929C7.79185 14.9852 8.04129 14.7357 8.349 14.7357C8.6567 14.7357 8.90614 14.9852 8.90614 15.2929Z"
                :class="fillColor"
            />
            <path
                d="M8.90614 17.9168C8.90614 18.2245 8.6567 18.4739 8.349 18.4739C8.04129 18.4739 7.79185 18.2245 7.79185 17.9168C7.79185 17.6091 8.04129 17.3596 8.349 17.3596C8.6567 17.3596 8.90614 17.6091 8.90614 17.9168Z"
                :class="fillColor"
            />
            <path
                d="M15.5071 12.3786C15.5071 12.6863 15.2577 12.9357 14.95 12.9357C14.6423 12.9357 14.3929 12.6863 14.3929 12.3786C14.3929 12.0709 14.6423 11.8214 14.95 11.8214C15.2577 11.8214 15.5071 12.0709 15.5071 12.3786Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
