<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M2.58279 11.4825L4.53825 11.4825C4.85448 9.75998 6.21538 8.39957 7.93733 8.08337V6.12802C5.15545 6.4776 2.93239 8.70065 2.58279 11.4825ZM9.47221 6.08011V8.01377H14.5278V6.08011H9.47221ZM16.0627 6.12802V8.08337C17.7846 8.39957 19.1455 9.75998 19.4618 11.4825L21.4172 11.4825C21.0676 8.70065 18.8446 6.4776 16.0627 6.12802ZM21.4172 13.0174L19.4617 13.0173C19.1455 14.7398 17.7846 16.1002 16.0627 16.4164V18.372C18.8446 18.0224 21.0676 15.7993 21.4172 13.0174ZM14.5278 18.4199V16.486H9.47221V18.4199H14.5278ZM7.93733 18.372V16.4164C6.2154 16.1002 4.8545 14.7398 4.53826 13.0173L2.58279 13.0174C2.93236 15.7993 5.15543 18.0224 7.93733 18.372ZM1 12.25C1 8.01062 4.46539 4.54523 8.70477 4.54523H15.2952C19.5346 4.54523 23 8.01062 23 12.25C23 16.4894 19.5346 19.9548 15.2952 19.9548H8.70477C4.46539 19.9548 1 16.4894 1 12.25ZM8.70477 9.54865C7.21631 9.54865 6.00355 10.7608 6.00355 12.2499C6.00355 13.7389 7.21631 14.9511 8.70477 14.9511H15.2952C16.7837 14.9511 17.9965 13.7389 17.9965 12.2499C17.9965 10.7608 16.7837 9.54865 15.2952 9.54865H8.70477Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
