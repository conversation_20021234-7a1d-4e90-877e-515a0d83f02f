<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M12.5 1.75C6.7011 1.75 2 6.4511 2 12.25C2 18.0489 6.7011 22.75 12.5 22.75C18.2989 22.75 23 18.0489 23 12.25C22.9933 6.45382 18.2962 1.75673 12.5 1.75ZM20.925 16.5078L19.1032 15.4564C19.5129 14.617 19.7574 13.7067 19.8234 12.775H21.9233C21.8535 14.075 21.5135 15.3464 20.925 16.5078ZM11.975 19.5734V21.6733C10.6746 21.6035 9.40316 21.2633 8.24174 20.6747L9.29341 18.8534C10.1329 19.2632 11.0433 19.5075 11.975 19.5734ZM13.025 19.5734C13.9567 19.5075 14.867 19.2632 15.7066 18.8534L16.7583 20.6747C15.5968 21.2633 14.3254 21.6035 13.025 21.6733V19.5734ZM12.5 18.5501C9.02056 18.5501 6.19994 15.7294 6.19994 12.25C6.19994 8.77056 9.02056 5.94994 12.5 5.94994C15.9794 5.94994 18.8001 8.77056 18.8001 12.25C18.7962 15.7278 15.9778 18.5462 12.5 18.5501ZM3.07666 12.775H5.17663C5.24248 13.7067 5.48681 14.617 5.89648 15.4564L4.07497 16.5078C3.4865 15.3464 3.14651 14.075 3.07666 12.775ZM4.07497 7.99223L5.8968 9.04357C5.48697 9.88295 5.24248 10.7933 5.17663 11.725H3.07666C3.14651 10.425 3.4865 9.15364 4.07497 7.99223ZM13.025 4.92663V2.82666C14.3254 2.89651 15.5968 3.2365 16.7583 3.82529L15.7066 5.64664C14.867 5.23681 13.9567 4.99248 13.025 4.92663ZM11.975 4.92663C11.0433 4.99248 10.1329 5.23681 9.29341 5.64664L8.24174 3.82529C9.40316 3.23666 10.6746 2.89651 11.975 2.82666V4.92663ZM19.8234 11.725C19.7575 10.7933 19.5132 9.88295 19.1035 9.04357L20.9252 7.99223C21.5137 9.15364 21.8536 10.425 21.9233 11.725H19.8234ZM20.4018 7.08171L18.5838 8.13113C18.0584 7.35792 17.3916 6.6911 16.6184 6.16575L17.6684 4.34792C18.757 5.06265 19.6872 5.99303 20.4018 7.08171ZM7.33187 4.34792L8.38161 6.16591C7.60841 6.69126 6.94158 7.35792 6.41639 8.13113L4.59824 7.08171C5.31281 5.99303 6.24319 5.06265 7.33187 4.34792ZM4.59824 17.4183L6.41623 16.3689C6.94158 17.1421 7.60841 17.8089 8.38161 18.3341L7.33187 20.1521C6.24319 19.4373 5.31281 18.507 4.59824 17.4183ZM17.6681 20.1521L16.6181 18.3341C17.3913 17.8089 18.0581 17.1421 18.5835 16.3689L20.4014 17.4183C19.687 18.507 18.7566 19.4373 17.6681 20.1521Z"
                :class="fillColor"
            />
            <path
                d="M15.65 11.1999C15.2814 11.2044 14.943 11.4044 14.7615 11.725H13.9783C13.8199 11.2802 13.4698 10.9303 13.025 10.7717V9.98869C13.3456 9.80685 13.5456 9.46847 13.5501 9.09997C13.5501 8.52014 13.08 8.05006 12.5 8.05006C11.9202 8.05006 11.4501 8.52014 11.4501 9.09997C11.4546 9.46847 11.6545 9.80701 11.9751 9.98869V10.7717C11.5304 10.9303 11.1803 11.2802 11.0218 11.725H10.2387C10.057 11.4044 9.71863 11.2044 9.35013 11.1999C8.7703 11.1999 8.30006 11.6702 8.30006 12.25C8.30006 12.8298 8.7703 13.3001 9.35013 13.3001C9.71863 13.2956 10.057 13.0956 10.2387 12.775H11.0218C11.1803 13.2198 11.5304 13.5697 11.9751 13.7283V14.5113C11.6545 14.6932 11.4546 15.0315 11.4501 15.4C11.4501 15.9799 11.9202 16.4499 12.5 16.4499C13.08 16.4499 13.5501 15.9799 13.5501 15.4C13.5456 15.0315 13.3456 14.693 13.025 14.5113V13.7283C13.4698 13.5697 13.8199 13.2198 13.9783 12.775H14.7615C14.9432 13.0956 15.2815 13.2956 15.65 13.3001C16.2299 13.3001 16.6999 12.8298 16.6999 12.25C16.6999 11.6702 16.2299 11.1999 15.65 11.1999ZM12.5 12.775C12.21 12.775 11.975 12.54 11.975 12.25C11.975 11.96 12.21 11.725 12.5 11.725C12.79 11.725 13.025 11.96 13.025 12.25C13.0247 12.5398 12.7898 12.7747 12.5 12.775Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
