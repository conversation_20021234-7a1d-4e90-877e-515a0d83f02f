<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M9.50936 1.37759C9.29131 1.37759 9.08915 1.49171 8.97648 1.67839L8.93163 1.76842C8.86433 1.93627 8.87285 2.12711 8.95867 2.29005L9.52341 3.36228C8.90285 3.54939 8.31214 3.80485 7.76001 4.11991C7.09279 4.50064 6.48191 4.96839 5.94278 5.50769C4.39263 7.05747 3.43359 9.19981 3.43359 11.5656C3.43359 13.9309 4.39265 16.0737 5.94286 17.6235C6.31302 17.9937 6.71699 18.3302 7.14974 18.6279L5.73444 21.3776H4.24148C3.89773 21.3776 3.61907 21.6563 3.61907 22C3.61907 22.3437 3.89773 22.6224 4.24148 22.6224H6.10082C6.10934 22.6226 6.11785 22.6226 6.12634 22.6224H17.8753C17.8838 22.6226 17.8923 22.6226 17.9008 22.6224H19.7602C20.1039 22.6224 20.3826 22.3437 20.3826 22C20.3826 21.6563 20.1039 21.3776 19.7602 21.3776H18.2672L16.8519 18.6279C17.2846 18.3302 17.6886 17.9937 18.0588 17.6235C19.609 16.0737 20.568 13.9309 20.568 11.5656C20.568 9.19981 19.609 7.05747 18.0588 5.50769C17.0736 4.52203 15.8488 3.77552 14.4782 3.36228L15.043 2.29005C15.1446 2.09712 15.1378 1.86508 15.0251 1.67839C14.9125 1.49171 14.7103 1.37759 14.4923 1.37759H9.50936ZM6.40152 6.84647C5.45106 7.9726 4.83513 9.38834 4.70445 10.9432L9.7936 10.9431C9.84302 10.7677 9.91279 10.6007 10.0001 10.4451L6.40152 6.84647ZM10.8804 9.56484C11.036 9.4775 11.203 9.40773 11.3784 9.3583V6.88418L10.1163 4.48792C9.05853 4.76872 8.09469 5.28017 7.28173 5.96626L10.8804 9.56484ZM10.9856 3.46721C10.9915 3.47768 10.9971 3.48835 11.0025 3.49922L12.0008 5.39469L12.9992 3.49917C13.0045 3.48834 13.0101 3.47769 13.016 3.46726L13.461 2.62241H10.5406L10.9856 3.46721ZM12.6232 6.88418V9.3583C12.7987 9.40773 12.9656 9.4775 13.1213 9.56484L16.7199 5.96626C15.9069 5.28017 14.9431 4.76872 13.8853 4.48792L12.6232 6.88418ZM17.6001 6.84647L14.0015 10.4451C14.0888 10.6007 14.1586 10.7677 14.208 10.9432H19.2972C19.1665 9.38834 18.5506 7.9726 17.6001 6.84647ZM19.2972 12.188H14.208C14.1586 12.3634 14.0888 12.5304 14.0015 12.686L17.6001 16.2846C18.5505 15.1585 19.1665 13.7426 19.2972 12.188ZM16.7198 17.1648L13.1213 13.5662C12.9656 13.6536 12.7987 13.7233 12.6232 13.7728L12.6232 18.8619C14.1779 18.7312 15.5937 18.1151 16.7198 17.1648ZM11.3784 18.8619L11.3784 13.7728C11.2029 13.7234 11.036 13.6536 10.8803 13.5662L7.28179 17.1648C8.40795 18.1151 9.82368 18.7312 11.3784 18.8619ZM6.40152 16.2846L10.0001 12.686C9.9128 12.5303 9.84303 12.3634 9.7936 12.1879L4.70445 12.188C4.83516 13.7426 5.45109 15.1585 6.40152 16.2846ZM8.22543 19.2581L7.13447 21.3776H16.8672L15.7762 19.2581C14.6373 19.8181 13.3557 20.1328 12.0008 20.1328C10.6459 20.1328 9.36436 19.8181 8.22543 19.2581ZM12.0008 10.5175C11.7114 10.5175 11.4502 10.6341 11.2598 10.8245C11.0694 11.0149 10.9528 11.2761 10.9528 11.5655C10.9528 11.8549 11.0694 12.1162 11.2598 12.3066C11.4502 12.497 11.7113 12.6136 12.0008 12.6136C12.2902 12.6136 12.5514 12.497 12.7418 12.3066C12.9322 12.1162 13.0489 11.855 13.0489 11.5656C13.0489 11.2761 12.9322 11.0149 12.7418 10.8245C12.5515 10.6341 12.2903 10.5175 12.0008 10.5175Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
