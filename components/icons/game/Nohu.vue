<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0.998291 12C0.998291 18.0751 5.92314 23 11.9983 23C18.0734 23 22.9983 18.0751 22.9983 12C22.9983 5.92485 18.0734 1 11.9983 1C5.92314 1 0.998291 5.92485 0.998291 12ZM11.9983 21.7109C6.63507 21.7109 2.28735 17.3632 2.28735 12C2.28735 6.63678 6.63507 2.28906 11.9983 2.28906C17.3615 2.28906 21.7092 6.63678 21.7092 12C21.7092 17.3632 17.3615 21.7109 11.9983 21.7109Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.72541 17.6862H14.5944V17.0417C14.5944 14.8339 15.4983 13.0338 16.5656 10.9081C16.7701 10.5008 16.9806 10.0815 17.1919 9.64517L17.2563 9.51211V6.31379H6.74335V11.0015H10.6105V10.18H12.1101C11.729 10.5941 11.2879 11.1077 10.855 11.6909C9.80937 13.0999 8.72541 15.0266 8.72541 17.0417V17.6862ZM9.32147 9.71244H8.03241V7.60285H15.9673V9.21583C15.7955 9.56738 15.6193 9.91665 15.4432 10.2656C14.4631 12.2077 13.4883 14.1393 13.3282 16.3971H10.0603C10.2527 15.0144 11.0248 13.6252 11.8902 12.4592C12.7664 11.2785 13.6717 10.4122 13.9945 10.1033C14.0352 10.0644 14.0666 10.0343 14.0875 10.0136L14.2787 9.82442V8.89092H9.32147V9.71244Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
