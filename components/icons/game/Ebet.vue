<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M8.98574 10.496C9.44952 8.68935 10.8718 7.40323 12.6806 7.40323C14.443 7.40323 15.4788 8.56686 15.4788 10.0214C15.4788 10.1898 15.4633 10.4042 15.4324 10.496H8.98574ZM11.2731 21.0913C15.5708 21.0913 18.7871 18.7487 19.6528 15.3343H14.6903C14.2884 16.498 13.1444 17.2788 11.5984 17.2788C9.63504 17.2788 8.44465 15.9161 8.44465 14.0329C8.44465 13.8645 8.46011 13.696 8.49103 13.5276H20.1012C20.2867 12.7161 20.364 11.6597 20.364 10.8023C20.364 6.45394 17.3494 3.63672 12.897 3.63672C7.36248 3.63672 3.63672 7.87787 3.63672 13.5276C3.63672 18.2128 6.51153 21.0913 11.2731 21.0913Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
