<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M11.5585 16.3927C11.303 16.1107 11.0824 15.74 10.9106 15.3051C10.7833 14.9828 10.6828 14.6252 10.6148 14.2423C10.5469 14.6252 10.4463 14.9829 10.319 15.3053C10.1472 15.74 9.92659 16.1107 9.67111 16.3927C9.57542 16.4984 9.64981 16.6671 9.79237 16.6671H11.4372C11.5798 16.6671 11.6542 16.4984 11.5585 16.3927Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M6.34046 3.46951C6.34046 2.4508 7.16625 1.625 8.18497 1.625H18.5854C19.6041 1.625 20.4299 2.4508 20.4299 3.46951V18.4289C20.4299 19.4476 19.6041 20.2734 18.5854 20.2734H17.2033C16.8581 20.2734 16.5783 19.9936 16.5783 19.6484C16.5783 19.3032 16.8581 19.0234 17.2033 19.0234H18.5854C18.9137 19.0234 19.1799 18.7572 19.1799 18.4289V3.46951C19.1799 3.14115 18.9137 2.875 18.5854 2.875H8.18497C7.85661 2.875 7.59046 3.14115 7.59046 3.46951V4.79984C7.59046 5.14502 7.31063 5.42484 6.96546 5.42484C6.62028 5.42484 6.34046 5.14502 6.34046 4.79984V3.46951Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M3.57013 6.07114C3.57013 5.05242 4.39593 4.22663 5.41464 4.22663H15.815C16.8338 4.22663 17.6596 5.05242 17.6596 6.07114V21.0305C17.6596 22.0492 16.8338 22.875 15.815 22.875H5.41464C4.39593 22.875 3.57013 22.0492 3.57013 21.0305V6.07114ZM5.41464 5.47663C5.08628 5.47663 4.82013 5.74278 4.82013 6.07114V21.0305C4.82013 21.3588 5.08628 21.625 5.41464 21.625H15.815C16.1434 21.625 16.4096 21.3588 16.4096 21.0305V6.07114C16.4096 5.74278 16.1434 5.47663 15.815 5.47663H5.41464Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M7.16042 7.84381L6.38237 9.88668C6.28877 10.1325 6.01365 10.2558 5.76788 10.1622C5.52211 10.0686 5.39875 9.79348 5.49236 9.54771L6.65646 6.49121C6.84137 6.03893 7.48157 6.03853 7.66732 6.49023L7.67263 6.50314L8.82191 9.54906C8.91477 9.79511 8.79058 10.0699 8.54452 10.1627C8.29847 10.2556 8.02373 10.1314 7.93087 9.88533L7.16042 7.84381Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M5.82295 9.21235C5.82295 8.94935 6.03615 8.73616 6.29914 8.73616H8.02044C8.28343 8.73616 8.49663 8.94935 8.49663 9.21235C8.49663 9.47534 8.28343 9.68854 8.02044 9.68854H6.29914C6.03615 9.68854 5.82295 9.47534 5.82295 9.21235Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.6852 16.9389C12.9312 16.8461 13.206 16.9702 13.2988 17.2163L14.0693 19.2578L14.8473 17.2149C14.9409 16.9692 15.216 16.8458 15.4618 16.9394C15.7076 17.033 15.8309 17.3081 15.7373 17.5539L14.5732 20.6104C14.3883 21.0627 13.7481 21.0631 13.5624 20.6114L13.5571 20.5985L12.4078 17.5526C12.3149 17.3065 12.4391 17.0318 12.6852 16.9389Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.7331 17.8893C12.7331 17.6263 12.9463 17.4131 13.2092 17.4131H14.9305C15.1935 17.4131 15.4067 17.6263 15.4067 17.8893C15.4067 18.1523 15.1935 18.3655 14.9305 18.3655H13.2092C12.9463 18.3655 12.7331 18.1523 12.7331 17.8893Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.91418 12.2841C8.22427 12.494 7.72218 13.135 7.72218 13.8935C7.72218 14.8226 8.47535 15.5758 9.40441 15.5758C9.74175 15.5758 10.0557 15.4763 10.319 15.3053C10.4463 14.9829 10.5469 14.6252 10.6148 14.2423C10.6828 14.6252 10.7833 14.9828 10.9106 15.3051C11.1739 15.4763 11.4879 15.5758 11.8254 15.5758C12.7545 15.5758 13.5076 14.8226 13.5076 13.8935C13.5076 13.135 13.0055 12.494 12.3156 12.2841C12.2892 12.276 12.2626 12.2686 12.2357 12.2619C12.2432 12.2348 12.2501 12.2074 12.2563 12.1797C12.2829 12.0612 12.2971 11.9381 12.2971 11.8116C12.2971 10.8825 11.544 10.1293 10.6149 10.1293C9.68583 10.1293 8.93266 10.8825 8.93266 11.8116C8.93266 11.9381 8.94693 12.0612 8.97352 12.1797C8.97973 12.2074 8.9866 12.2348 8.99413 12.2619C8.96722 12.2686 8.94056 12.276 8.91418 12.2841ZM10.9478 13.0119L11.2329 11.9838C11.2483 11.928 11.2565 11.8706 11.2565 11.8116C11.2565 11.4572 10.9692 11.17 10.6149 11.17C10.2606 11.17 9.97331 11.4572 9.97331 11.8116C9.97331 11.8706 9.98146 11.928 9.99694 11.9838L10.282 13.0119L9.24721 13.2713C8.96808 13.3413 8.76283 13.5948 8.76283 13.8935C8.76283 14.2479 9.05008 14.5351 9.40441 14.5351C9.5852 14.5351 9.74752 14.4613 9.86601 14.3385L10.6149 13.5623L11.3638 14.3386C11.4822 14.4613 11.6445 14.5351 11.8254 14.5351C12.1797 14.5351 12.4669 14.2479 12.467 13.8936C12.4669 13.5948 12.2617 13.3413 11.9826 13.2713L10.9478 13.0119Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
