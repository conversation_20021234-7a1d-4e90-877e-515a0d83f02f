<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M17.6243 9.78907C17.3007 9.78907 17.0384 10.0514 17.0384 10.375C17.0384 10.6986 17.3007 10.9609 17.6243 10.9609H17.6303C17.9539 10.9609 18.2162 10.6986 18.2162 10.375C18.2162 10.0514 17.9539 9.78907 17.6303 9.78907H17.6243Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M17.6243 9.78907C17.3007 9.78907 17.0384 10.0514 17.0384 10.375C17.0384 10.6986 17.3007 10.9609 17.6243 10.9609H17.6303C17.9539 10.9609 18.2162 10.6986 18.2162 10.375C18.2162 10.0514 17.9539 9.78907 17.6303 9.78907H17.6243Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M14.5262 5.33398C14.2595 4.81146 13.7001 3.89586 12.7213 3.19487C11.4885 2.31198 9.66667 1.82429 7.12907 2.71223C6.88205 2.79866 6.7223 3.03843 6.73766 3.29968C6.75302 3.56093 6.93976 3.78032 7.19521 3.83721C8.21303 4.06391 8.77883 4.48888 9.12978 4.89803C9.28957 5.0843 9.40678 5.26578 9.50437 5.44327C8.99477 5.55751 8.52887 5.69824 8.12827 5.85671C7.62143 6.05728 7.18492 6.30914 6.77724 6.57858C2.24077 9.57329 1.55355 13.4575 2.21155 16.1397C2.67319 18.0207 3.89513 19.5303 4.95846 20.5497C5.49486 21.0639 6.00302 21.465 6.37794 21.7381C6.56568 21.8749 6.72079 21.9802 6.83033 22.0521C7.06803 22.2082 7.33237 22.2605 7.46319 22.2445C7.55404 22.2379 7.67826 22.2235 7.82306 22.1926C8.10986 22.1314 8.49673 22.0012 8.86237 21.7202C9.64554 21.1182 10.1309 19.9924 9.76222 18.0768C9.72845 17.9013 9.61645 17.7508 9.45805 17.6681C9.39142 17.6349 9.16687 17.5966 8.80174 17.7093C8.69661 17.7473 8.54051 17.7991 8.34532 17.8501C7.95265 17.9528 7.41443 18.049 6.82126 18.0315C5.72348 17.9992 4.41553 17.5837 3.4494 15.9899C3.50182 15.8986 3.569 15.7874 3.65087 15.663C3.88585 15.306 4.23427 14.8518 4.69222 14.4467C5.50243 13.7299 6.63569 13.1733 8.1482 13.5676C8.34384 13.6126 8.58701 13.7029 8.68414 13.7423C8.70047 13.7487 8.72417 13.7579 8.7549 13.7696C8.81636 13.793 8.90597 13.8266 9.02103 13.8683C9.25111 13.9518 9.58326 14.0678 9.99587 14.1997C10.1374 14.245 10.2886 14.2922 10.4485 14.3406C11.2196 14.5741 12.1936 14.8365 13.2716 15.0512C14.2717 15.2505 15.3823 15.4133 16.5148 15.4682C16.5638 15.4706 16.6127 15.4728 16.6618 15.4747C18.3854 15.544 20.1584 15.3573 21.6612 14.656C21.898 14.5455 22.0329 14.2917 21.9922 14.0336C21.9338 13.6637 21.8657 13.3078 21.7884 12.9654C21.4326 11.3894 20.8829 10.101 20.1934 9.05697C19.1595 7.49086 17.8195 6.48674 16.3772 5.88675L16.3648 5.88176C15.758 5.63043 15.1391 5.45294 14.5262 5.33398ZM9.23229 3.43243C9.54872 3.64783 9.80657 3.8871 10.0193 4.13506C10.3473 4.51743 10.5659 4.9273 10.7142 5.23481L10.7732 5.22765C11.4699 5.1426 12.2394 5.10489 13.041 5.15061C12.7951 4.81559 12.4661 4.45353 12.0389 4.14761C11.3951 3.68654 10.4912 3.32577 9.23229 3.43243ZM8.55939 6.94639C9.0662 6.74593 9.7085 6.57488 10.421 6.46C10.4352 6.45877 10.4495 6.45702 10.4637 6.45472C10.6092 6.43132 10.7588 6.41002 10.9121 6.39127C11.9033 6.2732 12.994 6.26649 14.0276 6.43522L14.0362 6.41635L14.0329 6.43609C14.295 6.47904 14.5574 6.53327 14.8184 6.60013C14.1775 7.1061 13.7105 7.67436 13.3884 8.28134C12.9136 9.17619 12.7741 10.1164 12.8139 10.9944C12.8639 12.0969 13.1978 13.1272 13.5365 13.9091L13.5005 13.9019C12.5346 13.7095 11.6535 13.4766 10.9362 13.2634C10.7272 13.2013 10.532 13.1408 10.3528 13.0835C9.95605 12.9566 9.63817 12.8456 9.42055 12.7667C9.31176 12.7272 9.2281 12.6958 9.17226 12.6745C8.89069 12.579 8.24277 12.3745 7.90364 12.3209C6.18734 12.0568 4.84389 12.7479 3.91576 13.569C3.62914 13.8225 3.38066 14.0889 3.16978 14.3442C3.17242 12.3626 4.07961 9.92486 7.02285 7.83066C7.15221 7.73862 7.28551 7.64724 7.42283 7.55659C7.78927 7.31439 8.15232 7.10749 8.55939 6.94639ZM4.93917 18.8195C5.20965 19.1417 5.49163 19.4374 5.76944 19.7037C6.25993 20.174 6.72572 20.5416 7.06803 20.791C7.23356 20.9116 7.36949 21.004 7.46441 21.0665C7.49817 21.0619 7.53661 21.0555 7.57853 21.0465C7.74994 21.0099 7.9598 20.9359 8.14822 20.7911C8.4243 20.5789 8.79216 20.1049 8.70414 18.9673C8.68368 18.9728 8.66284 18.9784 8.64165 18.9839C8.17495 19.1059 7.52176 19.2245 6.78681 19.2029C6.20059 19.1857 5.56645 19.0782 4.93917 18.8195ZM18.1919 8.45754C17.5784 7.85835 16.8981 7.41004 16.1856 7.08206C15.2971 7.60912 14.7503 8.21496 14.4236 8.83065C14.0672 9.50218 13.9521 10.2247 13.9846 10.9413C14.0429 12.2281 14.575 13.4415 14.951 14.1457C16.9453 14.4155 19.0586 14.4171 20.7573 13.7669C20.2881 11.2577 19.3368 9.57583 18.1919 8.45754Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M17.0384 10.375C17.0384 10.0514 17.3007 9.78907 17.6243 9.78907H17.6303C17.9539 9.78907 18.2162 10.0514 18.2162 10.375C18.2162 10.6986 17.9539 10.9609 17.6303 10.9609H17.6243C17.3007 10.9609 17.0384 10.6986 17.0384 10.375Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
