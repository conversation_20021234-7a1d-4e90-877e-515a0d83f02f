<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M13.1018 9.95972C10.7045 10.1035 11.7815 13.1818 14.4343 13.0201C17.0263 12.8584 15.6573 9.80401 13.1018 9.95972Z"
                :class="fillColor"
            />
            <path
                d="M12.0905 8.06108C7.81925 8.06108 4.36328 11.4688 4.36328 15.6672C4.36328 19.8715 7.82533 23.2733 12.0905 23.2733C16.3618 23.2733 19.8178 19.8655 19.8178 15.6672C19.8178 11.4629 16.3557 8.06108 12.0905 8.06108ZM12.0905 22.4767C8.2695 22.4767 5.17251 19.4283 5.17251 15.6672C5.17251 11.906 8.2695 8.85762 12.0905 8.85762C15.9116 8.85762 19.0086 11.906 19.0086 15.6672C19.0025 19.4283 15.9055 22.4767 12.0905 22.4767Z"
                :class="fillColor"
            />
            <path
                d="M11.8542 2.74871C10.0167 4.11421 10.3392 6.20439 10.3392 6.20439C10.3392 6.20439 9.18312 3.25778 7.68635 1.45508C7.48556 3.77284 8.22178 6.84522 8.22178 6.84522C8.22178 6.84522 7.28477 4.9407 4.98485 4.8329C6.88928 5.91093 7.63159 7.85138 7.79587 8.37243C8.89715 7.70165 10.1871 7.30039 11.5621 7.25247C11.4222 6.77335 10.8868 4.73109 11.8542 2.74871Z"
                :class="fillColor"
            />
            <path
                d="M6.8468 16.4094V18.0025H6.17142V13.9H7.5891C8.06977 13.9 8.42875 14.0138 8.67213 14.2354C8.91551 14.4629 9.0372 14.7684 9.0372 15.1577C9.0372 15.547 8.91551 15.8524 8.66605 16.074C8.41658 16.2956 8.0576 16.4094 7.5891 16.4094H6.8468ZM8.35574 15.1637C8.35574 14.9301 8.28881 14.7564 8.16104 14.6306C8.02718 14.5108 7.82031 14.445 7.52826 14.445H6.85288V15.8704H7.52826C8.07586 15.8704 8.35574 15.6368 8.35574 15.1637Z"
                :class="fillColor"
            />
            <path
                d="M10.1932 17.4874H11.6352V18.0025H9.52393V13.9H10.1993V17.4874H10.1932Z"
                :class="fillColor"
            />
            <path
                d="M14.7136 17.1167H12.9552L12.6388 18.0031H11.9208L13.4541 13.9665H14.2268L15.754 18.0031H15.0361L14.7136 17.1167ZM14.525 16.6016L13.8374 14.6851L13.1499 16.6016H14.525Z"
                :class="fillColor"
            />
            <path
                d="M18.4552 13.9L17.068 16.5112V18.0025H16.3865V16.5112L14.9932 13.9H15.7781L16.7333 15.7985L17.6886 13.9H18.4552Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
