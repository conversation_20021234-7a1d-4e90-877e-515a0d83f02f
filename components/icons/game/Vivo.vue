<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M2.90918 11.9057C2.90918 10.2269 2.90918 8.54914 2.91145 6.8703C2.91145 6.82385 2.92504 6.74682 2.94996 6.73889C2.99074 6.72643 3.05192 6.74795 3.09496 6.77174C4.1145 7.35968 5.13178 7.94988 6.15019 8.54008C7.05192 9.06231 7.95364 9.58455 8.85537 10.1068C9.84319 10.6789 10.831 11.2521 11.8222 11.8196C11.9344 11.8842 11.9752 11.9612 11.974 12.0881C11.9706 15.4333 11.9695 18.7785 11.9695 22.1238C11.9695 22.143 11.9695 22.1623 11.9695 22.1815C11.9661 22.313 11.9287 22.3367 11.8166 22.2722C11.4099 22.0377 11.0043 21.802 10.5988 21.5676C9.81714 21.1144 9.03549 20.6624 8.25271 20.2104C7.41329 19.7256 6.57386 19.2419 5.73558 18.757C4.84064 18.2393 3.94571 17.7194 3.04852 17.2051C2.94656 17.1473 2.90918 17.0759 2.90918 16.9638C2.91031 15.2781 2.91031 13.5913 2.91031 11.9057H2.90918ZM6.1887 13.7692C6.21703 14.1408 6.36203 14.6177 6.56254 14.9349C6.76871 15.2611 7.01114 15.5523 7.35551 15.7392C7.76673 15.9623 8.14962 15.7675 8.21306 15.3053C8.22779 15.2 8.23119 15.0889 8.21986 14.9836C8.15642 14.3424 7.87888 13.8043 7.40083 13.3772C7.26942 13.2594 7.10742 13.1643 6.9443 13.0952C6.67469 12.983 6.4198 13.0872 6.29972 13.3523C6.24081 13.4815 6.22382 13.6299 6.18757 13.7703L6.1887 13.7692ZM6.03464 16.4755C5.99046 15.8581 5.80241 15.3506 5.43085 14.9179C5.24959 14.7072 5.05022 14.5157 4.784 14.4092C4.4906 14.2914 4.18587 14.3979 4.08845 14.6834C4.03521 14.8397 4.00689 15.0153 4.01821 15.1796C4.04767 15.618 4.1972 16.0212 4.45209 16.3804C4.64693 16.6556 4.87576 16.9003 5.18162 17.0555C5.49768 17.2152 5.82733 17.119 5.94175 16.8267C5.99159 16.6998 6.01198 16.5616 6.03464 16.4744V16.4755ZM10.4436 13.8485C10.3994 13.2345 10.2114 12.727 9.8398 12.2943C9.65288 12.0768 9.4501 11.8796 9.1703 11.7811C8.85877 11.6712 8.56084 11.8037 8.47701 12.1209C8.43283 12.2875 8.4181 12.471 8.43396 12.6432C8.46681 12.9955 8.58576 13.3263 8.77494 13.6276C8.97772 13.9516 9.22241 14.2348 9.56452 14.4194C9.88737 14.5939 10.2352 14.4965 10.3541 14.194C10.4028 14.0705 10.4221 13.9357 10.4436 13.8496V13.8485Z"
                :class="fillColor"
            />
            <path
                d="M12.3649 11.1671C12.3263 11.149 12.2901 11.1365 12.2572 11.1173C11.0666 10.4274 9.87605 9.73634 8.68545 9.04645C7.57755 8.40414 6.46965 7.7641 5.36174 7.12179C4.74322 6.76381 4.12357 6.40584 3.50618 6.0456C3.47219 6.02635 3.4484 5.98896 3.42008 5.96064C3.45294 5.93119 3.48012 5.89154 3.51864 5.87568C4.15529 5.60607 4.7942 5.33872 5.43198 5.07024C6.06296 4.80516 6.69394 4.54121 7.32493 4.27613C7.96271 4.00765 8.60049 3.74031 9.23827 3.47183C9.87945 3.20222 10.5206 2.93147 11.1618 2.66186C11.5334 2.50553 11.9049 2.3458 12.2788 2.19514C12.332 2.17361 12.4113 2.17928 12.4657 2.20193C13.5657 2.65959 14.6645 3.12179 15.7622 3.58398C16.7647 4.00652 17.7662 4.4302 18.7687 4.85161C19.6047 5.20278 20.4419 5.55169 21.2779 5.904C21.3096 5.91759 21.3606 5.95951 21.3561 5.97197C21.3425 6.01049 21.3142 6.05467 21.2791 6.07506C20.8305 6.33561 20.3785 6.59162 19.9276 6.84878C19.0802 7.33362 18.2318 7.81847 17.3844 8.30332C16.4804 8.82102 15.5764 9.33872 14.6724 9.85642C13.9418 10.2744 13.2111 10.6924 12.4804 11.1105C12.4442 11.1308 12.4056 11.1478 12.366 11.1671H12.3649ZM12.409 6.87256C12.6322 6.88049 12.9551 6.83518 13.2711 6.74569C13.5657 6.66186 13.8466 6.54858 14.063 6.31975C14.2986 6.07053 14.3054 5.7692 14.0584 5.53357C13.9406 5.42142 13.8024 5.31833 13.654 5.25376C12.8406 4.90032 12.0137 4.89465 11.1822 5.18806C10.9624 5.26509 10.7642 5.3829 10.609 5.56189C10.413 5.78845 10.4039 6.06373 10.609 6.28123C10.7155 6.39451 10.8412 6.49873 10.9783 6.56897C11.3918 6.78307 11.8404 6.8635 12.4102 6.87143L12.409 6.87256Z"
                :class="fillColor"
            />
            <path
                d="M12.7931 17.0793C12.7931 15.4004 12.7931 13.7227 12.7919 12.0439C12.7919 11.9374 12.8225 11.866 12.9211 11.8094C13.8998 11.2521 14.8775 10.6913 15.8551 10.1317C16.8123 9.5834 17.7696 9.03285 18.7268 8.48456C19.7078 7.92268 20.6888 7.35967 21.6699 6.79892C21.7764 6.73775 21.8171 6.7672 21.8171 6.90087C21.8171 9.01472 21.816 11.1274 21.816 13.2413C21.816 14.4851 21.816 15.7278 21.8183 16.9717C21.8183 17.0748 21.7843 17.1439 21.6914 17.1971C20.2187 18.0456 18.7472 18.8963 17.2756 19.7471C15.8641 20.5627 14.4526 21.3784 13.04 22.1929C12.9766 22.2291 12.9029 22.2484 12.835 22.2744C12.8214 22.1985 12.7965 22.1215 12.7965 22.0456C12.7942 20.3905 12.7942 18.7344 12.7942 17.0793H12.7931ZM16.3818 14.9722C16.3966 15.0493 16.4068 15.1694 16.4408 15.2815C16.579 15.7471 17.0253 15.9148 17.424 15.6406C18.0471 15.2124 18.3654 14.6109 18.3734 13.853C18.3745 13.7284 18.3541 13.5959 18.3099 13.4803C18.1423 13.0385 17.7208 12.9128 17.3255 13.1892C16.724 13.6106 16.426 14.2008 16.3818 14.9711V14.9722ZM20.4793 15.5885C20.4589 15.4911 20.443 15.3438 20.3977 15.2067C20.289 14.8726 19.9967 14.7242 19.6568 14.8159C19.4065 14.8828 19.2241 15.0493 19.061 15.2339C18.6169 15.7346 18.3949 16.3124 18.5263 16.9864C18.6135 17.4339 19.0587 17.6423 19.4507 17.4089C20.1156 17.0136 20.4215 16.3951 20.4781 15.5896L20.4793 15.5885ZM14.1728 18.9269C14.1842 19.0074 14.1921 19.1274 14.2216 19.2419C14.2646 19.4152 14.3575 19.5613 14.5172 19.6508C14.763 19.7879 14.9975 19.7256 15.2128 19.584C15.6331 19.3076 15.9174 18.9201 16.0658 18.441C16.1575 18.1419 16.2153 17.8349 16.1236 17.52C15.9808 17.0351 15.5492 16.872 15.1312 17.1563C14.5229 17.5698 14.2148 18.1555 14.1728 18.9258V18.9269ZM18.5172 11.0255C18.5252 11.1104 18.5274 11.2113 18.5455 11.3087C18.6011 11.6134 18.7936 11.8287 19.0519 11.8694C19.2694 11.9046 19.4609 11.8287 19.6319 11.6973C19.9933 11.4197 20.2493 11.0629 20.3785 10.6279C20.4759 10.2993 20.5438 9.96403 20.4283 9.61965C20.2992 9.23789 19.9876 9.07929 19.6115 9.21637C19.5402 9.24242 19.4711 9.28094 19.4099 9.32625C18.8503 9.74766 18.5591 10.3118 18.5184 11.0244L18.5172 11.0255ZM14.2034 13.2639C14.2102 13.3398 14.2057 13.4305 14.2272 13.5154C14.2555 13.6253 14.2895 13.7409 14.3496 13.836C14.5444 14.1441 14.933 14.1929 15.2592 13.9538C15.8687 13.5086 16.1553 12.8924 16.1734 12.1504C16.1802 11.8706 16.0805 11.6123 15.8143 11.4707C15.5481 11.3291 15.2943 11.4072 15.0712 11.5783C14.5206 11.9974 14.2385 12.5605 14.2034 13.2639Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
