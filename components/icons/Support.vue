<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M22 10.3333V13.6667C22 14.5859 21.2526 15.3333 20.3333 15.3333H19.8796C19.2422 19.1107 15.9563 22 12 22C11.6316 22 11.3333 21.7018 11.3333 21.3333C11.3333 20.9649 11.6316 20.6667 12 20.6667C15.6758 20.6667 18.6667 17.6758 18.6667 14V10C18.6667 6.32422 15.6758 3.33333 12 3.33333C8.32422 3.33333 5.33333 6.32422 5.33333 10V14.6667C5.33333 15.0351 5.03516 15.3333 4.66667 15.3333H3.66667C2.7474 15.3333 2 14.5859 2 13.6667V10.3333C2 9.41407 2.7474 8.66667 3.66667 8.66667H4.12044C4.75781 4.88932 8.04362 2 12 2C15.9563 2 19.2422 4.88932 19.8796 8.66667H20.3333C21.2526 8.66667 22 9.41407 22 10.3333Z"
                :class="fillColor"
            />
            <path
                d="M12.0003 4.66699C9.05957 4.66699 6.66699 7.05957 6.66699 10.0003V13.3337C6.66699 16.2744 9.05957 18.667 12.0003 18.667C14.9411 18.667 17.3337 16.2744 17.3337 13.3337V10.0003C17.3337 7.05957 14.9411 4.66699 12.0003 4.66699Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
