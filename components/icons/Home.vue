<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12 13C10.3477 13 9 14.2978 9 15.8889V21H15V15.8889C15 14.2978 13.6523 13 12 13Z"
                :class="fillColor"
            />
            <path
                d="M20.5116 7.08399L14 2.61555C12.8 1.79482 11.1907 1.79482 10 2.61555L3.48837 7.08399C2.55814 7.72234 2 8.77106 2 9.88361V17.5803C2 19.468 3.56279 21 5.48837 21H7.5814V15.7564C7.5814 13.3672 9.56279 11.4248 12 11.4248C14.4372 11.4248 16.4186 13.3672 16.4186 15.7564V21H18.5116C20.4372 21 22 19.468 22 17.5803V9.88361C22 8.77106 21.4419 7.72234 20.5116 7.08399Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
