<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M15.9102 8.82031C12.0008 8.82031 8.82031 12.0008 8.82031 15.9102C8.82031 19.8195 12.0008 23 15.9102 23C19.8195 23 23 19.8195 23 15.9102C23 12.0008 19.8195 8.82031 15.9102 8.82031ZM15.9102 15.2656C16.9764 15.2656 17.8438 16.133 17.8438 17.1992C17.8438 18.0384 17.3033 18.7471 16.5547 19.0141V20.4219H15.2656V19.0141C14.517 18.7471 13.9766 18.0384 13.9766 17.1992H15.2656C15.2656 17.5548 15.5545 17.8438 15.9102 17.8438C16.2658 17.8438 16.5547 17.5548 16.5547 17.1992C16.5547 16.8436 16.2658 16.5547 15.9102 16.5547C14.8439 16.5547 13.9766 15.6873 13.9766 14.6211C13.9766 13.7819 14.517 13.0732 15.2656 12.8062V11.3984H16.5547V12.8062C17.3033 13.0732 17.8438 13.7819 17.8438 14.6211H16.5547C16.5547 14.2655 16.2658 13.9766 15.9102 13.9766C15.5545 13.9766 15.2656 14.2655 15.2656 14.6211C15.2656 14.9767 15.5545 15.2656 15.9102 15.2656Z"
                :class="fillColor"
            />
            <path
                d="M8.17578 8.82031C12.1512 8.82031 15.2656 7.1215 15.2656 4.95312C15.2656 2.78475 12.1512 1 8.17578 1C4.20031 1 1 2.78475 1 4.95312C1 7.1215 4.20031 8.82031 8.17578 8.82031Z"
                :class="fillColor"
            />
            <path
                d="M1 15.5045V16.5547C1 18.7231 4.20031 20.4219 8.17578 20.4219C8.40128 20.4219 8.61978 20.4011 8.8418 20.3902C8.3474 19.6129 7.97868 18.7503 7.76156 17.8282C4.88674 17.7489 2.39055 16.8655 1 15.5045Z"
                :class="fillColor"
            />
            <path
                d="M7.56223 16.5219C7.54745 16.3191 7.53125 16.1166 7.53125 15.9102C7.53125 15.2383 7.61929 14.588 7.76943 13.9614C4.89103 13.8834 2.3918 12.9995 1 11.6373V12.6875C1 14.7408 3.89016 16.3545 7.56223 16.5219Z"
                :class="fillColor"
            />
            <path
                d="M8.17578 12.6875L8.17776 12.6874C8.60285 11.6715 9.22078 10.7556 9.98816 9.98829C9.40606 10.0619 8.8045 10.1094 8.17578 10.1094C5.11568 10.1094 2.45776 9.19685 1 7.77011V8.82031C1 10.9887 4.20031 12.6875 8.17578 12.6875Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
