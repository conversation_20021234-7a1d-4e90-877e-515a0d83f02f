<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M1.05883 5.99996C1.05883 5.44768 1.53288 4.99997 2.11766 4.99997H15.8824C16.4672 4.99997 16.9413 5.44768 16.9413 5.99996V16.9999C16.9413 17.5522 16.4672 17.9999 15.8824 17.9999H2.11766C1.53288 17.9999 1.05883 17.5522 1.05883 16.9999V5.99996Z"
                :class="fillColorSecondary"
            />
            <path
                d="M7.9512 9.99505C6.40808 9.99505 1.86646 9.99505 1.86646 9.99505C1.38434 9.99505 0.989838 10.374 0.989838 10.8371V17.1579C0.989838 17.6211 1.38434 18 1.86646 18C1.86646 18 6.50311 18 8.05351 18C8.25973 18 8.25973 17.7872 8.25973 17.7872V10.2812C8.25973 10.2812 8.25967 9.99505 7.9512 9.99505Z"
                :class="fillColorPrimary"
            />
            <path
                d="M16.1336 9.99505C16.1336 9.99505 11.5746 9.99505 10.055 9.99505C9.6848 9.99505 9.74024 10.3654 9.74024 10.3654V17.794C9.74024 17.794 9.73724 17.9998 9.95994 17.9998C11.5033 17.9998 16.1334 17.9998 16.1334 17.9998C16.6156 17.9998 17.0101 17.6209 17.0101 17.1577V10.8371C17.0102 10.374 16.6157 9.99505 16.1336 9.99505Z"
                :class="fillColorPrimary"
            />
            <path
                d="M8.25975 5.02164C8.25975 5.02164 8.25975 4.73627 7.96622 4.73627C6.20857 4.73627 0.87662 4.73627 0.87662 4.73627C0.394498 4.73627 0 5.11526 0 5.57837V8.20711C0 8.67028 0.394498 9.0492 0.87662 9.0492C0.87662 9.0492 6.23246 9.0492 7.99024 9.0492C8.25975 9.0492 8.25975 8.82465 8.25975 8.82465V5.02164Z"
                :class="fillColorPrimary"
            />
            <path
                d="M17.1234 4.73627C17.1234 4.73627 11.7893 4.73627 10.0112 4.73627C9.74031 4.73627 9.74031 4.98421 9.74031 4.98421V8.82986C9.74031 8.82986 9.74031 9.0492 10.0696 9.0492C11.833 9.0492 17.1234 9.0492 17.1234 9.0492C17.6055 9.0492 18 8.67028 18 8.20711V5.57837C18 5.11526 17.6055 4.73627 17.1234 4.73627Z"
                :class="fillColorPrimary"
            />
            <path
                d="M5.27816 4.11322C4.87708 4.11322 4.51075 4.08267 4.18951 4.02235C3.37356 3.86921 2.81275 3.57115 2.47503 3.11124C2.17244 2.69906 2.07983 2.19092 2.19971 1.60087C2.4097 0.568513 3.13138 0 4.23166 0C4.46451 0 4.72023 0.0258303 4.99179 0.0768161C5.68256 0.206459 6.56608 0.586981 7.35526 1.09469C8.69421 1.95617 8.76044 2.49174 8.69376 2.81968C8.59573 3.30156 8.12689 3.64588 7.26042 3.8724C6.67503 4.02542 5.95252 4.11322 5.27816 4.11322ZM4.23172 1.34974C3.80561 1.34974 3.65916 1.46411 3.57876 1.85966C3.51298 2.18306 3.59594 2.29602 3.62314 2.33314C3.73708 2.48836 4.03386 2.61782 4.45889 2.69752C4.68823 2.74059 4.9716 2.76336 5.2781 2.76336C5.95207 2.76336 6.54583 2.66525 6.93982 2.55794C6.96849 2.55015 7.01301 2.51757 6.9653 2.49027C6.45022 2.08716 5.50788 1.5489 4.7224 1.40146C4.53943 1.36723 4.37427 1.34974 4.23172 1.34974Z"
                :class="fillColorSecondary"
            />
            <path
                d="M12.7421 4.11316C12.0678 4.11316 11.3452 4.02536 10.7598 3.87234C9.89329 3.64588 9.42451 3.3015 9.32648 2.81968C9.25987 2.49174 9.32597 1.95617 10.665 1.09469C11.4541 0.586981 12.3376 0.206459 13.0285 0.0768161C13.3001 0.0258303 13.5558 0 13.7885 0C14.889 0 15.6106 0.568574 15.8204 1.60093C15.9404 2.19092 15.8479 2.69906 15.5452 3.11124C15.2075 3.57121 14.6467 3.86921 13.8306 4.02235C13.5095 4.08254 13.1432 4.11316 12.7421 4.11316ZM11.0662 2.48186C11.0205 2.50732 11.0426 2.54751 11.0663 2.55407C11.4601 2.66286 12.0601 2.76336 12.742 2.76336C13.0486 2.76336 13.3319 2.74059 13.5613 2.69752C13.9862 2.61776 14.2832 2.48836 14.397 2.33314C14.4244 2.29602 14.5074 2.18306 14.4414 1.85966C14.3611 1.46411 14.2146 1.34974 13.7884 1.34974C13.6459 1.34974 13.4809 1.36717 13.2978 1.40153C12.5122 1.5489 11.5813 2.0787 11.0662 2.48186Z"
                :class="fillColorSecondary"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
