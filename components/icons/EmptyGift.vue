<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M71.165 21.9775H66.1407C66.8034 20.9287 67.2645 20.0466 67.3892 18.8015C67.7875 14.8223 65.6919 11.2551 62.1241 9.67366C58.8447 8.21999 55.1593 8.81424 52.5055 11.2212L46.1613 16.9709C44.9257 15.6195 43.1496 14.7702 41.1784 14.7702C39.2036 14.7702 37.4247 15.6222 36.1888 16.9778L29.8365 11.2205C27.1784 8.81356 23.4945 8.2216 20.2166 9.67447C16.6497 11.2561 14.5537 14.8244 14.9533 18.8035C15.0782 20.0477 15.4916 20.9292 16.1542 21.9775H11.1443C8.65808 21.9775 6.6665 24.2882 6.6665 26.7743V33.5266C6.6665 34.7696 7.67422 35.7774 8.91732 35.7774H73.4395C74.6825 35.7774 75.6903 34.7697 75.6903 33.5266V26.7743C75.6902 24.2882 73.6512 21.9775 71.165 21.9775ZM34.4023 21.2273V21.9775H23.8138C21.0162 21.9775 18.8381 19.6647 19.5369 16.752C19.8446 15.4692 20.7705 14.3758 21.9681 13.8222C23.6125 13.0622 25.4341 13.3062 26.8152 14.5563L34.4042 21.161C34.4038 21.1832 34.4023 21.2051 34.4023 21.2273ZM62.9227 18.2039C62.755 20.5433 60.6181 21.9778 58.2728 21.9778H47.907V21.2276C47.907 21.201 47.9053 21.1747 47.9051 21.1482C49.6296 19.5849 53.3945 16.4894 55.4235 14.6501C56.5767 13.6049 58.1903 13.117 59.6818 13.5619C61.8222 14.2004 63.0792 16.0206 62.9227 18.2039Z"
                :class="fillColorSecondary"
            />
            <path
                d="M71.2413 46.688C59.4234 47.2011 49.9998 56.9445 49.9998 68.8889C49.9998 72.0493 50.6595 75.0555 51.8487 77.7775H15.6988C13.1674 77.7775 11.1152 75.7254 11.1152 73.1939V35.7162H71.2413V46.688Z"
                :class="fillColorPrimary"
            />
            <path
                d="M71.2413 46.688C59.4234 47.2011 49.9998 56.9445 49.9998 68.8889C49.9998 72.0493 50.6595 75.0555 51.8487 77.7775H15.6988C13.1674 77.7775 11.1152 75.7254 11.1152 73.1939V35.7162H71.2413V46.688Z"
                :class="fillColorPrimary"
            />
            <rect
                x="33.4946"
                y="35.7163"
                width="15.2337"
                height="42.0613"
                :class="fillColorSecondary"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M72.2275 52.2223C68.931 52.222 65.7084 53.1992 62.9673 55.0304C60.2262 56.8616 58.0898 59.4646 56.8281 62.5101C55.5664 65.5556 55.2362 68.9069 55.8793 72.14C56.5223 75.3732 58.1097 78.3431 60.4406 80.6741C62.7716 83.005 65.7415 84.5924 68.9747 85.2355C72.2078 85.8785 75.5591 85.5483 78.6046 84.2866C81.6501 83.0249 84.2531 80.8885 86.0843 78.1474C87.9155 75.4063 88.8927 72.1837 88.8924 68.8872C88.8906 64.468 87.1342 60.2302 84.0094 57.1053C80.8845 53.9805 76.6467 52.2241 72.2275 52.2223ZM72.2275 80.7957C70.077 80.7952 67.9669 80.2122 66.1213 79.1085C64.2758 78.0048 62.7637 76.4217 61.7459 74.5275C60.728 72.6332 60.2424 70.4986 60.3406 68.3504C60.4389 66.2022 61.1173 64.1208 62.3038 62.3273L78.7908 78.8212C76.8464 80.1139 74.5623 80.801 72.2275 80.7957ZM82.1511 75.4557L65.6641 58.9601C67.9531 57.4481 70.6941 56.7732 73.4233 57.0497C76.1526 57.3261 78.7026 58.5369 80.6419 60.4772C82.5811 62.4174 83.7906 64.9681 84.0656 67.6975C84.3406 70.4269 83.6643 73.1675 82.1511 75.4557Z"
                :class="fillColorPrimary"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-gray-400',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-gray-700',
    },
})
</script>
