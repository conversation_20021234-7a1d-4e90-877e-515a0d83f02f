<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.6933 22C16.2018 21.9985 15.7155 21.8988 15.2631 21.7068C9.42422 19.207 4.77317 14.5534 2.27652 8.71311C1.99424 8.05585 1.91558 7.32904 2.05071 6.62661C2.18585 5.92417 2.52856 5.27842 3.03455 4.77281L5.17991 2.62745C5.58216 2.22569 6.12744 2.00003 6.69596 2.00003C7.26448 2.00003 7.80976 2.22569 8.21201 2.62745L10.7364 5.15182C11.1381 5.55407 11.3638 6.09935 11.3638 6.66787C11.3638 7.23639 11.1381 7.78167 10.7364 8.18393L9.57074 9.36387C10.0217 10.5083 10.7032 11.5477 11.573 12.4175C12.4428 13.2873 13.4822 13.9688 14.6266 14.4198L15.828 13.2255C16.2303 12.8238 16.7756 12.5981 17.3441 12.5981C17.9126 12.5981 18.4579 12.8238 18.8601 13.2255L21.3845 15.7499C21.7863 16.1521 22.0119 16.6974 22.0119 17.2659C22.0119 17.8345 21.7863 18.3797 21.3845 18.782L19.2392 20.9274C18.9072 21.2661 18.5111 21.5353 18.074 21.7195C17.6369 21.9036 17.1676 21.999 16.6933 22ZM6.72456 3.40693C6.63045 3.40638 6.53715 3.42442 6.45002 3.46001C6.3629 3.4956 6.28365 3.54803 6.21683 3.61431L4.07147 5.75967C3.76682 6.06295 3.5603 6.45076 3.47869 6.87282C3.39708 7.29488 3.44415 7.73173 3.61379 8.12672C5.95427 13.6416 10.3357 18.0384 15.8423 20.3982C16.2373 20.5678 16.6742 20.6149 17.0962 20.5333C17.5183 20.4517 17.9061 20.2451 18.2094 19.9405L20.3547 17.7951C20.4218 17.7287 20.475 17.6496 20.5113 17.5624C20.5476 17.4753 20.5663 17.3818 20.5663 17.2874C20.5663 17.193 20.5476 17.0995 20.5113 17.0124C20.475 16.9252 20.4218 16.8461 20.3547 16.7797L17.8232 14.2553C17.6892 14.1221 17.508 14.0473 17.3191 14.0473C17.1301 14.0473 16.9489 14.1221 16.8149 14.2553L15.2988 15.7713C15.2064 15.862 15.091 15.9259 14.965 15.9561C14.8391 15.9864 14.7073 15.9818 14.5837 15.943C13.065 15.4367 11.685 14.5838 10.553 13.4518C9.42098 12.3198 8.56808 10.9398 8.06184 9.42108C8.023 9.29751 8.01845 9.16572 8.04868 9.03977C8.07891 8.91382 8.14278 8.79844 8.23347 8.70596L9.74952 7.18991C9.8158 7.12309 9.86823 7.04384 9.90382 6.95671C9.93941 6.86959 9.95745 6.77629 9.9569 6.68217C9.95611 6.49457 9.88163 6.31479 9.74952 6.18159L7.2323 3.61431C7.16548 3.54803 7.08623 3.4956 6.9991 3.46001C6.91197 3.42442 6.81868 3.40638 6.72456 3.40693Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
