<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clip-path="url(#clip0_11655_79412)">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M3.69638 2.75C3.22055 2.75 2.82812 3.14243 2.82812 3.61826V7.72303H4.56464V3.61826C4.56464 3.14243 4.17221 2.75 3.69638 2.75ZM4.56465 9.22303H2.07812C1.66391 9.22303 1.32812 8.88724 1.32812 8.47303V3.61826C1.32812 2.314 2.39213 1.25 3.69638 1.25C3.70525 1.25 3.71412 1.25005 3.72297 1.25015C3.72793 1.25005 3.73291 1.25 3.7379 1.25H16.9742C18.2798 1.25 19.3426 2.3128 19.3426 3.61838V9.01703C20.0252 9.28536 20.6649 9.69539 21.2167 10.2471C23.3649 12.3954 23.3649 15.8784 21.2167 18.0267C20.6649 18.5784 20.0252 18.9884 19.3426 19.2568V22C19.3426 22.246 19.222 22.4763 19.0198 22.6164C18.8176 22.7566 18.5596 22.7886 18.3293 22.7023L16.9329 22.1786L15.5364 22.7023C15.3667 22.7659 15.1796 22.7659 15.0098 22.7023L13.6134 22.1786L12.217 22.7023C12.0472 22.7659 11.8601 22.7659 11.6903 22.7023L10.2939 22.1786L8.89747 22.7023C8.72769 22.7659 8.54059 22.7659 8.37081 22.7023L6.9744 22.1786L5.57798 22.7023C5.34766 22.7886 5.08963 22.7566 4.88745 22.6164C4.68527 22.4763 4.56465 22.246 4.56465 22V9.22303ZM6.06272 3.52257C6.064 3.54046 6.06465 3.55853 6.06465 3.57676V20.9178L6.71107 20.6754C6.88085 20.6117 7.06794 20.6117 7.23772 20.6754L8.63414 21.199L10.0306 20.6754C10.2003 20.6117 10.3874 20.6117 10.5572 20.6754L11.9536 21.199L13.35 20.6754C13.5198 20.6117 13.7069 20.6117 13.8767 20.6754L15.2731 21.199L16.6695 20.6754C16.8393 20.6117 17.0264 20.6117 17.1962 20.6754L17.8426 20.9178V19.6138C16.2683 19.7612 14.6425 19.2321 13.4371 18.0267C13.3129 17.9024 13.1958 17.7737 13.0859 17.6411C13.0047 17.6713 12.9168 17.6877 12.825 17.6877H7.80427C7.39005 17.6877 7.05427 17.352 7.05427 16.9377C7.05427 16.5235 7.39005 16.1877 7.80427 16.1877H12.221C12.0919 15.866 11.9945 15.5346 11.9287 15.1982H7.80427C7.39005 15.1982 7.05427 14.8624 7.05427 14.4482C7.05427 14.0339 7.39005 13.6982 7.80427 13.6982H11.8433C11.8698 13.3648 11.9266 13.0335 12.0136 12.7085H7.80427C7.39005 12.7085 7.05427 12.3728 7.05427 11.9585C7.05427 11.5443 7.39005 11.2085 7.80427 11.2085H12.6687C12.8837 10.8672 13.1398 10.5444 13.4371 10.2471C14.6425 9.04166 16.2683 8.51262 17.8426 8.65999V3.61838C17.8426 3.14122 17.4514 2.75 16.9742 2.75H5.89935C5.99483 2.99091 6.05184 3.25098 6.06272 3.52257ZM7.05427 4.48966C7.05427 4.07544 7.39005 3.73966 7.80427 3.73966H12.825C13.2392 3.73966 13.575 4.07544 13.575 4.48966C13.575 4.90387 13.2392 5.23966 12.825 5.23966H7.80427C7.39005 5.23966 7.05427 4.90387 7.05427 4.48966ZM14.5646 4.48966C14.5646 4.07544 14.9004 3.73966 15.3146 3.73966H16.103C16.5172 3.73966 16.853 4.07544 16.853 4.48966C16.853 4.90387 16.5172 5.23966 16.103 5.23966H15.3146C14.9004 5.23966 14.5646 4.90387 14.5646 4.48966ZM7.05427 6.97931C7.05427 6.5651 7.39005 6.22931 7.80427 6.22931H12.825C13.2392 6.22931 13.575 6.5651 13.575 6.97931C13.575 7.39353 13.2392 7.72931 12.825 7.72931H7.80427C7.39005 7.72931 7.05427 7.39353 7.05427 6.97931ZM14.5646 6.97931C14.5646 6.5651 14.9004 6.22931 15.3146 6.22931H16.103C16.5172 6.22931 16.853 6.5651 16.853 6.97931C16.853 7.39353 16.5172 7.72931 16.103 7.72931H15.3146C14.9004 7.72931 14.5646 7.39353 14.5646 6.97931ZM20.156 11.3078C18.5935 9.74529 16.0602 9.74529 14.4978 11.3078C12.9353 12.8703 12.9353 15.4035 14.4978 16.966C16.0602 18.5285 18.5935 18.5285 20.156 16.966C21.7185 15.4035 21.7185 12.8703 20.156 11.3078ZM7.05427 9.46897C7.05427 9.05476 7.39005 8.71897 7.80427 8.71897H12.825C13.2392 8.71897 13.575 9.05476 13.575 9.46897C13.575 9.88318 13.2392 10.219 12.825 10.219H7.80427C7.39005 10.219 7.05427 9.88318 7.05427 9.46897ZM17.3158 12.9518C17.3195 12.9518 17.3233 12.9519 17.327 12.9519C17.3308 12.9519 17.3346 12.9518 17.3383 12.9518H18.0329C18.4471 12.9518 18.7829 12.616 18.7829 12.2018C18.7829 11.7919 18.4541 11.4588 18.0459 11.4519C17.9537 11.1422 17.6667 10.9164 17.327 10.9164C16.9328 10.9164 16.6097 11.2205 16.5794 11.607C16.0171 11.8707 15.6278 12.4426 15.6278 13.1056C15.6278 13.6727 15.9046 14.1803 16.3691 14.4838C16.4036 14.5093 16.4408 14.5322 16.4805 14.5521L16.5317 14.5777M16.5403 14.582L17.4472 15.0355C17.4805 15.053 17.4974 15.0721 17.5067 15.0871C17.5163 15.1028 17.5263 15.1278 17.5263 15.1681C17.5263 15.2555 17.4566 15.3219 17.3757 15.3219H17.3302L17.327 15.3219L17.3238 15.3219H16.6212C16.207 15.3219 15.8712 15.6577 15.8712 16.0719C15.8712 16.4818 16.2 16.8149 16.6081 16.8218C16.7004 17.1316 16.9873 17.3574 17.327 17.3574C17.7213 17.3574 18.0445 17.0532 18.0747 16.6668C18.637 16.4031 19.0263 15.8311 19.0263 15.1681C19.0263 14.6009 18.7494 14.0933 18.2848 13.7898C18.2504 13.7643 18.2132 13.7415 18.1736 13.7216L18.1248 13.6972C18.1212 13.6954 18.1175 13.6936 18.1139 13.6917L17.2111 13.2403L17.2078 13.2387C17.1739 13.2211 17.1568 13.2017 17.1474 13.1866C17.1378 13.1709 17.1278 13.146 17.1278 13.1056C17.1278 13.0182 17.1975 12.9518 17.2784 12.9518H17.3158"
                    :class="fillColor"
                />
            </g>
            <defs>
                <clipPath id="clip0_11655_79412">
                    <rect width="20" height="20" fill="white" />
                </clipPath>
            </defs>
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
