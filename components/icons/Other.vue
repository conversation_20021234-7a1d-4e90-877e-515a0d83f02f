<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M2.90841 9.95312C2.72332 10.6881 2.625 11.4575 2.625 12.25C2.625 13.0424 2.72331 13.8118 2.90841 14.5468L4.69708 13.9656C4.56814 13.4146 4.49994 12.8403 4.49994 12.25C4.49994 11.6597 4.56814 11.0853 4.69708 10.5343L2.90841 9.95312ZM3.29443 8.76422L5.0831 9.34538C5.53528 8.26988 6.23097 7.32199 7.10117 6.57071L5.99597 5.04951C4.81289 6.03708 3.87694 7.31075 3.29443 8.76422ZM7.00666 4.31397L8.11187 5.83518C9.078 5.24834 10.1872 4.87361 11.3749 4.77563L11.375 2.89547C9.77623 3.00071 8.28695 3.50671 7.00666 4.31397ZM12.625 2.89547L12.6249 4.77563C13.8127 4.87362 14.9219 5.24835 15.888 5.83519L16.9933 4.31393C15.713 3.50669 14.2237 3.0007 12.625 2.89547ZM18.004 5.04946L16.8987 6.57071C17.7689 7.32199 18.4646 8.26988 18.9168 9.34537L20.7055 8.76417C20.123 7.3107 19.1871 6.03703 18.004 5.04946ZM21.0916 9.95307L19.3028 10.5343C19.4317 11.0853 19.4999 11.6597 19.4999 12.25C19.4999 12.8403 19.4317 13.4146 19.3028 13.9657L21.0916 14.5469C21.2767 13.8119 21.375 13.0424 21.375 12.25C21.375 11.4575 21.2767 10.6881 21.0916 9.95307ZM20.7056 15.7358L18.9168 15.1546C18.4646 16.23 17.7689 17.1779 16.8987 17.9292L18.004 19.4505C19.1871 18.4629 20.123 17.1892 20.7056 15.7358ZM16.9933 20.186L15.888 18.6647C14.9219 19.2516 13.8127 19.6263 12.6249 19.7243L12.625 21.6045C14.2237 21.4992 15.713 20.9932 16.9933 20.186ZM11.375 21.6045L11.3749 19.7243C10.1872 19.6263 9.078 19.2516 8.11187 18.6647L7.00665 20.186C8.28694 20.9932 9.77623 21.4992 11.375 21.6045ZM5.99596 19.4504L7.10117 17.9292C6.23097 17.1779 5.53528 16.23 5.0831 15.1545L3.29443 15.7357C3.87693 17.1892 4.81288 18.4629 5.99596 19.4504ZM1.375 12.25C1.375 6.38194 6.13197 1.62497 12 1.62497C17.868 1.62497 22.625 6.38194 22.625 12.25C22.625 18.118 17.868 22.875 12 22.875C6.13197 22.875 1.375 18.118 1.375 12.25ZM11.9999 5.99996C8.54816 5.99996 5.74994 8.79818 5.74994 12.25C5.74994 15.7017 8.54816 18.5 11.9999 18.5C15.4517 18.5 18.2499 15.7017 18.2499 12.25C18.2499 8.79818 15.4517 5.99996 11.9999 5.99996ZM11.9999 8.34368L15.0392 10.6234C15.0391 10.6233 15.0393 10.6234 15.0392 10.6234C15.1726 10.7232 15.2924 10.8344 15.3985 10.9543C15.7951 11.4023 16.0005 11.9725 16.0005 12.5467C16.0005 13.0479 15.8438 13.5547 15.5202 13.9861C14.8355 14.899 13.6253 15.1857 12.6249 14.7409V16H11.3749V14.7409C10.3746 15.1857 9.16451 14.8991 8.47978 13.9862C8.15603 13.5548 7.99934 13.0478 7.99934 12.5467C7.99934 11.9725 8.20472 11.4024 8.60133 10.9543C8.7075 10.8344 8.82716 10.7232 8.96065 10.6234L11.9999 8.34368ZM12.908 13.4665C13.4167 13.848 14.1387 13.7448 14.5202 13.2361C14.6758 13.0287 14.7505 12.787 14.7505 12.5467C14.7505 12.2689 14.6511 11.9958 14.4626 11.7829C14.4116 11.7253 14.3543 11.6721 14.2905 11.6243L11.9999 9.90623L9.71003 11.6238C9.64616 11.6716 9.58838 11.7251 9.53742 11.7827C9.34894 11.9956 9.24934 12.269 9.24934 12.5467C9.24934 12.787 9.32398 13.0286 9.47957 13.2359C9.86114 13.7447 10.5832 13.848 11.0919 13.4664L11.9999 12.7851L12.908 13.4665C12.9081 13.4665 12.908 13.4665 12.908 13.4665Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 20,
    },
    h: {
        type: Number,
        default: 20,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
