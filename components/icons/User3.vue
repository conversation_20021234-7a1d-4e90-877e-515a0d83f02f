<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10 10.5263C7.38432 10.5263 5.26316 8.38373 5.26316 5.74163V4.78469C5.26316 2.14258 7.38432 0 10 0C12.6157 0 14.7368 2.14258 14.7368 4.78469V5.74163C14.7368 8.38373 12.6157 10.5263 10 10.5263Z"
                fill="#00D486"
            />
            <path
                d="M20 17.7979C20 15.9032 18.803 14.2368 17.066 13.7389C15.172 13.1947 12.586 12.6316 10 12.6316C7.414 12.6316 4.828 13.1947 2.934 13.7389C1.197 14.2368 0 15.9032 0 17.7979C0 19.0141 0.985914 20 2.2021 20H17.7979C19.0141 20 20 19.0141 20 17.7979Z"
                fill="#00D486"
            />
            <path
                d="M10 10.5263C7.38432 10.5263 5.26316 8.38373 5.26316 5.74163V4.78469C5.26316 2.14258 7.38432 0 10 0C12.6157 0 14.7368 2.14258 14.7368 4.78469V5.74163C14.7368 8.38373 12.6157 10.5263 10 10.5263Z"
                fill="#00D486"
            />
            <path
                d="M20 17.7979C20 15.9032 18.803 14.2368 17.066 13.7389C15.172 13.1947 12.586 12.6316 10 12.6316C7.414 12.6316 4.828 13.1947 2.934 13.7389C1.197 14.2368 0 15.9032 0 17.7979C0 19.0141 0.985914 20 2.2021 20H17.7979C19.0141 20 20 19.0141 20 17.7979Z"
                fill="#00D486"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
