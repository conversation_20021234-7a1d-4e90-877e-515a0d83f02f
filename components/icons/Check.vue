<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M15.8896 6.39927C16.3962 6.89518 16.3962 7.69919 15.8896 8.1951L9.7274 14.2268C9.22077 14.7227 8.39937 14.7227 7.89274 14.2268L4.6495 11.0522C4.14287 10.5563 4.14287 9.75232 4.6495 9.25641C5.15613 8.76051 5.97753 8.76051 6.48416 9.25641L8.81007 11.5331L14.0549 6.39927C14.5615 5.90337 15.3829 5.90337 15.8896 6.39927Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-green-700',
    },
})
</script>
