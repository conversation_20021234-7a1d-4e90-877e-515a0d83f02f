<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M13.3684 7.62481C13.3799 7.5842 13.3877 7.54143 13.3947 7.4981C13.3964 7.48775 13.3996 7.47793 13.401 7.46731C13.4083 7.4128 13.4123 7.35694 13.4123 7.29999C13.4123 7.24304 13.4083 7.18718 13.401 7.13267C13.3996 7.12205 13.3964 7.11223 13.3947 7.10188C13.3877 7.05855 13.3799 7.01576 13.3684 6.97517C13.3664 6.96835 13.3635 6.96237 13.3613 6.95556C13.3338 6.8648 13.2967 6.78062 13.2482 6.70813L11.5895 4.24525C11.4838 4.08719 11.343 4 11.1935 4C11.044 4 10.9033 4.08719 10.7975 4.24525C10.6918 4.40331 10.6335 4.61367 10.6335 4.83713C10.6335 5.0606 10.6918 5.27095 10.7975 5.42901L11.5002 6.46286H1.13906C0.830219 6.46286 0.578979 6.83838 0.578979 7.29999C0.578979 7.7616 0.830219 8.13712 1.13906 8.13712H11.5002L10.7975 9.17099C10.6918 9.32903 10.6335 9.5394 10.6335 9.76287C10.6335 9.98631 10.6918 10.1967 10.7975 10.3547C10.9035 10.5128 11.044 10.6 11.1935 10.6C11.343 10.6 11.4836 10.5128 11.5895 10.3547L13.2482 7.89187C13.2967 7.81938 13.3338 7.73517 13.3613 7.64444C13.3635 7.63763 13.3664 7.63162 13.3684 7.62481Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
