<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.2411 2.28982C12.4946 2.37587 12.6829 2.59046 12.7354 2.85293C13.1994 5.17285 14.6006 7.42003 16.4685 8.91436C18.6032 10.6221 19.75 12.7221 19.75 15C19.75 16.0178 19.5495 17.0255 19.1601 17.9658C18.7706 18.9061 18.1997 19.7604 17.4801 20.4801C16.7604 21.1997 15.9061 21.7706 14.9658 22.1601C14.0255 22.5496 13.0177 22.75 12 22.75C10.9823 22.75 9.97448 22.5496 9.0342 22.1601C8.09393 21.7706 7.23958 21.1997 6.51992 20.4801C5.80027 19.7604 5.22941 18.9061 4.83993 17.9658C4.45046 17.0255 4.25 16.0178 4.25 15C4.25 13.6817 4.73789 12.3738 5.41524 11.5304C5.61452 11.2822 5.94871 11.1869 6.24893 11.2925C6.54914 11.3982 6.75 11.6818 6.75 12C6.75 12.4641 6.93437 12.9093 7.26256 13.2374C7.59075 13.5656 8.03587 13.75 8.5 13.75C8.96413 13.75 9.40925 13.5656 9.73744 13.2374C10.0656 12.9093 10.25 12.4641 10.25 12C10.25 11.073 10.0091 10.6221 9.6314 9.91501C9.53845 9.741 9.43721 9.55149 9.32924 9.33555C8.70747 8.09259 8.62842 6.86629 9.05834 5.67209C9.47567 4.51286 10.3492 3.4479 11.5061 2.43558C11.7076 2.25933 11.9877 2.20378 12.2411 2.28982ZM10.4697 6.18018C10.1876 6.96374 10.2205 7.76444 10.6708 8.66448L10.6708 8.6646C10.7486 8.82022 10.8315 8.9734 10.9155 9.12853C11.3205 9.87702 11.75 10.6707 11.75 12C11.75 12.862 11.4076 13.6886 10.7981 14.2981C10.1886 14.9076 9.36195 15.25 8.5 15.25C7.63805 15.25 6.8114 14.9076 6.2019 14.2981C6.08799 14.1842 5.98341 14.0627 5.88865 13.9348C5.80119 14.275 5.75 14.6366 5.75 15C5.75 15.8208 5.91166 16.6335 6.22575 17.3918C6.53984 18.1501 7.00022 18.8391 7.58058 19.4194C8.16095 19.9998 8.84994 20.4602 9.60823 20.7743C10.3665 21.0884 11.1792 21.25 12 21.25C12.8208 21.25 13.6335 21.0884 14.3918 20.7743C15.1501 20.4602 15.8391 19.9998 16.4194 19.4194C16.9998 18.8391 17.4602 18.1501 17.7742 17.3918C18.0883 16.6335 18.25 15.8208 18.25 15C18.25 13.278 17.3968 11.5779 15.5315 10.0857C13.7425 8.65445 12.3289 6.62676 11.6036 4.42793C11.0454 5.03848 10.6714 5.61988 10.4697 6.18018Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
