<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="12"
            height="13"
            viewBox="0 0 12 13"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M9.33333 3.58333V11.0833C9.33333 11.1938 9.28943 11.2998 9.21129 11.378C9.13315 11.4561 9.02717 11.5 8.91667 11.5H1.41667C1.30616 11.5 1.20018 11.4561 1.12204 11.378C1.0439 11.2998 1 11.1938 1 11.0833V3.58333C1 3.47283 1.0439 3.36685 1.12204 3.28871C1.20018 3.21057 1.30616 3.16667 1.41667 3.16667H8.91667C9.02717 3.16667 9.13315 3.21057 9.21129 3.28871C9.28943 3.36685 9.33333 3.47283 9.33333 3.58333ZM10.5833 1.5H3.08333C2.97283 1.5 2.86685 1.5439 2.78871 1.62204C2.71057 1.70018 2.66667 1.80616 2.66667 1.91667C2.66667 2.02717 2.71057 2.13315 2.78871 2.21129C2.86685 2.28943 2.97283 2.33333 3.08333 2.33333H10.1667V9.41667C10.1667 9.52717 10.2106 9.63315 10.2887 9.71129C10.3668 9.78943 10.4728 9.83333 10.5833 9.83333C10.6938 9.83333 10.7998 9.78943 10.878 9.71129C10.9561 9.63315 11 9.52717 11 9.41667V1.91667C11 1.80616 10.9561 1.70018 10.878 1.62204C10.7998 1.5439 10.6938 1.5 10.5833 1.5Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-900',
    },
})
</script>
