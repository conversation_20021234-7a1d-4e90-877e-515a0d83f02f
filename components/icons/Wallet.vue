<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="47"
            height="41"
            viewBox="0 0 47 41"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <g filter="url(#filter0_b_11655_89013)">
                <path
                    d="M0.598633 9.97846C0.598633 4.58185 4.97345 0.207031 10.3701 0.207031H36.4272C41.8238 0.207031 46.1986 4.58185 46.1986 9.97846V24.6356C46.1986 30.0322 41.8238 34.407 36.4272 34.407H10.3701C4.97345 34.407 0.598633 30.0322 0.598633 24.6356V9.97846Z"
                    :class="fillColorSecondary"
                />
            </g>
            <path
                d="M0.598633 16.144C0.598633 10.7474 4.97345 6.37256 10.3701 6.37256H36.4272C41.8238 6.37256 46.1986 10.7474 46.1986 16.144V30.8011C46.1986 36.1977 41.8238 40.5726 36.4272 40.5726H10.3701C4.97345 40.5726 0.598633 36.1977 0.598633 30.8011V16.144Z"
                :class="fillColorPrimary"
            />
            <path
                d="M20.3982 30.728C20.3982 29.7737 21.2041 29 22.1982 29H38.3982C39.3923 29 40.1982 29.7737 40.1982 30.728C40.1982 31.6823 39.3923 32.456 38.3982 32.456H22.1982C21.2041 32.456 20.3982 31.6823 20.3982 30.728Z"
                fill="white"
            />
            <defs>
                <filter
                    id="filter0_b_11655_89013"
                    x="-8.56208"
                    y="-8.95368"
                    width="63.9214"
                    height="52.5214"
                    filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB"
                >
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feGaussianBlur in="BackgroundImageFix" stdDeviation="4.58036" />
                    <feComposite
                        in2="SourceAlpha"
                        operator="in"
                        result="effect1_backgroundBlur_11655_89013"
                    />
                    <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_backgroundBlur_11655_89013"
                        result="shape"
                    />
                </filter>
            </defs>
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
