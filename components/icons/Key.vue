<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M9.73905 1.9489C10.8956 1.22329 12.2642 0.911672 13.621 1.06502C14.9777 1.21836 16.2422 1.8276 17.2077 2.79305C18.1731 3.7585 18.7823 5.02303 18.9357 6.37975C19.089 7.73646 18.7774 9.10507 18.0518 10.2617C17.3262 11.4182 16.2296 12.2944 14.9413 12.7467C13.7785 13.1549 12.5218 13.1961 11.3399 12.87L10.9294 13.2805C10.9293 13.2805 10.9294 13.2805 10.9294 13.2805C10.5005 13.7094 9.9188 13.9506 9.31225 13.9507H9.16906C9.11391 13.9507 9.06102 13.9726 9.02203 14.0116C8.98303 14.0506 8.96113 14.1035 8.96113 14.1586V14.9904C8.96113 15.3764 8.80777 15.7466 8.53481 16.0196C8.26184 16.2926 7.89161 16.4459 7.50558 16.4459H6.67383C6.61868 16.4459 6.5658 16.4678 6.5268 16.5068C6.48781 16.5458 6.4659 16.5987 6.4659 16.6538V17.4856C6.4659 17.8716 6.31255 18.2418 6.03958 18.5148C5.76661 18.7878 5.39638 18.9411 5.01035 18.9411H2.51512C2.12909 18.9411 1.75886 18.7878 1.48589 18.5148C1.21292 18.2418 1.05957 17.8716 1.05957 17.4856V15.679C1.0597 15.0725 1.30074 14.4907 1.72968 14.0619C1.72966 14.0619 1.7297 14.0618 1.72968 14.0619L7.13075 8.66079C6.80465 7.47891 6.84582 6.22217 7.25405 5.05941C7.70635 3.77114 8.58246 2.6745 9.73905 1.9489ZM13.4808 2.30474C12.4048 2.18312 11.3194 2.43027 10.4021 3.00575C9.48479 3.58123 8.78995 4.45097 8.43122 5.4727C8.0725 6.49443 8.07114 7.60765 8.42736 8.63026C8.50608 8.85624 8.44859 9.10735 8.27937 9.27656L2.61188 14.9441C2.41692 15.139 2.30728 15.4034 2.30718 15.679C2.30718 15.679 2.30718 15.6791 2.30718 15.679V17.4856C2.30718 17.5407 2.32909 17.5936 2.36809 17.6326C2.40708 17.6716 2.45997 17.6935 2.51512 17.6935H5.01035C5.0655 17.6935 5.11839 17.6716 5.15738 17.6326C5.19638 17.5936 5.21828 17.5407 5.21828 17.4856V16.6538C5.21828 16.2678 5.37164 15.8976 5.64461 15.6246C5.91757 15.3516 6.2878 15.1983 6.67383 15.1983H7.50558C7.56073 15.1983 7.61361 15.1764 7.65261 15.1374C7.6916 15.0984 7.71351 15.0455 7.71351 14.9904V14.1586C7.71351 13.7726 7.86686 13.4024 8.13983 13.1294C8.4128 12.8564 8.78303 12.7031 9.16906 12.7031H9.31199C9.31194 12.7031 9.31203 12.7031 9.31199 12.7031C9.58766 12.703 9.85214 12.5934 10.047 12.3984L10.7241 11.7213C10.8934 11.5521 11.1445 11.4946 11.3705 11.5733C12.3931 11.9296 13.5063 11.9282 14.528 11.5695C15.5497 11.2108 16.4195 10.5159 16.995 9.59862C17.5704 8.68133 17.8176 7.59589 17.696 6.51987C17.5744 5.44385 17.0912 4.44095 16.3255 3.67525C15.5598 2.90954 14.5569 2.42636 13.4808 2.30474ZM13.7436 6.465C13.8585 6.465 13.9516 6.3719 13.9516 6.25706C13.9516 6.14222 13.8585 6.04912 13.7436 6.04912C13.6288 6.04912 13.5357 6.14222 13.5357 6.25706C13.5357 6.3719 13.6288 6.465 13.7436 6.465ZM12.704 6.25706C12.704 5.68286 13.1694 5.21738 13.7436 5.21738C14.3178 5.21738 14.7833 5.68286 14.7833 6.25706C14.7833 6.83126 14.3178 7.29674 13.7436 7.29674C13.1694 7.29674 12.704 6.83126 12.704 6.25706Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
