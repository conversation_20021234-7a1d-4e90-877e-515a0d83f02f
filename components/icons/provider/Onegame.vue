<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M16.3632 13.4695C16.1737 13.9951 15.9368 13.8995 15.9368 13.8995C15.9368 13.8995 16.0908 14.234 16.6593 14.1623C17.2279 14.0906 17.3463 13.374 17.3463 13.374C17.4481 13.3907 17.5515 13.3947 17.6543 13.3859C17.8484 13.3656 18.0393 13.3214 18.2228 13.2545C18.064 13.1049 17.9562 12.9084 17.9149 12.6932C18.0751 12.6777 18.2291 12.6227 18.3633 12.5332C18.4975 12.4436 18.6079 12.3222 18.6848 12.1796C18.2465 12.3587 18.0096 12.3229 17.8793 12.2871C17.8777 12.2112 17.8816 12.1354 17.8912 12.0601C18.1754 11.021 16.1855 10.4477 18.1044 8.47686C18.1044 8.47686 16.8488 9.89822 18.3531 10.7821C18.9335 11.1285 18.8387 11.869 19.0638 12.1796C19.2888 12.4901 19.2059 12.4782 19.5613 12.5498C19.7152 12.5737 19.2415 13.2307 19.2415 13.2307C19.2415 13.2307 19.5376 13.3979 20.0114 12.729C20.4259 13.159 21.2787 11.0329 20.5799 10.794C20.5799 10.794 20.189 11.021 19.9048 10.2565C19.7271 9.79072 20.4496 9.1099 20.0943 8.73963C20.1298 8.67991 20.876 8.58436 20.651 7.54521C20.5207 6.92411 20.5799 6.17163 20.6984 6.06413C20.8168 5.95663 20.0587 6.08802 19.7863 6.85245C19.9171 6.43612 20.0084 6.00818 20.0587 5.57442C20.0943 5.07276 20.0351 4.41583 19.3007 3.79473C19.3007 3.79473 19.9403 4.83387 19.2178 5.75358C19.2178 5.75358 19.6679 3.14974 16.683 2.21809C16.683 2.21809 17.4055 2.38531 17.8556 3.56779C17.9734 3.88624 17.9818 4.23536 17.8793 4.55916C17.8793 4.55916 17.7253 3.95 17.0147 3.11391C16.1145 2.03893 14.0535 0.450348 9.62358 2.31365C9.62358 2.31365 12.8927 1.81199 14.243 4.0575C14.8945 5.0847 15.6644 5.8133 16.0552 5.94469C14.5628 5.34748 13.5442 5.7058 13.5442 5.7058C14.0061 5.94469 14.7879 6.11191 14.9537 7.3541C14.7992 7.23008 14.6172 7.14573 14.4233 7.10828C14.2293 7.07083 14.0293 7.08141 13.8403 7.13911C13.6757 7.19645 13.5262 7.29076 13.4031 7.41492C13.28 7.53907 13.1865 7.68981 13.1296 7.85576C13.1296 7.85576 13.8995 7.09133 14.1956 7.99909C14.3496 8.42908 14.3141 9.50406 15.842 10.376C15.842 10.376 15.0958 10.7343 14.4681 10.2804C14.4681 10.2804 14.7049 11.3076 15.996 11.2599C16.2803 11.2479 16.458 11.3315 16.458 11.5465C16.4106 12.1318 15.309 11.9049 15.309 11.9049C15.349 12.0218 15.4129 12.1291 15.4965 12.2196C15.5802 12.3101 15.6817 12.382 15.7947 12.4304C16.4343 12.729 16.5646 12.4304 16.5646 12.4304C16.3158 13.2665 15.5459 12.9082 15.5459 12.9082C15.5459 12.9082 15.6881 13.4457 16.3632 13.4695ZM18.8151 8.13048C18.8442 8.20642 18.8888 8.2754 18.9459 8.33299C19.003 8.39057 19.0714 8.43551 19.1467 8.46492C19.2342 8.49214 19.3139 8.54028 19.379 8.60524C19.4441 8.67021 19.4927 8.75007 19.5206 8.83803C19.5485 8.926 19.555 9.01946 19.5394 9.11047C19.5237 9.20147 19.4866 9.28733 19.431 9.36073C19.431 9.36073 18.6848 9.69517 18.8151 8.13048ZM19.2178 8.0827C19.0348 8.00126 18.8866 7.85673 18.7998 7.67504C18.7129 7.49335 18.6932 7.2864 18.744 7.09133C18.3057 7.37799 17.8438 8.33353 17.8438 8.33353C17.7476 8.0419 17.6287 7.75834 17.4884 7.48549C17.1805 6.95995 16.7778 6.92411 16.7778 6.92411C16.1382 7.80798 16.6356 8.78741 16.6356 8.78741C16.3395 8.9188 15.8776 8.84713 15.8776 8.84713C15.8894 9.13379 16.1263 9.46823 16.3987 9.731C17.3226 10.6029 17.0265 10.9851 17.0265 10.9851C16.9791 10.4238 16.529 10.2446 16.2329 9.946C15.2143 8.93074 15.463 8.20214 15.4985 8.23798C15.7354 8.7038 16.304 8.56047 16.304 8.56047C15.8539 7.25855 16.837 5.9208 17.1568 5.50275C16.9199 6.58967 17.9741 6.67328 18.0451 7.53327C18.6611 6.45829 19.1941 6.3269 19.1941 6.3269C19.0401 6.75689 19.0756 7.49744 19.3481 7.76021C19.964 8.35742 19.8455 8.88296 19.8455 8.88296C19.8219 8.36936 19.4547 8.1902 19.2178 8.0827ZM19.6205 11.2001C20.1535 10.9613 20.0469 12.299 20.0469 12.299C19.3362 11.666 19.6205 11.2001 19.6205 11.2001Z"
                :class="fillColor"
            />
            <path
                d="M14.9186 5.39532C11.7679 4.81006 8.6054 6.63752 6.8287 9.79079C5.05199 12.9441 5.99957 17.5187 9.51744 19.0476C15.2266 21.52 19.4196 17.2679 20.912 13.8996C22.0136 11.4152 21.4095 9.80274 21.445 9.67135C21.4806 9.53996 23.3639 13.1829 18.318 18.9998C18.723 18.924 19.0998 18.7381 19.4077 18.4623C16.6006 22.5472 12.3957 21.9858 12.3957 21.9858C12.3957 21.9858 12.526 22.2247 13.9237 22.3442C8.09608 23.4311 6.12986 19.7523 6.12986 19.7523C6.12986 19.7523 5.81005 20.1464 6.30753 20.9825C4.74403 19.9673 2.46985 17.4231 3.46481 14.1146C3.46481 14.1146 2.6949 14.9626 2.56461 16.0257C1.0248 10.6986 4.62559 7.66472 4.62559 7.66472C4.62559 7.66472 4.16364 7.44973 3.02655 8.77553C3.21607 7.46167 6.87607 1.89567 12.0285 3.25731C13.6512 3.6873 13.7815 4.69061 14.9186 5.39532Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
