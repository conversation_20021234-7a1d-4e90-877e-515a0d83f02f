<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M5.25 11.0149V4.5L8.62559 7.73825L12.0012 4.5L15.3768 7.73825C16.4969 6.66421 17.6129 5.59368 18.7465 4.50616V11.0137L5.25 11.0149Z"
                :class="fillColor"
            />
            <path
                d="M15.8659 19.5V15.8332C15.8014 15.8713 15.7567 15.8956 15.7142 15.9229C14.7661 16.5352 13.8194 17.1497 12.8669 17.7567C12.7724 17.8134 12.6648 17.8447 12.5546 17.8476C12.1783 17.8585 11.8017 17.8585 11.4248 17.8476C11.3194 17.8442 11.2167 17.8134 11.1269 17.7582C10.1756 17.1508 9.22894 16.5367 8.27966 15.9243C8.23886 15.898 8.19718 15.8736 8.1329 15.8349V19.4991H5.25073V13.0294C5.29242 13.0264 5.33879 13.0203 5.38517 13.0203C6.09933 13.0203 6.81348 13.0168 7.52793 13.0238C7.61881 13.0305 7.70621 13.0614 7.78096 13.1135C9.13452 13.9978 10.4858 14.8855 11.8349 15.7766C11.9546 15.856 12.0368 15.8578 12.1578 15.7781C13.5062 14.886 14.8578 13.9987 16.2123 13.1161C16.3056 13.058 16.4129 13.0261 16.5228 13.0238C17.2027 13.0159 17.8828 13.0197 18.5626 13.0197H18.7457V19.5H15.8659Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
