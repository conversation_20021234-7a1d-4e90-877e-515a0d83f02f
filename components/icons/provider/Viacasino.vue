<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M16.7927 15.8885C16.7473 15.7201 16.7006 15.5516 16.6552 15.3845C16.6362 15.3176 16.5858 15.2891 16.5176 15.3064C16.4546 15.3225 16.3915 15.3399 16.3284 15.356C16.2161 15.3857 16.1026 15.4154 15.9903 15.4451C15.9361 15.46 15.9058 15.512 15.9197 15.564C15.9663 15.7374 16.0143 15.9095 16.061 16.0829C16.0723 16.125 16.0963 16.156 16.1417 16.1659L16.1455 16.1683H16.1796L16.1846 16.1646C16.36 16.1188 16.5353 16.073 16.7107 16.0272C16.7813 16.0086 16.8129 15.9566 16.7939 15.8885H16.7927ZM16.2426 16.0024C16.1985 16.0024 16.1631 15.9665 16.1631 15.9219C16.1631 15.8773 16.1997 15.8439 16.2451 15.8439C16.2893 15.8439 16.3259 15.8798 16.3259 15.9231C16.3259 15.9665 16.288 16.0049 16.2439 16.0036L16.2426 16.0024ZM16.3549 15.8129C16.3095 15.8129 16.2754 15.7783 16.2754 15.7324C16.2754 15.6879 16.3108 15.6532 16.3562 15.6532C16.4003 15.6532 16.4369 15.6903 16.4369 15.7337C16.4369 15.777 16.3991 15.8129 16.3549 15.8129ZM16.4659 15.6222C16.4218 15.6222 16.3864 15.5875 16.3852 15.5442C16.3852 15.5021 16.4218 15.4637 16.4659 15.4637C16.5113 15.4637 16.5467 15.4996 16.5467 15.5442C16.5467 15.5888 16.5126 15.6222 16.4659 15.6222Z"
                :class="fillColor"
            />
            <path
                d="M15.9007 14.6092C15.7254 14.5622 15.55 14.5163 15.3734 14.4705C15.3154 14.4557 15.2624 14.4854 15.2473 14.5424C15.1993 14.717 15.1514 14.8916 15.1047 15.0662C15.0883 15.1256 15.1198 15.1764 15.1817 15.1925C15.3557 15.2383 15.5286 15.2841 15.7027 15.33C15.7695 15.3473 15.8225 15.3176 15.8414 15.2507C15.8818 15.1021 15.9222 14.9535 15.9626 14.8061C15.9714 14.7739 15.979 14.7417 15.984 14.7207C15.984 14.66 15.955 14.6253 15.9007 14.6105V14.6092ZM15.5387 15.055C15.4516 15.055 15.386 14.9857 15.386 14.899C15.386 14.8148 15.4554 14.7479 15.5412 14.7467C15.6257 14.7467 15.6989 14.8173 15.7002 14.9015C15.7014 14.9857 15.6282 15.0563 15.5399 15.0563L15.5387 15.055Z"
                :class="fillColor"
            />
            <path
                d="M14.918 11.9021C14.7843 11.7956 14.6833 11.7523 14.6152 11.7709C14.4411 11.8439 14.262 11.9554 14.0765 12.1052C13.8923 12.2563 13.7018 12.4334 13.5025 12.639C13.3044 12.8445 13.0988 13.065 12.8843 13.3015C12.7317 13.4674 12.5753 13.6359 12.415 13.8068H12.4138C12.1716 13.7968 12.0971 13.7956 11.7401 13.8018C11.3995 13.8068 11.5975 13.8043 11.2582 13.7968C10.9201 13.7882 10.6362 13.7597 10.4091 13.7126C10.3713 13.7003 10.3612 13.7188 10.3789 13.7671C10.3965 13.8142 10.4344 13.8736 10.4911 13.943C10.5467 14.0136 10.6148 14.078 10.693 14.1337C10.7699 14.1919 10.8507 14.2253 10.9327 14.2352C11.0841 14.2451 11.2557 14.2451 11.2557 14.2451C11.2557 14.2451 11.4348 14.2389 11.6657 14.234H11.6732C11.9192 14.2278 11.8965 14.2241 12.0391 14.2241L12.0441 14.2328C11.5572 14.7801 11.0967 15.2321 10.5984 15.5516C10.1064 15.8674 9.46044 16.0817 9.23336 15.943C9.00628 15.8043 9.20308 15.4724 9.43269 15.1058C9.6623 14.7393 9.81873 14.2984 9.90956 14.0408C9.94993 13.9182 9.98399 13.8167 10.0092 13.7362C10.037 13.6421 9.99409 13.5442 9.90452 13.4934C9.89316 13.4873 9.88181 13.4811 9.86919 13.4736C9.78467 13.4278 9.69257 13.3882 9.59291 13.3523C9.49324 13.3151 9.4062 13.2916 9.32924 13.2842C9.25481 13.2743 9.21444 13.2891 9.20813 13.3263C9.20056 13.3882 9.1665 13.512 9.10847 13.699C9.058 13.8724 8.54202 14.9114 8.4903 15.1071C8.45498 15.2445 8.43101 15.3671 8.41965 15.4724L8.43227 15.7584C8.46759 15.9083 8.54581 16.0358 8.66566 16.1399C8.78929 16.2464 8.93185 16.3033 9.09333 16.312C9.3734 16.3467 9.65094 16.3231 9.9247 16.2427C10.2388 16.1498 10.5479 16.0012 10.8507 15.7981C11.1522 15.595 11.4462 15.3584 11.7338 15.0897C11.9533 14.8829 12.1678 14.6687 12.3772 14.4482C12.5273 14.2897 12.7998 14.2786 12.9449 14.442L12.9474 14.4458C13.0395 14.5523 13.1102 14.6786 13.1581 14.8247C13.2073 14.9721 13.2464 15.1207 13.2779 15.2693C13.3107 15.4204 13.3473 15.5516 13.389 15.6631C13.452 15.8067 13.5277 15.9132 13.6148 15.9838C13.7031 16.0557 13.7876 16.1015 13.8684 16.1201C13.9478 16.1374 14.0059 16.1399 14.0412 16.125C14.0753 16.1089 14.0702 16.0854 14.0248 16.0532C13.9213 15.9838 13.8646 15.8464 13.857 15.6445C13.8469 15.4414 13.8734 15.1999 13.9352 14.9213C13.997 14.6414 14.0816 14.3529 14.1888 14.0544C14.2948 13.756 14.4121 13.4773 14.5408 13.2173C14.6682 12.9585 14.7931 12.7455 14.9167 12.5808C14.947 12.5399 14.9773 12.504 15.0063 12.4718C15.1375 12.3294 15.1425 12.1201 15.0063 11.9826C14.9773 11.9529 14.947 11.9257 14.9155 11.9009L14.918 11.9021ZM13.9668 13.065C13.8633 13.3287 13.7599 13.621 13.6552 13.9393C13.6135 14.0668 13.5757 14.1919 13.5416 14.317C13.505 14.452 13.3082 14.4767 13.2363 14.3554C13.2224 14.3318 13.2086 14.3095 13.1934 14.2885C13.1392 14.2092 13.0799 14.1399 13.0168 14.0817C12.9159 13.9876 12.902 13.8414 12.9941 13.7399C13.0483 13.6792 13.1026 13.6185 13.1568 13.5578C13.3347 13.3547 13.5113 13.1603 13.6867 12.9721C13.7006 12.9572 13.7132 12.9436 13.7271 12.93C13.8305 12.821 14.021 12.9263 13.968 13.0637L13.9668 13.065Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M16.3827 11.0489C14.161 9.46375 12.6308 6.82724 9.357 7.72878C6.08323 8.63032 5.18247 15.699 5.18247 15.699C5.08785 15.6049 5.0008 15.5083 4.92133 15.4117C4.66901 15.0575 4.37128 14.4718 4.14546 13.9541C4.05589 13.7486 3.97767 13.5541 3.91964 13.3907C3.76447 12.9547 3.57145 12.1684 3.27498 10.9993C2.97851 9.83031 2.07145 7.58637 4.00543 6C3.61308 6.14613 3.10972 6.47802 2.71232 6.89659C2.46379 6.9003 1.90997 6.94241 1.67531 6.9808C1.34226 7.03281 0.992807 7.15541 0.691292 7.37956C0.539904 7.49101 0.39987 7.62847 0.287591 7.7907C0.172788 7.95169 0.0895248 8.13868 0.0441084 8.32816C-0.00256954 8.51763 -0.0114005 8.71205 0.0138308 8.89533C0.0390622 9.07985 0.104664 9.25199 0.199281 9.39564C0.341838 9.61112 0.55252 9.76716 0.790956 9.83527C0.870435 9.85756 0.952437 9.87118 1.03696 9.87242C1.27161 9.87861 1.51005 9.80555 1.71947 9.67056C1.7283 9.74734 1.72452 9.81545 1.74975 9.90214C1.84815 10.2353 2.17363 10.4086 2.04748 11.008C1.83301 10.9597 1.51383 10.8743 1.39146 10.774C1.39146 10.774 1.68919 11.0093 1.9516 11.0675C1.9516 11.0675 1.44571 11.0637 1.2262 10.9399C1.2262 10.9399 1.48482 11.0885 2.01089 11.1442C2.0008 11.1764 1.98818 11.2086 1.97557 11.2421C1.95412 11.2928 1.93267 11.3424 1.90997 11.3894C1.90618 11.3981 1.9024 11.4068 1.89861 11.4142C1.87717 11.4588 1.85572 11.5009 1.83427 11.5418C1.83049 11.5492 1.82544 11.5579 1.82166 11.5653C1.79895 11.6062 1.7775 11.6458 1.75479 11.6829C1.75353 11.6854 1.75227 11.6879 1.75101 11.6904C1.72956 11.7263 1.70685 11.7609 1.68541 11.7931C1.68162 11.7993 1.67784 11.8043 1.67405 11.8105C1.65261 11.8427 1.6299 11.8736 1.60845 11.9034C1.6034 11.9096 1.59962 11.9157 1.59457 11.9219C1.57439 11.9492 1.5542 11.9752 1.53528 12C1.52897 12.0086 1.52266 12.0161 1.5151 12.0247C1.49996 12.0433 1.48608 12.0606 1.47094 12.0767C1.46463 12.0842 1.45832 12.0928 1.45202 12.1003C1.43436 12.1201 1.41795 12.1387 1.40155 12.1572C1.39272 12.1671 1.38389 12.177 1.37506 12.1857C1.36623 12.1944 1.35866 12.2031 1.35109 12.2117C1.28549 12.2811 1.23124 12.3368 1.19213 12.3839C1.19087 12.3863 1.18835 12.3876 1.18709 12.39C1.177 12.4024 1.1669 12.4148 1.15933 12.426C1.15807 12.4284 1.15681 12.4297 1.15555 12.4322C1.14924 12.4421 1.14293 12.452 1.13915 12.4619C1.13915 12.4619 1.13789 12.4643 1.13663 12.4668C1.13284 12.4767 1.13032 12.4866 1.12906 12.4953C1.12906 12.4978 1.12906 12.499 1.12906 12.5015C1.12906 12.5114 1.12906 12.5201 1.13158 12.5287C1.13158 12.5287 1.33721 12.899 1.9024 12.9498V12.9523V12.956C1.9024 12.956 1.9024 12.9597 1.9024 12.9622V12.9671C1.90744 13.0241 1.9188 13.2173 1.8431 13.3845C1.73082 13.382 1.63999 13.538 1.74975 13.6321C1.90618 13.7659 2.05757 13.8798 2.26194 13.9343C2.29601 13.943 2.33133 13.9504 2.36539 13.9566C2.31997 14.052 2.22914 14.2612 2.24428 14.3566C2.26447 14.4829 2.49029 14.5362 2.49029 14.5362C2.49029 14.5362 2.73882 14.5547 2.82586 14.6798C2.83343 14.691 2.83974 14.7033 2.84479 14.717C2.84479 14.7194 2.85109 14.7417 2.85488 14.7677C2.86497 14.8284 2.95454 15.0798 3.09205 15.3956C3.13242 15.5244 4.01047 17.486 5.32881 17.5392C6.89694 17.4872 7.21612 12.2055 9.10973 10.2749C11.0033 8.34549 12.7027 10.5511 17.5723 12.9907C22.442 15.4303 24 13.3486 24 13.3486C20.5181 14.8061 18.6018 12.6365 16.3801 11.0526L16.3827 11.0489ZM2.30862 7.41547C2.30862 7.41547 2.15723 7.6681 2.13579 7.70154C2.14588 7.6842 2.19382 7.60618 2.30862 7.41547C1.83553 8.00122 1.63999 8.69843 1.70685 9.53805C1.505 9.66189 1.2754 9.72134 1.06219 9.70152C1.01678 9.69781 0.972622 9.69038 0.929728 9.67923C0.6976 9.62103 0.497011 9.46375 0.375901 9.25323C0.228297 9.00555 0.205589 8.67614 0.293899 8.37645C0.379685 8.07553 0.585321 7.82537 0.840157 7.65819C0.967575 7.57398 1.10509 7.50959 1.24638 7.46253C1.38642 7.41671 1.53402 7.38823 1.68162 7.37213C1.81535 7.3585 2.12569 7.33745 2.36539 7.32878C2.34647 7.35727 2.32754 7.38575 2.31114 7.41423L2.30862 7.41547Z"
                :class="fillColor"
            />
            <path
                d="M10.8986 12.4C10.9579 12.2315 10.8671 12.047 10.6943 11.9888C10.5681 11.9455 10.4331 11.9826 10.3473 12.0718C10.336 11.9492 10.2527 11.839 10.1266 11.7956C9.95498 11.7374 9.767 11.8266 9.70771 11.9962C9.6951 12.0322 9.68879 12.0681 9.69005 12.104C9.69005 12.1448 9.69888 12.1845 9.71402 12.2216C9.72033 12.2377 9.7279 12.2526 9.73673 12.2674L10.0483 12.8916C10.0483 12.8916 10.0534 12.8978 10.0572 12.899C10.0609 12.9003 10.066 12.899 10.0698 12.899L10.7321 12.5857C10.775 12.5647 10.8141 12.5349 10.8444 12.4978C10.8671 12.4693 10.886 12.4371 10.8999 12.4H10.8986Z"
                :class="fillColor"
            />
            <path
                d="M9.62445 12.3257C9.61309 12.3071 9.60426 12.2873 9.59543 12.2674C9.57399 12.2154 9.56389 12.1609 9.56263 12.1052C9.56263 12.0544 9.5702 12.0049 9.58786 11.9566C9.66986 11.7226 9.93101 11.5987 10.1682 11.6792C10.2742 11.7151 10.3612 11.7882 10.4155 11.8798C10.4924 11.8489 10.5757 11.839 10.6589 11.8526C10.6577 11.6668 10.5391 11.4935 10.3498 11.4303C10.1745 11.3709 9.98652 11.4216 9.86667 11.5467C9.85027 11.3758 9.73547 11.2223 9.56011 11.1628C9.32041 11.0811 9.05927 11.2062 8.976 11.4414C8.95834 11.491 8.95077 11.5418 8.95077 11.5913C8.95077 11.6483 8.96212 11.7027 8.98357 11.7548C8.9924 11.777 9.00249 11.7981 9.01511 11.8179L9.44909 12.6873C9.44909 12.6873 9.45666 12.6959 9.46171 12.6984C9.46675 12.7009 9.47306 12.6996 9.47811 12.6984L9.74556 12.5721L9.62319 12.3281L9.62445 12.3257Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
