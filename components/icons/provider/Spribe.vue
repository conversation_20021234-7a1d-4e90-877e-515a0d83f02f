<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="26"
            viewBox="0 0 21 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path d="M5.1665 25.1213V22.4378H15.8332V25.1213H5.1665Z" :class="fillColor" />
            <path
                d="M11.657 6.5842C11.5923 5.97642 11.5707 5.4341 11.4689 4.91048C11.3146 4.12038 10.8102 3.76974 9.9619 3.78065C9.16601 3.79156 8.69865 4.14999 8.52744 4.9136C8.17113 6.5016 8.84518 7.74831 9.87707 8.84074C10.9043 9.9285 12.0442 10.9165 13.0144 12.0526C14.4874 13.7777 14.8453 15.8426 14.3609 18.0306C13.8195 20.471 11.472 21.7925 8.85752 21.205C6.62098 20.7016 5.37315 19.2087 5.2698 16.8867C5.24513 16.3179 5.26672 15.7475 5.26672 15.1164H8.36085C8.41021 15.8317 8.4133 16.5298 8.51972 17.2109C8.657 18.0867 9.11511 18.4544 9.91563 18.4591C10.7177 18.4638 11.1511 18.0789 11.3408 17.2311C11.7357 15.4764 10.7732 14.2546 9.72437 13.0936C8.77268 12.0401 7.68526 11.1035 6.78911 10.0049C5.36389 8.25791 4.96132 6.232 5.56287 4.02532C6.22457 1.59891 8.39324 0.702833 10.5033 0.906982C13.8149 1.22801 14.4519 3.58274 14.5075 6.06213C14.5106 6.232 14.2175 6.54991 14.0478 6.56082C13.2596 6.61536 12.4653 6.5842 11.657 6.5842Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
