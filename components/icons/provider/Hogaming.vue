<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M11.9745 3.91316V10.0745C11.9041 10.1762 11.8198 10.3119 11.7298 10.4796C10.912 11.5582 10.4267 12.9028 10.4267 14.3607C10.4267 17.9115 13.3052 20.7899 16.8559 20.7899C20.4067 20.7899 23.2852 17.9115 23.2852 14.3607C23.2852 12.8379 22.7557 11.4387 21.8709 10.3373L21.8564 10.3127V3.91316C21.8564 3.52479 21.5416 3.20996 21.1532 3.20996H12.6777C12.2893 3.20996 11.9745 3.52479 11.9745 3.91316ZM17.1292 14.3373V15.3664H20.1263C20.0492 15.718 19.9248 16.0481 19.7533 16.3569C19.5904 16.6656 19.3803 16.94 19.123 17.1801C18.8657 17.4202 18.557 17.6089 18.1968 17.7461C17.8452 17.8747 17.4465 17.9391 17.0006 17.9391C16.366 17.9391 15.7914 17.789 15.2769 17.4888C14.7623 17.1887 14.3507 16.7728 14.042 16.2411C13.7418 15.7094 13.5918 15.0962 13.5918 14.4016C13.5918 13.6984 13.7418 13.0853 14.042 12.5622C14.3507 12.0305 14.7623 11.6146 15.2769 11.3144C15.7914 11.0143 16.366 10.8642 17.0006 10.8642C17.6866 10.8642 18.2826 11.0014 18.7886 11.2758C19.2945 11.5417 19.689 11.9147 19.972 12.3949L20.7438 11.6874C20.4865 11.2672 20.1692 10.9156 19.7919 10.6327C19.4232 10.3497 19.0029 10.1353 18.5313 9.98948C18.0682 9.8437 17.558 9.7708 17.0006 9.7708C16.3488 9.7708 15.7442 9.88657 15.1868 10.1181C14.6294 10.3411 14.1449 10.6627 13.7333 11.0829C13.3216 11.4945 13.0001 11.9833 12.7685 12.5493C12.5456 13.1153 12.4341 13.7327 12.4341 14.4016C12.4341 15.0705 12.5456 15.688 12.7685 16.254C13.0001 16.8199 13.3216 17.313 13.7333 17.7332C14.1449 18.1449 14.6294 18.4665 15.1868 18.698C15.7442 18.921 16.3488 19.0324 17.0006 19.0324C17.6866 19.0324 18.2955 18.9081 18.8272 18.6594C19.3674 18.4021 19.8219 18.0548 20.1907 17.6175C20.5594 17.1801 20.8381 16.6827 21.0268 16.1253C21.2154 15.5593 21.3098 14.9633 21.3098 14.3373H17.1292Z"
                :class="fillColor"
            />
            <path
                d="M2.95739 8.32959H1.28516V19.3921H2.95739V14.3754H7.58819V19.3921H9.13179V8.32959H7.58819V12.8318H2.95739V8.32959Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
