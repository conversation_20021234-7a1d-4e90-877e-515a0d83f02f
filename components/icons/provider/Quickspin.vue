<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M15.5385 1.667H5.45805C5.13765 1.667 4.83038 1.7837 4.60383 1.99142C4.37728 2.19915 4.25 2.48088 4.25 2.77465V17.226C4.25 17.3715 4.28125 17.5155 4.34196 17.6499C4.40267 17.7843 4.49165 17.9064 4.60383 18.0092C4.71601 18.1121 4.84918 18.1937 4.99575 18.2493C5.14232 18.305 5.2994 18.3337 5.45805 18.3337H15.5385C15.6975 18.3341 15.8549 18.3057 16.0019 18.2502C16.1488 18.1948 16.2824 18.1132 16.395 18.0103C16.5075 17.9074 16.5968 17.7852 16.6577 17.6506C16.7186 17.516 16.75 17.3717 16.75 17.226V2.77465C16.75 2.62893 16.7186 2.48464 16.6577 2.35004C16.5968 2.21545 16.5075 2.09321 16.395 1.99031C16.2824 1.88741 16.1488 1.80589 16.0019 1.75041C15.8549 1.69493 15.6975 1.66658 15.5385 1.667ZM14.6284 10.5581C14.6287 11.1577 14.4186 11.7414 14.0291 12.2231L12.4118 14.2217L14.2868 15.8122C14.3263 15.846 14.3537 15.8899 14.3656 15.9382C14.3775 15.9866 14.3733 16.037 14.3536 16.0831C14.3338 16.1292 14.2995 16.1688 14.2549 16.1968C14.2103 16.2248 14.1576 16.2398 14.1036 16.24H11.7414C11.6063 16.2407 11.4758 16.1947 11.3759 16.1113L11.0856 15.8648L10.8527 15.667L10.5 15.3647L9.61558 14.6197C9.56717 14.5783 9.53702 14.522 9.53087 14.4615C9.52471 14.401 9.54298 14.3406 9.58219 14.2916L12.0873 11.1947C12.3628 10.8541 12.5114 10.4413 12.5111 10.0172V7.41331C12.5111 6.57727 12.161 6.2193 12.0291 6.12431C11.8767 6.01598 11.5539 5.75693 10.5 5.75693C9.44606 5.75693 9.12158 6.01598 8.96918 6.12431C8.83562 6.22009 8.48545 6.57727 8.48545 7.41331V10.1719C8.48546 10.3985 8.53658 10.6226 8.63559 10.8303C8.7346 11.0379 8.87936 11.2245 9.06079 11.3784L9.90068 12.0912C9.94992 12.1327 9.98067 12.1894 9.98699 12.2504C9.99331 12.3115 9.97477 12.3725 9.93493 12.4217L8.92979 13.6612C8.90734 13.689 8.87876 13.7122 8.84591 13.7292C8.81306 13.7462 8.77668 13.7566 8.73913 13.7599C8.70158 13.7631 8.66371 13.7591 8.62797 13.7481C8.59223 13.737 8.55942 13.7192 8.53168 13.6958L7.34589 12.6894C7.03757 12.4277 6.79156 12.1104 6.6233 11.7574C6.45505 11.4044 6.36818 11.0234 6.36815 10.6382V6.57805C6.36815 5.40995 6.75514 4.88242 7.20034 4.52367C7.70462 4.11703 8.60616 3.75985 10.4983 3.75985C12.3904 3.75985 13.292 4.11703 13.7962 4.52367C14.2414 4.88242 14.6284 5.40995 14.6284 6.57805V10.5581Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
