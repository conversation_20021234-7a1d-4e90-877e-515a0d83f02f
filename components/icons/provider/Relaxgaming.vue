<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M12.5824 10C12.5824 11.2084 11.6526 12.1831 10.5 12.1831C9.34731 12.1831 8.4175 11.2084 8.4175 10C8.4175 8.79162 9.34731 7.81686 10.5 7.81686C11.6526 7.81686 12.5824 8.79162 12.5824 10Z"
                :class="fillColor"
            />
            <path
                d="M18 15.3169C18 16.5252 17.0702 17.5 15.9175 17.5C14.7649 17.5 13.8351 16.5252 13.8351 15.3169C13.8351 14.1085 14.7649 13.1337 15.9175 13.1337C17.0702 13.1337 18 14.1085 18 15.3169Z"
                :class="fillColor"
            />
            <path
                d="M5.08247 17.5C6.23259 17.5 7.16494 16.5226 7.16494 15.3169C7.16494 14.1111 6.23259 13.1337 5.08247 13.1337C3.93235 13.1337 3 14.1111 3 15.3169C3 16.5226 3.93235 17.5 5.08247 17.5Z"
                :class="fillColor"
            />
            <path
                d="M18 4.6912C18 5.89958 17.0702 6.87434 15.9175 6.87434C14.7649 6.87434 13.8351 5.89958 13.8351 4.6912C13.8351 3.48282 14.7649 2.5 15.9175 2.5C17.0702 2.5 18 3.47476 18 4.68314V4.6912Z"
                :class="fillColor"
            />
            <path
                d="M5.08247 6.87434C6.23259 6.87434 7.16494 5.89692 7.16494 4.6912C7.16494 3.48549 6.23259 2.50806 5.08247 2.50806C3.93235 2.50806 3 3.48549 3 4.6912C3 5.89692 3.93235 6.87434 5.08247 6.87434Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
