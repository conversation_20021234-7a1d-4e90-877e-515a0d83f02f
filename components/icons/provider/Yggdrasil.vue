<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M18.8531 10.4864C18.8125 11.1458 18.6936 11.8037 18.494 12.4468C15.3367 12.2578 12.5122 10.8319 10.4995 8.64694C8.48735 10.8314 5.66387 12.2573 2.50715 12.4468C2.31004 11.8103 2.18863 11.1519 2.14697 10.4864C3.20565 10.4483 4.22165 10.2436 5.16959 9.89764C4.32986 9.20066 3.35551 8.65964 2.29226 8.31877C2.42028 7.67971 2.62449 7.04928 2.90745 6.4412C4.4914 6.95123 5.91279 7.82245 7.0751 8.95784C7.78122 8.50877 8.42588 7.97282 8.99484 7.36474C7.04615 7.02642 5.31945 6.04191 4.04233 4.64033C4.47007 4.12064 4.95013 3.66395 5.46931 3.2733C6.22013 4.10947 7.1767 4.75768 8.25925 5.13817C7.87266 4.20091 7.60241 3.20319 7.46779 2.16331C8.09009 1.92252 8.73881 1.7564 9.3982 1.66852C9.53383 2.9563 9.91687 4.17093 10.4995 5.26263C11.0822 4.17043 11.4658 2.95529 11.6009 1.66699C12.2542 1.75335 12.9024 1.91744 13.5318 2.1628C13.3967 3.20319 13.1269 4.20141 12.7398 5.13868C13.8219 4.75869 14.7774 4.11099 15.5283 3.27533C16.0678 3.6802 16.5453 4.13893 16.9578 4.63931C15.6807 6.0414 13.9535 7.02642 12.0042 7.36474C12.5732 7.97282 13.2184 8.50877 13.924 8.95784C15.0863 7.82245 16.5077 6.95123 18.0916 6.4412C18.37 7.04267 18.5753 7.67361 18.7048 8.31928C17.6421 8.66015 16.6687 9.20117 15.8295 9.89764C16.7779 10.2436 17.7945 10.4483 18.8531 10.4864ZM11.4871 18.2746V15.4659L14.3243 11.7671C13.7147 11.4994 13.1244 11.1793 12.5656 10.8146L10.4995 13.5085L8.4335 10.8146C7.8747 11.1798 7.2844 11.4994 6.6748 11.7671L9.51199 15.4659V18.2761C10.1709 18.3538 10.8338 18.3523 11.4871 18.2746Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
