<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M12.3476 8.35736H6.15096C6.12706 8.35736 6.10521 8.37291 6.0988 8.39736L4.12581 14.8425C4.11503 14.878 4.14125 14.9158 4.17825 14.9158H7.13396C7.15785 14.9158 7.1797 14.9003 7.1864 14.8758L7.72772 13.055C7.73879 13.0172 7.71257 12.9816 7.67557 12.9816H6.37267C6.33567 12.9816 6.30741 12.9438 6.32052 12.9083L7.12318 10.3649C7.12959 10.3405 7.15144 10.3249 7.17533 10.3249H11.7104C11.7474 10.3249 11.7757 10.3627 11.7626 10.3983L10.4163 14.8803C9.85717 16.6966 8.10618 18.1817 6.32926 18.1817H3.09939C1.32218 18.1817 0.32391 16.6944 0.882829 14.8803L2.91439 8.27509C3.47348 6.45872 5.35063 5.10256 7.12755 5.10256H13.3309C13.3679 5.10256 13.3962 5.14036 13.383 5.17593L12.4 8.31956C12.3933 8.33956 12.3715 8.35736 12.3476 8.35736Z"
                :class="fillColor"
            />
            <path
                d="M17.8675 14.9219H14.8267C14.7897 14.9219 14.7614 14.8841 14.7745 14.8485L16.7516 8.38784C16.7583 8.3634 16.7799 8.34781 16.8038 8.34781H19.8206C19.8576 8.34781 19.8859 8.38562 19.8728 8.4212L17.9196 14.8818C17.9132 14.9063 17.8913 14.9219 17.8675 14.9219ZM20.8103 5.09082H17.8372C16.0078 5.09082 14.0829 6.46921 13.511 8.33892L11.575 14.7685C11.0007 16.636 11.986 18.1677 13.8154 18.1677H16.7886C18.6156 18.1677 20.632 16.6271 21.2063 14.7596L23.1161 8.35451C23.69 6.48477 22.6374 5.09082 20.8103 5.09082Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
