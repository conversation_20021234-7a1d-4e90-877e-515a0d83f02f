<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M3.65854 14.34C4.32652 15.373 5.23131 16.2317 6.29777 16.8448C7.36423 17.458 8.56158 17.8078 9.79037 17.8653C11.0192 17.9228 12.2439 17.6863 13.363 17.1754C14.4821 16.6646 15.4631 15.8941 16.2246 14.928C16.9861 13.9619 17.5062 12.8281 17.7415 11.6207C17.9769 10.4133 17.9208 9.16715 17.578 7.98576C17.2351 6.80437 16.6153 5.72184 15.77 4.82806C14.9248 3.93429 13.8786 3.25506 12.7181 2.8468L11.7981 5.46176C12.5425 5.72367 13.2137 6.1594 13.7559 6.73276C14.2982 7.30612 14.6958 8.00058 14.9157 8.75844C15.1357 9.51631 15.1717 10.3157 15.0207 11.0903C14.8697 11.8648 14.5361 12.5922 14.0475 13.212C13.559 13.8317 12.9297 14.326 12.2118 14.6537C11.4939 14.9814 10.7082 15.1331 9.91994 15.0962C9.13166 15.0593 8.36355 14.8349 7.67941 14.4416C6.99527 14.0483 6.41484 13.4974 5.98633 12.8348L3.65854 14.34Z"
                :class="fillColor"
            />
            <path
                d="M14.9129 5.94498C15.4871 5.43879 15.5485 4.55096 14.948 4.07617C14.4425 3.67641 13.8875 3.3403 13.2953 3.07682C12.2796 2.62487 11.1783 2.39727 10.0666 2.40954C8.95486 2.42182 7.85887 2.67367 6.85333 3.14793C5.84779 3.62219 4.95634 4.30771 4.23976 5.15774C3.52318 6.00777 2.99833 7.00233 2.70099 8.0736C2.40364 9.14487 2.3408 10.2677 2.51675 11.3654C2.69269 12.4632 3.10329 13.5101 3.72053 14.4348C4.33778 15.3595 5.14717 16.1402 6.09351 16.7237L7.54842 14.3641C6.94134 13.9898 6.42211 13.4889 6.02614 12.8957C5.63017 12.3026 5.36677 11.631 5.2539 10.9267C5.14103 10.2225 5.18134 9.50223 5.37209 8.815C5.56284 8.12777 5.89954 7.48975 6.35923 6.94445C6.81892 6.39915 7.39079 5.95939 8.03585 5.65514C8.68091 5.3509 9.384 5.18934 10.0972 5.18147C10.8103 5.17359 11.5168 5.3196 12.1684 5.60953C12.3961 5.71084 12.6153 5.82889 12.8241 5.96244C13.4691 6.37481 14.3386 6.45116 14.9129 5.94498Z"
                :class="fillColor"
            />
            <path
                d="M4.5345 13.095C3.85694 13.4512 3.58754 14.2995 4.05885 14.9027C4.96902 16.0675 6.20082 16.9553 7.61556 17.4465C9.46995 18.0905 11.4998 18.005 13.2935 17.2075C15.0873 16.4101 16.5105 14.9602 17.2747 13.1521C18.0389 11.3439 18.0868 9.31281 17.4087 7.47065L14.8073 8.42823C15.2423 9.61 15.2115 10.913 14.7213 12.0729C14.2311 13.2329 13.318 14.1629 12.1674 14.6745C11.0167 15.1861 9.7145 15.2409 8.52489 14.8278C7.77615 14.5678 7.10728 14.1347 6.56825 13.5715C6.03898 13.0185 5.21207 12.7388 4.5345 13.095Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
