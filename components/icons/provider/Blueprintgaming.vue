<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M18.8347 9.14832V10.9377C18.7971 10.9998 18.7349 11.0585 18.7253 11.1247C18.3933 13.4348 17.2685 15.2678 15.425 16.6845C14.207 17.6204 12.7841 18.0236 11.3194 18.3337H9.53008C9.48757 18.2982 9.4479 18.2361 9.4021 18.2312C5.26101 17.787 1.64683 13.7899 2.22698 8.99182C2.6143 5.78864 4.96037 2.91041 8.09927 1.99798C11.3736 1.04616 14.9735 2.19803 17.011 4.79994C18.0229 6.09207 18.5165 7.57758 18.8347 9.14832ZM8.67498 8.25054C8.67498 7.47987 8.67498 6.88472 8.67498 6.28962C8.67489 4.62158 8.35927 4.33136 6.67891 4.33377C6.24026 4.3344 6.15987 4.48353 6.16266 4.88133C6.18119 7.52354 6.16074 10.166 6.18397 12.8082C6.20375 15.0547 7.08001 16.1561 9.23969 16.7385C12.2879 17.5604 14.5151 16.3032 15.0952 13.4124C15.264 12.5715 15.2818 11.7146 15.138 10.8654C14.8676 9.26898 14.2608 7.84491 12.6455 7.21862C11.2044 6.65995 9.84191 6.88164 8.67498 8.25054Z"
                :class="fillColor"
            />
            <path
                d="M8.7031 11.9438C8.69165 10.8832 8.79898 9.8351 9.90934 9.32438C11.1105 8.77187 12.3253 9.53047 12.5941 10.9833C12.7165 11.6445 12.7163 12.3287 12.5697 12.9925C12.2838 14.2874 11.6079 14.8442 10.4522 14.7738C9.48048 14.7146 8.81235 13.9336 8.70627 12.7158C8.68403 12.4604 8.7031 12.2012 8.7031 11.9438Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
