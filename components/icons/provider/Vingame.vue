<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M5.96235 1.92817C5.41959 1.61512 4.74219 2.00855 4.74219 2.63888V7.54616L10.2203 4.39873L5.96235 1.92817Z"
                :class="fillColor"
            />
            <path
                d="M18.2745 9.0774L12.1484 5.51964L9.19899 7.2118L12.8216 13.659L18.2745 10.4904C18.8173 10.1773 18.8173 9.39047 18.2745 9.0774Z"
                :class="fillColor"
            />
            <path
                d="M4.74219 9.77132V16.9292C4.74219 17.5595 5.41959 17.9529 5.96235 17.6356L11.1165 14.6447L7.49808 8.18491L4.74219 9.77132Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
