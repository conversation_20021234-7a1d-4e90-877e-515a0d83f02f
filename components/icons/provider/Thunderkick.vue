<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M17.3052 13.0658L17.8164 9.49836L15.6789 5.02329L14.2268 5.5197L12.3337 3.48962L8.98855 1.66699L8.25506 4.3713L7.73642 2.95246L5.16548 5.46413L3.72813 7.9758L3.66515 10.5801L2.65381 10.0985L3.34285 14.2698L4.90986 16.9556L8.60328 18.2225L11.6299 18.3337L16.8459 16.4258L18.3462 12.7139L17.3052 13.0658ZM15.0492 11.0839L12.093 11.1654L11.9855 14.4921L11.8262 16.4629L9.24787 16.4369V14.5476L9.03672 11.2469L5.94713 11.3803L5.8397 8.18696L10.3926 8.16103L15.1047 8.02767L15.1862 8.45369L15.0492 11.0839Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
