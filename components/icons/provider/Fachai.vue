<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M21.6497 7.80655C21.5305 7.68031 21.3989 7.56709 21.2558 7.4679C20.8276 7.17133 20.3003 7 19.7061 7H12.2599L12.814 7.70736C12.8236 7.71938 12.8321 7.73341 12.8388 7.74744C12.8464 7.76347 12.8522 7.7815 12.856 7.79954V7.80254L13.2155 8.26243C13.225 8.27445 13.2336 8.28848 13.2403 8.30251C13.2479 8.31854 13.2537 8.33657 13.2575 8.35461C13.2632 8.38266 13.2651 8.41172 13.2603 8.43977L12.3038 14.1648C12.2161 14.6888 12.2466 15.1857 12.3734 15.6286C12.3801 15.6526 12.3954 15.6987 12.3954 15.6987C12.3954 15.6987 12.4889 16.0809 12.6338 16.2247C12.6681 16.2588 12.7024 16.2929 12.7387 16.3249C12.8007 16.381 12.8655 16.4331 12.9323 16.4832C12.9571 16.5013 12.9828 16.5193 13.0076 16.5373C13.0563 16.5704 13.1049 16.6015 13.1564 16.6315C13.4253 16.7898 13.7286 16.903 14.0595 16.9582C14.2045 16.9822 14.3542 16.9952 14.5087 16.9972C14.522 16.9972 17.5404 16.9982 17.5404 16.9982C18.0944 16.9982 18.6418 16.8499 19.1406 16.5894C19.2608 16.5263 19.379 16.4562 19.4935 16.38C19.5802 16.3229 19.6651 16.2618 19.7481 16.1977C19.9159 16.0674 20.0742 15.9252 20.222 15.7699C20.224 15.7679 20.2249 15.7669 20.2268 15.7649C20.7828 15.1787 21.1843 14.4283 21.3207 13.6087L21.3607 13.3682H18.8068L15.9115 16.2338L15.5091 15.6787L16.0784 12.2761H14.2159L13.8135 11.7211L18.31 7.77048L18.7124 8.32555L18.3691 10.4045H21.8547L21.9501 9.83545C22.0769 9.07499 21.9568 8.37264 21.6497 7.80755V7.80655Z"
                :class="fillColor"
            />
            <path
                d="M11.1466 7.00077H2.44822L2.57601 7.16408L2.59985 7.19514L2.99276 7.69611C3.01565 7.72616 3.03091 7.76123 3.03663 7.7973L3.3952 8.25017C3.43144 8.29626 3.4467 8.35638 3.44003 8.41549C3.44003 8.4195 3.44003 8.4225 3.44003 8.42651L2 17H5.62963L6.17226 13.7528H10.7613L11.1094 11.6687H7.0153L6.61285 11.1137L6.95522 9.08377H11.5376L11.7578 7.78327L11.1466 7.00077Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
