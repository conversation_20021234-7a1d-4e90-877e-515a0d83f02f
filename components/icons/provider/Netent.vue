<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M9.41191 10.1265C9.41191 10.2527 9.41191 10.3788 9.41191 10.505C9.41191 13.1788 9.40589 15.8557 9.42093 18.5296C9.42093 18.8557 9.32168 18.9142 9.03595 18.908C8.34418 18.8896 7.6524 18.8865 6.96063 18.908C6.66287 18.9172 6.58467 18.8403 6.58467 18.5296C6.5967 14.1634 6.59069 9.79729 6.59369 5.43115C6.59369 5.09577 6.44932 4.64654 6.65986 4.4527C6.84033 4.28654 7.26742 4.40346 7.58624 4.40039C7.99528 4.39731 8.40734 4.4127 8.81639 4.39423C9.04798 4.385 9.18032 4.46193 9.30664 4.665C11.0692 7.48345 12.8377 10.2988 14.537 13.1757C14.6543 13.0803 14.5942 12.9542 14.5942 12.8527C14.5972 10.1634 14.6002 7.4773 14.5942 4.78808C14.5942 4.49577 14.6393 4.37885 14.9611 4.39116C15.6649 4.41885 16.3717 4.4127 17.0755 4.39116C17.3703 4.38193 17.4545 4.45885 17.4545 4.77269C17.4425 8.20652 17.4455 11.6434 17.4485 15.0773C17.4485 16.2434 17.4425 17.4126 17.4545 18.5788C17.4545 18.8249 17.4064 18.9172 17.1447 18.908C16.5041 18.8865 15.8634 18.8865 15.2228 18.908C14.9731 18.9172 14.8378 18.8342 14.7025 18.6188C12.958 15.788 11.1865 12.9788 9.41191 10.1296V10.1265Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
