<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M17.2275 22.9989C16.5168 23.0111 15.8082 22.9191 15.1256 22.7269C14.582 22.5751 14.0762 22.3162 13.6403 21.9668C13.2661 21.661 12.9665 21.2784 12.764 20.8464C12.5673 20.4158 12.4675 19.9493 12.4719 19.4783C12.4608 18.9295 12.616 18.3897 12.9184 17.9257C13.1991 17.4932 13.5869 17.1356 14.0465 16.8854C13.6815 16.6546 13.383 16.338 13.1785 15.9651C12.9707 15.5661 12.8675 15.1239 12.8781 14.6771C12.8703 14.1057 13.0336 13.5446 13.3482 13.0614C13.6987 12.551 14.195 12.1506 14.7767 11.9092C15.4149 11.6207 16.2319 11.4762 17.2275 11.4762C18.2232 11.4762 19.0235 11.6175 19.6622 11.9003C20.2428 12.136 20.7372 12.5343 21.0824 13.0444C21.3923 13.5295 21.5525 14.0903 21.5447 14.6609C21.5525 15.1066 21.4527 15.5477 21.2526 15.9492C21.059 16.3265 20.7683 16.6489 20.4086 16.8854C20.8854 17.1208 21.2846 17.481 21.5612 17.9254C21.8572 18.3976 22.009 18.9414 21.9996 19.4936C22.0043 20.1178 21.821 20.7297 21.4719 21.2541C21.083 21.8182 20.533 22.2601 19.8895 22.5263C19.186 22.8407 18.2986 22.9981 17.2275 22.9984M17.2273 20.9659C17.5483 20.971 17.8668 20.9082 18.1606 20.782C18.4224 20.6703 18.6491 20.4937 18.8179 20.2699C18.9829 20.0432 19.068 19.7711 19.0614 19.4936C19.0722 19.2084 18.9868 18.9274 18.8179 18.6933C18.648 18.4744 18.4212 18.3032 18.1606 18.1972C17.8654 18.0761 17.5477 18.0163 17.2273 18.0211C16.9043 18.0168 16.5838 18.0767 16.2859 18.1972C16.0222 18.3016 15.7924 18.4728 15.6207 18.6931C15.4519 18.9271 15.3665 19.2081 15.3773 19.4934C15.3704 19.7708 15.4558 20.0432 15.6207 20.2699C15.7913 20.4953 16.021 20.672 16.2862 20.782C16.5827 20.9076 16.9035 20.9704 17.2273 20.9659ZM17.2275 16.0851C17.5127 16.0886 17.7953 16.0315 18.0552 15.9171C18.286 15.8187 18.4872 15.6645 18.6391 15.469C18.7818 15.2757 18.8558 15.0424 18.8499 14.8049C18.8552 14.566 18.7843 14.3314 18.6471 14.1327C18.5028 13.9337 18.303 13.7781 18.071 13.6845C17.5299 13.4712 16.924 13.4712 16.383 13.6845C16.151 13.7781 15.9512 13.9334 15.8068 14.1327C15.6697 14.3309 15.5988 14.5654 15.604 14.8041C15.5988 15.043 15.6697 15.2778 15.8071 15.4763C15.9512 15.6755 16.1512 15.8311 16.3832 15.9244C16.6497 16.0347 16.9374 16.0892 17.2273 16.0843"
                :class="fillColor"
            />
            <path
                d="M3.92864 21.098C3.73781 21.29 3.6265 21.5545 3.48561 21.7746C3.14922 22.4079 3.06513 22.8319 3.06513 22.9648L4.4869 22.9996C6.57374 23.0004 8.66059 23.0001 10.7472 22.9985C10.8858 22.9982 11.0245 22.9812 11.1492 22.9728V20.8233H7.29661C7.90341 20.2405 8.47394 19.7438 8.98059 19.1921C9.51792 18.6072 10.0622 18.0107 10.4824 17.3471C11.1093 16.4006 11.2412 15.2238 10.8389 14.1689C10.2617 12.5915 8.55708 11.6597 6.76011 11.9158C5.15285 12.1448 4.10245 13.0401 3.46496 14.4698L5.2505 15.6778C5.40227 15.4136 5.52781 15.187 5.66089 14.965C5.91226 14.5274 6.34553 14.2156 6.85022 14.1086C7.96786 13.8525 8.84779 14.6489 8.65864 15.743C8.54007 16.4276 8.10038 16.9347 7.63084 17.4087C6.40551 18.6464 5.15452 19.8606 3.92864 21.098Z"
                :class="fillColor"
            />
            <path
                d="M4.29352 6.18671C4.53527 7.22585 4.95007 7.96481 5.72151 8.49576C6.99061 9.36905 8.60143 9.302 9.7141 8.24936C9.95658 8.02002 10.0414 7.63383 10.2677 7.18202H8.12772V5.37241C9.40391 5.37241 10.6723 5.36293 11.9399 5.39113C12.0365 5.39326 12.2034 5.65885 12.2076 5.80527C12.233 6.69562 12.2384 7.58716 12.2076 8.47728C12.1863 8.71088 12.0895 8.93217 11.9306 9.10915C10.4481 10.7728 8.55304 11.3604 6.37341 10.8974C4.56387 10.5127 3.51744 8.17354 4.29401 6.18647"
                :class="fillColor"
            />
            <path
                d="M7.53629 1.0002C6.36765 0.990724 5.22198 1.31602 4.24302 1.93462C2.84926 2.85886 2.01134 4.3863 2.00009 6.02296V6.02746C1.99496 6.79769 2.20322 7.55513 2.60287 8.22089C2.72704 7.60299 2.79793 7.02561 2.96145 6.4743C3.87075 3.40354 6.69812 2.22959 9.58001 3.70159C9.71176 3.77006 9.86722 3.78167 10.0083 3.73381C10.5888 3.42321 11.1515 3.08204 11.7259 2.74774C10.8058 1.60601 9.20407 1.00447 7.57002 1.0002H7.53629Z"
                :class="fillColor"
            />
            <path
                d="M19.6983 10.7088C19.777 10.9175 19.8772 10.9924 20.1072 10.9884L21.9189 10.9524L21.5132 9.72414C20.4037 6.90571 19.2982 4.0861 18.1963 1.2653C18.1222 1.07576 18.0303 0.990704 17.8069 1.00137C17.4036 1.02198 16.9993 1.02198 16.5957 1.00137C16.3601 0.989519 16.2708 1.08026 16.1921 1.28236C14.9705 4.41021 13.748 7.53782 12.5251 10.665C12.4902 10.755 12.4677 10.8498 12.4266 10.9829C13.0793 10.9829 13.6833 10.9722 14.2865 10.9884C14.529 10.9948 14.6307 10.9161 14.7138 10.6922C15.483 8.61629 16.2684 6.54606 17.0481 4.47441C17.0873 4.37135 17.13 4.26947 17.1919 4.11476C17.2508 4.25929 17.2892 4.34861 17.3236 4.43911C18.1171 6.52829 18.9137 8.61629 19.6983 10.7088Z"
                :class="fillColor"
            />
            <path d="M15.6627 8.58006L17.2109 10.9045L18.745 8.58006H15.6627Z" :class="fillColor" />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
