<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M5.42446 5.81836L6.80014 7.4784L6.68102 7.89019L6.02471 10.1958L4.8172 14.3845C4.66734 14.8198 4.36417 15.197 3.95536 15.4569C3.64403 15.6468 3.26678 15.7232 2.89733 15.6713C2.59679 15.646 2.30181 15.581 2.02147 15.4783L0.727539 17.0311C1.57878 17.427 2.54972 17.55 3.4859 17.3807C4.05588 17.2999 4.60052 17.1083 5.08346 16.8188C5.93282 16.2918 6.55105 15.5052 6.82816 14.599L7.23223 13.207L9.33428 5.81836H5.42446Z"
                :class="fillColor"
            />
            <path
                d="M23.9857 8.51495C24.025 8.17314 23.9846 7.82754 23.867 7.50127C23.7494 7.175 23.5575 6.87559 23.304 6.62306C22.9503 6.25709 22.2686 5.81836 21.0378 5.81836H9.43227L10.812 7.47912L8.64585 14.8477H19.6325C20.4857 14.844 21.3173 14.5998 22.015 14.1478C22.3786 13.9305 22.6876 13.6444 22.9215 13.3086C23.1554 12.9728 23.3088 12.595 23.3714 12.2003C23.4277 11.8746 23.4094 11.5417 23.3176 11.2229C23.2259 10.9041 23.0627 10.6065 22.8386 10.3491C23.4156 9.86539 23.8973 9.21692 23.9857 8.51495ZM16.4775 10.1907C16.2649 10.9349 15.8362 11.6118 15.2373 12.1489C14.4393 12.8423 13.2155 13.1955 11.9032 13.1955H11.0772L12.7501 7.47912H14.0275C15.121 7.47912 15.8725 7.7231 16.2634 8.20035C16.7892 8.8424 16.5868 9.80975 16.4775 10.195V10.1907ZM21.5031 12.1703C21.4007 12.699 20.605 13.1998 19.7511 13.1998H16.5333C17.1591 12.6195 17.6492 11.9276 17.9735 11.1666H20.3374C20.8167 11.1666 21.1215 11.2244 21.3472 11.4748C21.4226 11.5744 21.4752 11.687 21.502 11.8058C21.5288 11.9246 21.5292 12.0471 21.5031 12.1661V12.1703ZM22.0778 8.47429C22.0033 9.08637 21.4031 9.51227 20.6213 9.51227H18.3993C18.4787 8.81171 18.3339 8.10508 17.9828 7.47912H20.8749C21.3542 7.47912 21.6916 7.59469 21.8847 7.79372C21.9677 7.88711 22.0279 7.99584 22.0613 8.11253C22.0946 8.22922 22.1002 8.35114 22.0778 8.47001V8.47429Z"
                :class="fillColor"
            />
            <path
                d="M12.7429 7.47159H10.815L9.78221 11.0006L11.5724 11.414L12.7429 7.47159Z"
                :class="fillColor"
            />
            <path
                d="M16.3576 5.84511C16.3576 5.84511 17.3904 6.26347 17.9757 7.47162C19.215 7.47162 21.9167 7.54901 21.9167 7.54901L21.8857 5.84512L16.3576 5.84511Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
