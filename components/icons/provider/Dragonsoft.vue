<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12.764 12.8866C13.0763 13.061 12.7847 15.1224 10.9511 15.955C8.65913 16.9957 6.04507 16.3019 5.25218 16.0665C6.10701 16.9957 7.69276 18.3337 10.4927 18.3337C13.2926 18.3337 14.5067 16.1284 14.866 14.7533C15.2252 13.3781 14.6801 11.272 12.6359 9.84724C13.4908 10.1694 15.2252 11.272 15.4358 13.6135C15.6043 15.4867 14.5645 16.9957 14.0235 17.516C15.0229 17.1154 17.2372 15.727 18.0995 13.3781C19.1773 10.4419 19.1196 7.77325 17.4206 5.35094C15.7216 2.92862 13.1815 1.66699 10.5069 1.66699C7.83224 1.66699 2.18018 3.55102 2.18018 10.0273C2.20188 9.97726 2.23623 9.87449 2.28487 9.72896C2.72634 8.40815 4.34505 3.56526 8.37055 2.62583C12.8396 1.58289 15.7997 3.87063 16.9496 5.85558C18.0995 7.84053 18.0995 8.84983 18.0995 10.532C18.0532 10.4725 18.0098 10.4186 17.9692 10.3681C17.6652 9.98991 17.5184 9.8073 17.4918 8.98908C17.4677 8.24711 17.5773 7.83038 17.6351 7.71476C17.4164 7.94097 16.8117 8.40696 16.1421 8.46125C15.4725 8.51554 15.1292 8.43361 15.0412 8.38585C15.5615 8.4501 16.78 8.3063 17.4918 7.21709C17.434 7.21379 17.3751 7.21087 17.3152 7.20791C16.2814 7.15677 14.9842 7.09259 15.0412 4.81171C15.0178 4.86699 14.9934 4.92815 14.9671 4.99383C14.6893 5.68971 14.2091 6.89247 12.583 6.96826C12.6544 6.93088 12.7263 6.89506 12.7984 6.85916C13.3804 6.56916 13.9725 6.27412 14.4153 5.11333C14.2293 5.15985 14.0639 5.20354 13.9153 5.24281C13.1244 5.45183 12.8063 5.53589 12.3493 5.25659C12.0335 5.06359 11.9116 4.67412 11.8025 4.32566C11.7241 4.0751 11.6523 3.84575 11.5198 3.7259C11.51 3.79883 11.5008 3.87406 11.4913 3.95101C11.3842 4.82087 11.2499 5.91211 9.98913 6.41781C10.0252 6.37289 10.061 6.32878 10.0964 6.28514C10.6021 5.66215 11.0298 5.13525 10.9241 3.7259C10.4416 5.06808 10.1173 5.58837 9.03904 6.19914C9.4236 5.30938 9.60457 4.28389 8.96364 2.85875C8.6017 4.72123 8.37549 5.43756 7.73456 5.96539C7.74062 5.88408 7.74698 5.80404 7.75325 5.72526C7.82479 4.82504 7.88328 4.08904 7.34246 3.49969C7.16903 5.18119 6.85233 6.47059 6.18124 7.3679C5.86139 7.79557 5.60662 7.9389 5.39001 8.06076C5.15214 8.19459 4.96027 8.30254 4.77873 8.73271C4.57703 9.21065 4.45947 9.61464 4.3712 9.91801C4.30767 10.1363 4.2593 10.3025 4.20566 10.4067C4.07747 10.6555 3.02936 11.877 2.90871 11.9072C2.78807 11.9374 2.61464 11.9072 2.54678 11.5754C2.36581 11.8393 2.02649 12.518 2.74283 13.3248C3.31589 13.9703 4.86167 14.0261 5.56293 13.9733C5.25126 13.8401 4.6988 13.4771 4.98232 13.091C5.20823 12.7834 6.12048 12.5064 6.86749 12.2796C7.29234 12.1506 7.66375 12.0379 7.82504 11.9449C8.26992 11.6885 8.29254 11.6659 8.39057 11.2512C8.48859 10.8365 9.33432 10.0975 10.9511 11.862C11.0019 11.5664 10.7633 10.849 10.6376 10.5273C10.9895 10.5902 11.7687 10.8606 12.0703 11.4397C12.4473 12.1636 12.1457 13.3097 11.6556 14.1392C11.2635 14.8027 11.1152 15.044 11.09 15.0817C11.5173 14.8951 12.4503 14.1949 12.764 12.8866ZM6.52475 8.82438C6.37422 8.90444 6.04528 9.08184 5.93386 9.15101C5.79458 9.23747 5.5544 9.5497 5.57842 9.94358C5.64104 9.91695 5.69643 9.8941 5.74608 9.87361C6.02778 9.75737 6.12468 9.71738 6.30852 9.49686C6.48151 9.28935 6.52475 8.96208 6.52475 8.82438Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
