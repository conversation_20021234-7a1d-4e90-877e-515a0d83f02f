<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M16.9734 14.6587C17.1456 15.1857 17.0701 15.7347 16.8103 16.1702L16.771 16.2302L16.765 16.2428C16.5716 16.5363 16.2906 16.7666 15.9462 16.8865C15.8827 16.9118 15.8162 16.9275 15.7498 16.9433C15.6682 16.9528 15.5836 16.9591 15.499 16.9591C15.2059 16.9591 14.9038 16.896 14.6288 16.7635C14.5745 16.7382 14.5201 16.7098 14.4657 16.6783L8.28396 13.043L6.90319 12.2289C6.57084 12.0301 6.1841 11.967 5.83363 12.0238C5.48013 12.0774 5.16288 12.2541 4.96045 12.535L5.68256 11.5315C5.68558 11.5252 5.6886 11.522 5.69464 11.5157C6.44999 10.4838 7.83075 10.0263 9.17526 10.2566C9.65868 10.3324 10.133 10.5028 10.5772 10.7615L12.4867 11.8849L12.6257 11.967L16.4386 14.2705C16.7166 14.4346 16.6411 13.7215 16.6229 13.5542L16.8465 14.2579L16.9734 14.6587Z"
                :class="fillColor"
            />
            <path
                d="M16.6225 13.5524C16.6407 13.7197 16.7162 14.4328 16.4382 14.2687L12.6253 11.9652L12.4228 11.3246L10.6372 5.68556L11.1176 4.18035C11.178 3.99417 11.2566 3.8143 11.3623 3.65021C11.5013 3.43878 11.6554 3.28416 11.8699 3.17687C12.166 3.0254 12.9093 3.23998 13.1721 3.38198C13.1752 3.38514 13.1812 3.38829 13.1842 3.39145C13.4018 3.6155 13.577 3.89319 13.6828 4.21821L16.6165 13.5177C16.6165 13.5177 16.6165 13.5209 16.6195 13.5272L16.6225 13.543C16.6225 13.543 16.6195 13.543 16.6225 13.5461C16.6205 13.5482 16.6205 13.5503 16.6225 13.5524Z"
                :class="fillColor"
            />
            <path
                d="M13.2091 3.40464C13.203 3.40148 13.194 3.39517 13.1849 3.39202C13.1819 3.38886 13.1758 3.38571 13.1728 3.38255C12.91 3.24055 12.1667 3.02597 11.8706 3.17744C11.6561 3.28473 11.502 3.43935 11.363 3.65077C11.2573 3.81486 11.1787 3.99473 11.1183 4.18091L10.6379 5.68613L9.17554 10.2554C7.83103 10.0251 6.45026 10.4826 5.69492 11.5145C5.68888 11.5208 5.68586 11.524 5.68283 11.5303L4.96073 12.5337L4.90332 12.5148L7.33553 4.82465C7.93074 3.04806 9.68313 2.11716 11.4174 2.51476C11.5292 2.54001 12.7166 2.84295 13.2091 3.40464Z"
                :class="fillColor"
            />
            <path
                d="M6.90265 12.227L8.28342 13.0412L7.4042 15.7928C7.09602 16.708 6.13824 17.1908 5.26204 16.8689C4.38585 16.5502 3.92358 15.5467 4.23176 14.6316L4.9025 12.5142L4.95991 12.5331C5.16234 12.2523 5.47958 12.0756 5.83308 12.0219C6.18356 11.9651 6.5703 12.0282 6.90265 12.227Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
