<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M19.4318 8C18.898 8 18.3883 8.11399 17.9264 8.31882C17.9264 8.31882 17.1121 8.65012 16.5852 9.52287C16.5852 9.52287 16.5869 9.52287 16.5886 9.52287C16.2909 9.99131 16.1199 10.5399 16.1199 11.1259C16.1199 11.8099 16.3542 12.4422 16.7494 12.9569C17.3464 13.7317 18.313 14.2358 19.4044 14.2358C20.5574 14.2358 21.5719 13.6729 22.1586 12.8215C21.8165 14.0523 20.7268 14.9536 19.4335 14.9536C19.4335 14.9536 18.2086 15.0604 16.0788 13.9508C15.9026 13.8439 15.699 13.7228 15.4835 13.5963C15.6871 13.1083 15.8017 12.5686 15.8017 12.0022C15.8 9.79004 14.0807 8 11.9595 8C9.83821 8 8.11896 9.79004 8.11896 11.9987C8.11896 12.5953 8.24555 13.1617 8.46965 13.6694C7.5818 14.5136 6.51091 14.8004 5.84032 14.8966C5.44515 14.9536 5.18854 14.9447 5.18854 14.9447V14.9482C3.67116 14.8912 2.45828 13.5928 2.45828 11.9987C2.45828 10.3689 3.72932 9.04553 5.29461 9.04553C6.31931 9.04553 7.21572 9.61015 7.71524 10.458L8.19765 9.3786C7.49285 8.53434 6.45446 8 5.29461 8C3.17335 8 1.4541 9.79004 1.4541 11.9987C1.4541 14.2073 3.17335 15.9973 5.29461 15.9973C5.41265 15.9973 5.53068 15.992 5.64701 15.9813C7.41929 15.8459 8.82035 14.7487 9.02563 14.5795C9.73043 15.4469 10.7825 15.9973 11.9595 15.9973C13.1638 15.9973 14.2381 15.4202 14.9412 14.519C15.6888 15.023 17.473 16.0596 19.4318 15.9973C21.553 15.9973 23.2723 14.2073 23.2723 11.9987C23.2723 9.79004 21.553 8 19.4318 8ZM19.4027 13.2526C18.1607 13.2526 17.1548 12.2997 17.1548 11.1241C17.1548 10.4117 17.5244 9.78292 18.0906 9.39641C18.4601 9.14527 18.9134 8.99566 19.4027 8.99566C19.7842 8.99566 20.1451 9.08649 20.4599 9.24502C21.1681 9.60302 21.6505 10.3101 21.6505 11.1241C21.6505 12.2997 20.6447 13.2526 19.4027 13.2526ZM11.9595 14.9518C10.3925 14.9518 9.12314 13.6302 9.12314 11.9987C9.12314 10.3671 10.3925 9.04553 11.9595 9.04553C13.5265 9.04553 14.7958 10.3689 14.7958 11.9987C14.7958 12.3834 14.7257 12.7521 14.5956 13.0887C14.0978 12.818 13.6394 12.5936 13.3947 12.5366C13.3947 12.5366 11.3197 11.9523 9.94085 12.8073L10.4729 13.6355C10.4729 13.6355 12.0946 12.8679 14.0841 13.9543C13.5658 14.5653 12.8063 14.9518 11.9595 14.9518Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
