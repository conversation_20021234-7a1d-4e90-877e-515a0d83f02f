<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="21"
            height="20"
            viewBox="0 0 21 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M1.3335 11.5519C1.3716 11.1799 1.42329 10.8103 1.5733 10.4634C2.10194 9.24888 3.03611 8.59676 4.36489 8.51307C5.79812 8.42277 7.07628 9.25232 7.55456 10.6717C7.65453 10.977 7.7084 11.2954 7.71443 11.6163C7.73574 12.5047 7.72429 13.394 7.72082 14.2829C7.71896 14.7531 7.36724 15.0309 6.94279 14.906C6.82206 14.8732 6.71582 14.8014 6.64091 14.7021C6.566 14.6027 6.52671 14.4814 6.52926 14.3574C6.52926 14.2275 6.52926 14.0973 6.52926 13.9434C6.42268 14.0474 6.33742 14.134 6.24842 14.2166C5.60761 14.8112 4.83944 15.0377 3.98067 14.9414C2.62977 14.7898 1.5781 13.7435 1.38679 12.3915C1.36734 12.2529 1.35082 12.1135 1.3335 11.9743V11.5519ZM6.58015 11.6248C6.54365 11.4584 6.51114 11.1617 6.41256 10.8884C6.16422 10.202 5.67049 9.74999 4.94335 9.6127C4.20129 9.47224 3.5405 9.66075 3.03371 10.2463C2.53731 10.8193 2.40222 11.4872 2.53278 12.2204C2.77232 13.5687 4.25272 14.3141 5.46346 13.6778C6.2311 13.2733 6.54232 12.5874 6.58015 11.6248Z"
                :class="fillColor"
            />
            <path
                d="M15.9038 11.1039C16.0316 10.9901 16.1436 10.8927 16.2525 10.7931C17.0572 10.0688 17.8615 9.34437 18.6655 8.61973C18.8224 8.47769 18.9986 8.41353 19.2024 8.48218C19.419 8.55557 19.5618 8.70659 19.5981 8.93682C19.6284 9.12955 19.5602 9.28981 19.4142 9.42023C18.7454 10.018 18.0798 10.6191 17.4068 11.2124C17.3117 11.2963 17.3194 11.3412 17.3924 11.4307C18.1067 12.3062 18.8183 13.1839 19.5272 14.0638C19.6956 14.2721 19.7102 14.4888 19.5789 14.7117C19.374 15.0596 18.9034 15.1014 18.6492 14.7872C17.9552 13.9295 17.2636 13.0699 16.5744 12.2085C16.4596 12.066 16.4588 12.0665 16.3226 12.1927C16.2222 12.2856 16.1204 12.3775 16.0135 12.4633C15.9267 12.533 15.8926 12.6146 15.8934 12.7257C15.8976 13.258 15.8976 13.7902 15.8934 14.3225C15.8912 14.6956 15.6703 14.9192 15.3144 14.9181C14.968 14.9181 14.7495 14.7027 14.749 14.3568C14.749 13.3536 14.749 12.3503 14.749 11.347C14.7466 10.1061 14.7402 8.86685 14.7364 7.62676C14.7351 7.22704 14.7431 6.82732 14.7346 6.42759C14.722 5.85098 15.2954 5.7327 15.6208 5.90695C15.8254 6.01678 15.8947 6.19499 15.8947 6.41149C15.8947 7.04513 15.8947 7.67877 15.8947 8.31241C15.8947 9.2011 15.8947 10.09 15.8947 10.979C15.8952 11.0109 15.8998 11.0447 15.9038 11.1039Z"
                :class="fillColor"
            />
            <path
                d="M11.3864 14.9628C10.6062 14.9628 9.93129 14.8175 9.30806 14.4759C9.12377 14.371 8.95244 14.2452 8.79754 14.101C8.57106 13.8956 8.55508 13.5907 8.7344 13.3633C8.94756 13.0938 9.28461 13.0156 9.54867 13.1785C9.61123 13.22 9.67038 13.2664 9.72559 13.3171C10.445 13.9413 11.2659 14.0397 12.1516 13.7591C12.2683 13.7167 12.3746 13.6502 12.4634 13.564C12.7434 13.3055 12.7466 12.9216 12.4799 12.6489C12.2819 12.4461 12.0282 12.3382 11.7605 12.2685C11.3533 12.1628 10.9403 12.0781 10.5335 11.9706C10.1948 11.8796 9.87285 11.736 9.57957 11.545C8.62462 10.9328 8.66032 9.54036 9.58757 8.95503C9.96718 8.71605 10.3985 8.56957 10.8463 8.52759C11.5049 8.46053 12.1495 8.52759 12.7562 8.81141C13.0722 8.95978 13.3549 9.15463 13.5654 9.43977C13.7319 9.66471 13.7519 9.85322 13.632 10.0357C13.5073 10.2247 13.2726 10.3163 13.0208 10.2426C12.8841 10.2028 12.7391 10.1394 12.6371 10.0449C12.1817 9.62432 11.635 9.51924 11.0445 9.56967C10.8378 9.58911 10.6345 9.63508 10.4397 9.70643C10.2121 9.78854 10.0475 9.94721 10.0336 10.211C10.0203 10.4702 10.1271 10.6841 10.3605 10.7826C10.6885 10.9212 11.0333 11.0231 11.3754 11.1258C11.8071 11.2562 12.2513 11.3505 12.6752 11.5015C13.0991 11.6525 13.4537 11.9073 13.6677 12.316C14.0607 13.066 13.7658 14.0638 13.0184 14.5393C12.4977 14.8725 11.9195 14.9649 11.3864 14.9628Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
