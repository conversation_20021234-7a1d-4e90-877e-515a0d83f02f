<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M3.58428 12.133L1.44643 12.1402L2.01094 13.7164L2.75499 13.6863L1.44923 20.9921H4.34161L5.24705 15.932L4.14249 15.3713L3.58428 12.133Z"
                :class="fillColor"
            />
            <path
                d="M21.1656 16.8895C21.256 16.3777 21.0589 16.1213 20.5746 16.1204C21.0608 16.1204 21.3514 15.8509 21.4465 15.3119L21.735 13.7257H21.7643L24 12.1402H8.83946C8.18227 12.1402 7.65364 12.2491 7.25355 12.4668L8.00249 17.3292L5.92193 16.2743L5.3756 19.3988C5.1837 20.4667 5.8041 21.0005 7.23678 21H10.1655C11.5739 20.9952 12.3746 20.4799 12.5674 19.4539L13.0069 16.8816C13.2043 15.8246 12.5816 15.2906 11.1387 15.2797H9.01133L9.2852 13.7257H14.525C14.5248 13.7309 14.5248 13.7362 14.525 13.7414L14.2456 15.3119C14.1524 15.8246 14.3543 16.0889 14.8513 16.1046C14.3417 16.1099 14.0485 16.361 13.9717 16.858L13.5385 19.3988C13.3466 20.4667 13.967 21.0005 15.3997 21H21.5478L23.3684 19.4202H20.7318L21.1656 16.8895ZM10.1438 16.8737L9.70368 19.4202H8.254L8.71511 16.8637L10.1438 16.8737ZM17.8624 19.4202H16.412L16.8738 16.8637L18.3019 16.8716L17.8624 19.4202ZM16.6622 14.8922C17.9623 13.4049 19.0948 13.803 19.0948 13.803C18.5499 16.5121 16.6622 14.8887 16.6622 14.8887V14.8922Z"
                :class="fillColor"
            />
            <path
                d="M2.35676 4.62886H2.40008C2.42802 4.62886 2.45527 4.62886 2.48252 4.63316L2.52444 4.63746C2.55099 4.63746 2.57684 4.64319 2.60199 4.64748L2.63552 4.6525C2.67045 4.65822 2.70539 4.66539 2.73613 4.67326L3.67091 9.93457H2.61177L1.45971 11.4055H3.9343L4.56308 14.9445L7.40445 16.4741L6.51927 11.4055H16.5099C17.8205 11.4017 18.5657 10.9223 18.7455 9.96751L19.1549 7.57139C19.2448 7.09397 18.647 6.72494 17.3615 6.46427L15.7134 6.06539L15.9691 4.63316H20.6563C20.7347 4.62405 20.8124 4.6097 20.8889 4.59019C20.9842 4.56547 21.0776 4.53364 21.1684 4.49495C21.2103 4.47776 21.2522 4.45843 21.2935 4.43766C21.3347 4.41689 21.371 4.39827 21.4094 4.37607C21.7761 4.16456 22.0889 3.86746 22.3226 3.50886C22.5083 3.22876 22.6391 2.91426 22.7075 2.58292C22.7464 2.39132 22.7621 2.19554 22.7543 2C22.1216 2.64473 21.2948 3.05129 20.4083 3.15366H19.4246C19.3897 3.15366 19.3547 3.15366 19.3212 3.15366C19.2877 3.15366 19.2555 3.15366 19.2227 3.15366H15.7414C15.2858 3.15366 14.917 3.20021 14.6172 3.30548C14.5384 3.33287 14.4615 3.36614 14.3874 3.40502C14.3636 3.41719 14.3406 3.4308 14.3175 3.44441C13.9305 3.675 13.695 4.05597 13.549 4.6303L13.1389 6.86816C13.0537 7.32576 13.519 7.6874 14.8156 7.95379L16.3436 8.35983L16.0683 9.93529H9.94262L9.83503 9.13109H11.3469L12.5129 6.1456L13.6811 3.16441L9.81896 3.17228L9.38371 4.62313L10.7265 4.63817L9.41514 7.83563L8.66271 3.13146H6.10987L6.49971 5.80473L5.76753 7.83563L5.0151 3.13146H2.47274H2.46645V3.13719L1.01118 4.84155C1.00774 4.84563 1.00492 4.85022 1.00279 4.85516C1.00247 4.85657 1.00247 4.85804 1.00279 4.85945C1.00214 4.86276 1.00214 4.86617 1.00279 4.86948V4.87449L1 4.88308C1 4.88308 1 4.88308 1 4.8881L1.00419 4.89597V4.90027C1.00592 4.90277 1.00805 4.90495 1.01048 4.90672C1.01405 4.90955 1.01806 4.91173 1.02236 4.91316H1.03493C1.04068 4.91451 1.04665 4.91451 1.0524 4.91316C1.4213 4.74858 1.81597 4.65303 2.21773 4.63101C2.27153 4.62886 2.31484 4.62886 2.35676 4.62886ZM7.16272 9.14684L7.30245 9.93457H6.26078L6.12105 9.13825L7.16272 9.14684Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
