<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M20.3638 4.36328L4.36377 8.32913V11.7043L18.6616 8.32913L20.3638 4.36328Z"
                :class="fillColor"
            />
            <path
                d="M4.36377 12.5481V15.6702L16.1936 14.1513L17.4702 10.6918L4.36377 12.5481Z"
                :class="fillColor"
            />
            <path d="M4.36377 16.4296V19.636H13.8957L15.0872 16.4296H4.36377Z" :class="fillColor" />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
