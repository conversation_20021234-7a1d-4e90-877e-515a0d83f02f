<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M6.66699 25.1213V22.4378H17.3337V25.1213H6.66699Z" :class="fillColor" />
            <path
                d="M13.1575 6.5842C13.0928 5.97642 13.0712 5.4341 12.9694 4.91048C12.8151 4.12038 12.3107 3.76974 11.4624 3.78065C10.6665 3.79156 10.1991 4.14999 10.0279 4.9136C9.67162 6.5016 10.3457 7.74831 11.3776 8.84074C12.4048 9.9285 13.5447 10.9165 14.5149 12.0526C15.9879 13.7777 16.3458 15.8426 15.8614 18.0306C15.32 20.471 12.9724 21.7925 10.358 21.205C8.12147 20.7016 6.87364 19.2087 6.77029 16.8867C6.74561 16.3179 6.76721 15.7475 6.76721 15.1164H9.86134C9.9107 15.8317 9.91378 16.5298 10.0202 17.2109C10.1575 18.0867 10.6156 18.4544 11.4161 18.4591C12.2182 18.4638 12.6516 18.0789 12.8413 17.2311C13.2362 15.4764 12.2737 14.2546 11.2249 13.0936C10.2732 12.0401 9.18575 11.1035 8.2896 10.0049C6.86438 8.25791 6.46181 6.232 7.06336 4.02532C7.72506 1.59891 9.89373 0.702833 12.0038 0.906982C15.3154 1.22801 15.9524 3.58274 16.008 6.06213C16.011 6.232 15.718 6.54991 15.5483 6.56082C14.7601 6.61536 13.9658 6.5842 13.1575 6.5842Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
