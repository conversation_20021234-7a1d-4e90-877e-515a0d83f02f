<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M12 2.75C10.0716 2.75 8.18657 3.32183 6.58319 4.39317C4.97982 5.46451 3.73013 6.98726 2.99218 8.76884C2.25422 10.5504 2.06114 12.5108 2.43735 14.4021C2.81355 16.2934 3.74215 18.0307 5.10571 19.3943C6.46928 20.7579 8.20656 21.6865 10.0979 22.0627C11.9892 22.4389 13.9496 22.2458 15.7312 21.5078C17.5127 20.7699 19.0355 19.5202 20.1068 17.9168C21.1782 16.3134 21.75 14.4284 21.75 12.5C21.7473 9.91498 20.7192 7.43661 18.8913 5.60872C17.0634 3.78084 14.585 2.75273 12 2.75ZM12 20.75C10.3683 20.75 8.77326 20.2661 7.41655 19.3596C6.05984 18.4531 5.00242 17.1646 4.378 15.6571C3.75358 14.1496 3.5902 12.4908 3.90853 10.8905C4.22685 9.29016 5.01259 7.82015 6.16637 6.66637C7.32016 5.51259 8.79017 4.72685 10.3905 4.40852C11.9909 4.09019 13.6497 4.25357 15.1571 4.87799C16.6646 5.50242 17.9531 6.55984 18.8596 7.91655C19.7661 9.27325 20.25 10.8683 20.25 12.5C20.2475 14.6873 19.3775 16.7843 17.8309 18.3309C16.2843 19.8775 14.1873 20.7475 12 20.75ZM18 12.5C18 12.6989 17.921 12.8897 17.7803 13.0303C17.6397 13.171 17.4489 13.25 17.25 13.25H12C11.8011 13.25 11.6103 13.171 11.4697 13.0303C11.329 12.8897 11.25 12.6989 11.25 12.5V7.25C11.25 7.05109 11.329 6.86032 11.4697 6.71967C11.6103 6.57902 11.8011 6.5 12 6.5C12.1989 6.5 12.3897 6.57902 12.5303 6.71967C12.671 6.86032 12.75 7.05109 12.75 7.25V11.75H17.25C17.4489 11.75 17.6397 11.829 17.7803 11.9697C17.921 12.1103 18 12.3011 18 12.5Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
