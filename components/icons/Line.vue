<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M2.00391 10.6184C2.00391 14.8653 5.52692 18.4392 10.332 19.1081C10.6544 19.1789 10.7277 19.2522 10.7277 19.2522C10.7869 19.3864 10.6361 20.0278 10.5711 20.6459C10.407 21.7414 11.1051 22.3137 12.2639 21.8047C13.3602 21.3241 17.8396 18.355 19.8547 15.9225C22.0665 13.3767 22.6005 10.3052 21.3201 7.49441C17.2714 -1.38436 2.00391 1.17564 2.00391 10.6184ZM18.9017 15.1136C17.0673 17.3279 12.9437 20.0995 11.8307 20.6285C11.974 19.4788 12.3963 18.2826 10.5528 17.8785C6.32333 17.2887 3.2535 14.2355 3.2535 10.6184C3.2535 2.52187 16.6891 0.345912 20.1821 8.01257C21.251 10.3585 20.7886 12.9426 18.9017 15.1136Z"
                :class="fillColor"
            />
            <path
                d="M7.77478 11.7839H6.77011V8.33416C6.77011 7.98927 6.4902 7.70936 6.14531 7.70936C5.80043 7.70936 5.52052 7.98927 5.52052 8.33416V12.4095C5.52052 12.7544 5.80043 13.0343 6.14531 13.0343H7.77478C8.60118 13.0335 8.60118 11.7839 7.77478 11.7839Z"
                :class="fillColor"
            />
            <path
                d="M9.16867 8.33411C9.35528 11.4731 8.70965 13.0342 9.79347 13.0342C10.1384 13.0342 10.4183 12.7543 10.4183 12.4094V8.33411C10.4183 7.50772 9.16867 7.50688 9.16867 8.33411Z"
                :class="fillColor"
            />
            <path
                d="M17.8672 8.95896C18.6936 8.95896 18.6944 7.70936 17.8672 7.70936H16.2377C15.8928 7.70936 15.6129 7.98927 15.6129 8.33416V12.4095C15.6129 12.7544 15.8928 13.0343 16.2377 13.0343H17.8672C18.6936 13.0343 18.6944 11.7847 17.8672 11.7847H16.8625V10.9966H17.8672C18.6936 10.9966 18.6944 9.74703 17.8672 9.74703H16.8625V8.95896H17.8672Z"
                :class="fillColor"
            />
            <path
                d="M13.6133 8.33415V10.1536L12.3295 8.01342C12.003 7.47027 11.1683 7.70436 11.1683 8.33499V12.4103C11.1683 12.7552 11.4482 13.0351 11.793 13.0351C12.1379 13.0351 12.4178 12.7552 12.4178 12.4103V10.5909L13.7016 12.7311C13.8174 12.9227 14.0223 13.0343 14.2381 13.0343C14.2931 13.0343 14.3497 13.0268 14.4047 13.0118C14.6754 12.9368 14.8629 12.6902 14.8629 12.4095V8.33415C14.8629 7.50776 13.6133 7.50692 13.6133 8.33415Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
