<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M10.0284 15.402C10.0284 14.3333 10.0284 13.2646 10.0284 12.1958C10.0284 12.0038 10.0535 11.9287 10.2711 11.937C11.5684 11.9454 12.8741 11.9454 14.1714 11.937C14.3471 11.937 14.4392 11.9955 14.5062 12.1458C14.6234 12.4129 14.7322 12.6801 14.7322 12.9723C14.7322 14.2414 14.7656 15.5105 14.7238 16.7796C14.6903 17.8818 13.711 18.8419 12.6146 18.8837C11.8195 18.9171 11.016 18.8837 10.2125 18.9087C10.0367 18.9087 10.0284 18.8336 10.0284 18.7C10.0284 17.6062 10.0284 16.5041 10.0284 15.4104V15.402ZM11.6102 14.876C11.6102 15.4688 11.6102 16.0533 11.6102 16.6461C11.6102 16.8631 11.5433 17.147 11.6521 17.2723C11.7776 17.4226 12.0789 17.314 12.2966 17.314C12.849 17.314 13.1503 17.0134 13.1503 16.4624C13.1503 15.6358 13.1586 14.8175 13.167 13.991C13.167 13.6904 13.2005 13.3898 13.1503 13.0976C13.0833 12.7135 12.8239 12.4714 12.4305 12.4463C12.2463 12.438 12.0622 12.4547 11.8864 12.438C11.6688 12.4129 11.5935 12.4714 11.6019 12.7135C11.6186 13.4399 11.6019 14.1663 11.6019 14.8843L11.6102 14.876Z"
                :class="fillColor"
            />
            <path
                d="M10.0284 7.85418C10.0284 6.77711 10.0284 5.69169 10.0284 4.61462C10.0284 4.40589 10.0786 4.35579 10.2794 4.36414C11.016 4.38084 11.7525 4.34744 12.4891 4.38919C13.6525 4.46433 14.5731 5.47461 14.5899 6.63517C14.5982 7.76233 14.5899 8.88115 14.5899 10.0083C14.5899 10.4091 14.5062 10.7932 14.322 11.1605C14.2551 11.2858 14.1714 11.3359 14.0207 11.3359C12.7569 11.3359 11.4931 11.3359 10.2292 11.3359C10.0535 11.3359 10.02 11.2774 10.02 11.1188C10.02 10.0334 10.02 8.93959 10.02 7.85418H10.0284ZM11.5851 8.38019V10.3423C11.5851 10.6763 11.7497 10.8405 12.0789 10.8349C12.7234 10.8349 12.9996 10.551 12.9996 9.89977C12.9996 8.96464 12.9745 8.02951 12.9996 7.09438C13.0247 6.19265 12.7234 5.89208 11.8278 5.93382C11.6186 5.94217 11.5768 6.00062 11.5768 6.201C11.5851 6.9274 11.5768 7.65379 11.5768 8.38019H11.5851Z"
                :class="fillColor"
            />
            <path
                d="M18.3654 14.3333C17.6121 13.5651 16.8672 12.8053 16.114 12.0372C15.6955 11.6114 15.6955 11.6197 16.114 11.1939C16.8589 10.4341 17.5954 9.67434 18.3319 8.91455C18.357 8.93124 18.3822 8.95629 18.4156 8.97299V14.2748C18.4156 14.2748 18.3738 14.3166 18.357 14.3333H18.3654Z"
                :class="fillColor"
            />
            <path
                d="M5.81836 8.93959C5.89369 8.91455 5.9188 8.97299 5.95228 9.01474C6.77251 9.83298 7.60112 10.6512 8.42136 11.4778C8.47994 11.5362 8.59712 11.578 8.47157 11.7032C7.60112 12.5465 6.73903 13.4065 5.81836 14.3082V8.93959Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 24,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
