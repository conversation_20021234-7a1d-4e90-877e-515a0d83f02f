<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="19"
            height="19"
            viewBox="0 0 19 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M0 9.64395C0 9.2635 0.30842 8.95508 0.688874 8.95508H4.82212C5.20258 8.95508 5.511 9.2635 5.511 9.64395V15.8438C5.511 17.3656 4.27732 18.5993 2.7555 18.5993C1.23368 18.5993 0 17.3656 0 15.8438V9.64395Z"
                :class="fillColorSecondary"
            />
            <path
                d="M13.0886 9.64395C13.0886 9.2635 13.397 8.95508 13.7775 8.95508H17.9107C18.2912 8.95508 18.5996 9.2635 18.5996 9.64395V15.8438C18.5996 17.3656 17.3659 18.5993 15.8441 18.5993C14.3223 18.5993 13.0886 17.3656 13.0886 15.8438V9.64395Z"
                :class="fillColorSecondary"
            />
            <path
                d="M10.3331 1.20553C10.3331 0.539734 9.79338 0 9.12759 0C8.46179 0 7.92206 0.539734 7.92206 1.20553C7.92206 1.87133 7.38232 2.41106 6.71653 2.41106H6.54431C2.92999 2.41106 0 5.34105 0 8.95537C0 12.5697 2.92999 15.4997 6.54431 15.4997H12.0553C15.6696 15.4997 18.5996 12.5697 18.5996 8.95537C18.5996 5.34105 15.6696 2.41106 12.0553 2.41106H11.5386C10.8729 2.41106 10.3331 1.87133 10.3331 1.20553Z"
                :class="fillColorPrimary"
            />
            <path
                d="M12.3333 6.66667C12.3333 6.29848 12.6318 6 13 6C13.3682 6 13.6667 6.29848 13.6667 6.66667C13.6667 7.03486 13.3682 7.33333 13 7.33333C12.6318 7.33333 12.3333 7.03486 12.3333 6.66667Z"
                fill="white"
            />
            <path
                d="M12.3333 9.33333C12.3333 8.96514 12.6318 8.66667 13 8.66667C13.3682 8.66667 13.6667 8.96514 13.6667 9.33333C13.6667 9.70152 13.3682 10 13 10C12.6318 10 12.3333 9.70152 12.3333 9.33333Z"
                fill="white"
            />
            <path
                d="M11.6667 8.66667C11.2985 8.66667 11 8.36819 11 8C11 7.63181 11.2985 7.33333 11.6667 7.33333C12.0349 7.33333 12.3333 7.63181 12.3333 8C12.3333 8.36819 12.0349 8.66667 11.6667 8.66667Z"
                fill="white"
            />
            <path
                d="M14.3333 8.66667C13.9651 8.66667 13.6667 8.36819 13.6667 8C13.6667 7.63181 13.9651 7.33333 14.3333 7.33333C14.7015 7.33333 15 7.63181 15 8C15 8.36819 14.7015 8.66667 14.3333 8.66667Z"
                fill="white"
            />
            <path
                d="M4.33333 6.66667C4.33333 6.29848 4.63181 6 5 6C5.36819 6 5.66667 6.29848 5.66667 6.66667V9.33333C5.66667 9.70152 5.36819 10 5 10C4.63181 10 4.33333 9.70152 4.33333 9.33333V6.66667Z"
                fill="white"
            />
            <path
                d="M3 8C3 7.63181 3.29848 7.33333 3.66667 7.33333H6.33333C6.70152 7.33333 7 7.63181 7 8C7 8.36819 6.70152 8.66667 6.33333 8.66667H3.66667C3.29848 8.66667 3 8.36819 3 8Z"
                fill="white"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
