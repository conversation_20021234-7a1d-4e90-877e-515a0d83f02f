<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M18.1478 10.6273C14.0015 10.6273 10.6283 14.0005 10.6283 18.1468C10.6283 22.2931 14.0015 25.6663 18.1478 25.6663C22.2941 25.6663 25.6673 22.2931 25.6673 18.1468C25.6673 14.0005 22.2941 10.6273 18.1478 10.6273ZM18.1478 17.4632C19.2786 17.4632 20.1986 18.3832 20.1986 19.514C20.1986 20.404 19.6254 21.1557 18.8314 21.4389V22.932H17.4642V21.4389C16.6702 21.1557 16.097 20.404 16.097 19.514H17.4642C17.4642 19.8912 17.7706 20.1976 18.1478 20.1976C18.5249 20.1976 18.8314 19.8912 18.8314 19.514C18.8314 19.1368 18.5249 18.8304 18.1478 18.8304C17.0169 18.8304 16.097 17.9105 16.097 16.7796C16.097 15.8896 16.6702 15.1379 17.4642 14.8548V13.3617H18.8314V14.8548C19.6254 15.1379 20.1986 15.8896 20.1986 16.7796H18.8314C18.8314 16.4025 18.5249 16.096 18.1478 16.096C17.7706 16.096 17.4642 16.4025 17.4642 16.7796C17.4642 17.1568 17.7706 17.4632 18.1478 17.4632Z"
                :class="fillColor"
            />
            <path
                d="M9.94466 10.6273C14.1611 10.6273 17.4642 8.82551 17.4642 6.52572C17.4642 4.22592 14.1611 2.33301 9.94466 2.33301C5.72826 2.33301 2.33398 4.22592 2.33398 6.52572C2.33398 8.82551 5.72826 10.6273 9.94466 10.6273Z"
                :class="fillColor"
            />
            <path
                d="M2.33398 17.7166V18.8304C2.33398 21.1302 5.72826 22.932 9.94466 22.932C10.1838 22.932 10.4156 22.91 10.651 22.8984C10.1267 22.074 9.73562 21.1591 9.50534 20.1811C6.45628 20.097 3.80882 19.16 2.33398 17.7166Z"
                :class="fillColor"
            />
            <path
                d="M9.29392 18.7956C9.27825 18.5806 9.26107 18.3658 9.26107 18.1468C9.26107 17.4342 9.35444 16.7445 9.51368 16.0799C6.46084 15.9972 3.81014 15.0598 2.33398 13.615V14.7288C2.33398 16.9066 5.39931 18.6181 9.29392 18.7956Z"
                :class="fillColor"
            />
            <path
                d="M9.94466 14.7288L9.94676 14.7287C10.3976 13.6512 11.053 12.6799 11.8669 11.866C11.2495 11.9441 10.6115 11.9945 9.94466 11.9945C6.69909 11.9945 3.88009 11.0266 2.33398 9.51343V10.6273C2.33398 12.9271 5.72826 14.7288 9.94466 14.7288Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
