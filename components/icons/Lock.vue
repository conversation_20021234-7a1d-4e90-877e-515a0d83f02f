<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="19"
            height="22"
            viewBox="0 0 19 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.31283 0H10.6878C12.874 0 14.6462 1.72157 14.6462 3.84524V11.5357C14.6462 13.6594 12.874 15.381 10.6878 15.381H8.31283C6.1267 15.381 4.35449 13.6594 4.35449 11.5357V3.84524C4.35449 1.72157 6.1267 0 8.31283 0ZM8.31283 3.07619C7.8756 3.07619 7.52116 3.42051 7.52116 3.84524V11.5357C7.52116 11.9604 7.8756 12.3048 8.31283 12.3048H10.6878C11.1251 12.3048 11.4795 11.9604 11.4795 11.5357V3.84524C11.4795 3.42051 11.1251 3.07619 10.6878 3.07619H8.31283Z"
                :class="fillColorSecondary"
            />
            <path
                d="M0 9.22853C0 7.5296 1.41777 6.15234 3.16667 6.15234H15.8333C17.5822 6.15234 19 7.5296 19 9.22853V12.3047C19 17.4015 14.7467 21.5333 9.5 21.5333C4.2533 21.5333 0 17.4015 0 12.3047V9.22853Z"
                :class="fillColorPrimary"
            />
            <path
                d="M9.5 10.7676C9.06277 10.7676 8.70833 11.1119 8.70833 11.5366V13.8438H7.91667C7.47944 13.8438 7.125 14.1881 7.125 14.6128C7.125 15.0375 7.47944 15.3819 7.91667 15.3819H11.0833C11.5206 15.3819 11.875 15.0375 11.875 14.6128C11.875 14.1881 11.5206 13.8438 11.0833 13.8438H10.2917V11.5366C10.2917 11.1119 9.93723 10.7676 9.5 10.7676Z"
                fill="white"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorSecondary: {
        type: String,
        default: 'fill-green-200',
    },
    fillColorPrimary: {
        type: String,
        default: 'fill-green-400',
    },
})
</script>
