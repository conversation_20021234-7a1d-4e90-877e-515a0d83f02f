<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M6.24984 1.875C4.8685 1.875 3.54374 2.42373 2.56699 3.40049C1.59024 4.37724 1.0415 5.702 1.0415 7.08333C1.0415 9.28097 2.47564 10.901 3.72828 12.1123L9.5579 17.9419C9.80197 18.186 10.1977 18.186 10.4418 17.9419L16.2729 12.1109C17.5126 10.8958 18.9582 9.27412 18.9582 7.08333C18.9582 5.702 18.4094 4.37724 17.4327 3.40049C16.4559 2.42373 15.1312 1.875 13.7498 1.875C12.9539 1.875 12.2395 1.98866 11.544 2.29157C11.0078 2.52511 10.5062 2.861 9.99984 3.30812C9.49352 2.861 8.99185 2.52511 8.45566 2.29157C7.76016 1.98866 7.04579 1.875 6.24984 1.875Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
