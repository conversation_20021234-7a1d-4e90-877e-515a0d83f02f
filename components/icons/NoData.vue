<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 70 75"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M53.3667 41.5C50.094 41.5 46.9849 42.4818 44.2849 44.2818C41.5849 46.0818 39.4576 48.7 38.2304 51.7273C37.0031 54.7545 36.6758 58.1091 37.3304 61.3C37.9849 64.4909 39.5395 67.4364 41.8304 69.8091C44.1213 72.1 47.0667 73.7364 50.2576 74.3091C53.4486 74.9636 56.7213 74.6364 59.7486 73.3273C62.7758 72.1 65.3122 69.9727 67.1122 67.2727C68.9122 64.5727 69.894 61.3818 69.894 58.1091C69.894 53.6909 68.1758 49.5182 65.0667 46.4091C61.8758 43.2182 57.7031 41.5 53.3667 41.5ZM53.3667 69.8091C51.2395 69.8091 49.194 69.2364 47.394 68.1727C45.594 67.1091 44.1213 65.4727 43.0576 63.5909C42.0758 61.7091 41.5849 59.5818 41.6667 57.4545C41.7486 55.3273 42.4031 53.2818 43.6304 51.4818L59.8304 67.8455C57.8667 69.1546 55.6576 69.8091 53.3667 69.8091ZM63.1031 64.5727L46.9031 48.2091C49.194 46.7364 51.8122 46 54.5122 46.3273C57.2122 46.5727 59.6667 47.8 61.6304 49.7636C63.5122 51.7273 64.7395 54.1818 64.9849 56.9636C65.3122 59.5818 64.5758 62.2818 63.1031 64.5727Z"
                :class="fillColor"
            />
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M35.7457 47.3244C33.9174 50.3835 32.9524 53.8761 32.9515 57.4349C32.948 60.0729 33.4773 62.6847 34.5059 65.116C35.5354 67.5473 36.601 69.6586 38.5 71.5C36.6355 71.5746 34.7029 71.707 31.4228 71.707C23.4973 71.707 21.434 71.4609 18.5179 71.1133C17.7907 71.0266 17.0104 70.9336 16.0737 70.8365L15.9571 70.8238C12.0973 70.4247 8.4937 68.7176 5.74995 65.9888C3.0062 63.2601 1.28925 59.6758 0.888075 55.8374C0.299559 50.2124 0 44.787 0 36.0866C0 27.3861 0.299559 21.9608 0.888075 16.3358C1.22121 13.14 2.46982 10.1063 4.48367 7.59454C6.49752 5.08262 9.19444 3.1978 12.2528 2.16355V2.22683C13.4793 1.80122 14.7509 1.5181 16.0428 1.38308L16.1603 1.37045C16.8929 1.29453 17.5503 1.22103 18.175 1.15116C21.5541 0.77345 23.9992 0.5 32.3542 0.5C40.2594 0.5 42.3333 0.744776 45.2388 1.08744C45.9722 1.17401 46.7587 1.26674 47.7051 1.36342L47.8217 1.37605C49.0526 1.50448 50.2659 1.76716 51.4385 2.15938C51.5967 2.21277 51.754 2.26902 51.9113 2.32813C54.8795 3.41127 57.4818 5.3018 59.4232 7.78427C61.3637 10.2667 62.5646 13.2414 62.8898 16.3695H62.8059C63.3926 21.9833 63.6895 27.403 63.6895 36.0866C63.6895 36.8609 63.6869 37.6092 63.6825 38.3339C63.6772 39.1899 63.701 39.9427 63.6895 40.737C60.6842 38.8065 57.1779 37.7877 53.6026 37.6547C50.0273 37.5215 46.4821 38.3494 43.3398 40.051C40.1966 41.7527 37.5739 44.2653 35.7457 47.3244ZM47.4665 18.4561H16.6074C15.7141 18.4561 14.8578 18.809 14.226 19.4371C13.5951 20.0653 13.2398 20.9173 13.2398 21.8058C13.2398 22.6942 13.5951 23.5462 14.226 24.1743C14.8578 24.8025 15.7141 25.1554 16.6074 25.1554H47.4665C48.3599 25.1554 49.2161 24.8025 49.8479 24.1743C50.4789 23.5462 50.8341 22.6942 50.8341 21.8058C50.8341 20.9173 50.4789 20.0653 49.8479 19.4371C49.2161 18.809 48.3599 18.4561 47.4665 18.4561ZM14.2631 38.4842C14.8799 39.1105 15.7194 39.4714 16.6004 39.4894H34.2046C34.27 39.4907 34.3353 39.4902 34.4007 39.4877C34.5351 39.4826 34.6685 39.4696 34.8002 39.4489C34.8788 39.4365 34.9574 39.4213 35.0352 39.4033C35.1987 39.3657 35.3586 39.3159 35.515 39.2543C35.9312 39.0901 36.3112 38.8447 36.6311 38.5327C36.7548 38.4125 36.8688 38.2834 36.9713 38.1466C37.1357 37.928 37.2726 37.69 37.3795 37.4373C37.5527 37.0262 37.6429 36.5848 37.6429 36.139C37.6429 35.7788 37.5846 35.4216 37.4706 35.081C37.4494 35.0178 37.4255 34.955 37.4008 34.8929L37.3795 34.8408C37.2055 34.4297 36.9519 34.0572 36.6311 33.7452C36.3112 33.4332 35.9312 33.1879 35.515 33.0236C35.3365 32.9534 35.1536 32.8986 34.9672 32.8596C34.7171 32.8073 34.4608 32.7834 34.2046 32.7886H16.6004C15.7194 32.8065 14.8799 33.1674 14.2631 33.7938C13.6463 34.4201 13.3008 35.2621 13.3008 36.139C13.3008 37.0158 13.6463 37.8578 14.2631 38.4842Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 70,
    },
    h: {
        type: Number,
        default: 75,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-gray-400',
    },
})
</script>
