<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M10 1.25C5.17517 1.25 1.25 5.17517 1.25 10C1.25 14.8248 5.17517 18.75 10 18.75C14.8248 18.75 18.75 14.8248 18.75 10C18.75 5.17517 14.8248 1.25 10 1.25ZM10 17.5C5.86426 17.5 2.5 14.1357 2.5 10C2.5 5.86426 5.86426 2.5 10 2.5C14.1357 2.5 17.5 5.86426 17.5 10C17.5 14.1357 14.1357 17.5 10 17.5Z"
                :class="fillColor"
            />
            <path
                d="M12.7156 9.57588C12.9551 9.21127 13.0969 8.77674 13.0969 8.30872V7.87659C13.0969 6.87141 12.4499 6.02222 11.5533 5.70221V5C11.5533 4.65454 11.2738 4.375 10.9283 4.375C10.5829 4.375 10.3033 4.65454 10.3033 5V5.55969H8.78357V5C8.78357 4.65454 8.50403 4.375 8.15857 4.375C7.81311 4.375 7.53357 4.65454 7.53357 5V5.55969H6.52771C6.18225 5.55969 5.90271 5.83923 5.90271 6.18469C5.90271 6.53015 6.18225 6.80969 6.52771 6.80969H6.98975V13.1903H6.52771C6.18225 13.1903 5.90271 13.4698 5.90271 13.8153C5.90271 14.1608 6.18225 14.4403 6.52771 14.4403H7.53357V15C7.53357 15.3455 7.81311 15.625 8.15857 15.625C8.50403 15.625 8.78357 15.3455 8.78357 15V14.4403H10.3033V15C10.3033 15.3455 10.5829 15.625 10.9283 15.625C11.2738 15.625 11.5533 15.3455 11.5533 15V14.4403H11.7804C13.0579 14.4403 14.0973 13.4009 14.0973 12.1234V11.6913C14.0973 10.7471 13.5277 9.93626 12.7156 9.57588ZM11.8469 8.30872C11.8469 8.89648 11.3684 9.375 10.7806 9.375H8.23975V6.80969H10.7806C11.3684 6.80969 11.8469 7.28821 11.8469 7.87659V8.30872ZM12.8473 12.1234C12.8473 12.7118 12.3688 13.1903 11.7804 13.1903H8.23975V10.625H11.7804C12.3688 10.625 12.8473 11.1035 12.8473 11.6913V12.1234Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-green-200',
    },
})
</script>
