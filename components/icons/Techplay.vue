<template>
    <span :class="classWrapper ? classWrapper : 'flex items-center justify-center'">
        <svg
            :width="w"
            :height="h"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.55502 2.31341C5.90371 1.93775 5.09082 2.40987 5.09082 3.16626V9.055L11.6646 5.27809L6.55502 2.31341Z"
                :class="fillColor"
            />
            <path
                d="M21.3296 10.8925L13.9783 6.62318L10.439 8.65377L14.7861 16.3904L21.3296 12.5881C21.9809 12.2124 21.9809 11.2682 21.3296 10.8925Z"
                :class="fillColor"
            />
            <path
                d="M5.09082 11.7252V20.3146C5.09082 21.071 5.90371 21.5431 6.55502 21.1624L12.74 17.5733L8.39789 9.8215L5.09082 11.7252Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    w: {
        type: Number,
        default: 24,
    },
    h: {
        type: Number,
        default: 25,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
