<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M14.0548 3.63672C14.864 4.13425 15.1023 4.81836 15.0233 5.80353C14.8764 7.66646 15.342 9.48273 15.7368 11.2919C15.7728 11.4573 15.7895 11.4531 16.5598 11.6114C15.7424 11.6015 15.9156 12.0072 16.0666 12.4623C16.4573 13.6482 16.442 13.6496 17.5615 13.0997C17.6959 13.0333 17.8248 12.8934 18.0645 12.9966C17.8081 13.3499 17.4479 13.5506 17.1099 13.7316C16.6513 13.9761 16.5806 14.2489 16.7552 14.7351C17.4424 16.6489 18.088 18.5783 18.7531 20.5006C19.1077 21.5239 18.9581 21.979 18.0714 22.5472H17.5172C16.2093 21.4037 14.8501 20.3041 13.7625 18.9359C12.7123 17.6143 12.7275 16.2122 14.0507 15.2298C15.1729 14.3973 15.2145 13.593 14.7351 12.4976C14.7254 12.4764 14.724 12.4524 14.7199 12.4284C14.6631 12.0948 14.537 11.9676 14.1671 12.1019C13.2651 12.4284 12.3701 12.7209 12.1872 13.88C12.1553 14.0764 12.0085 14.2574 11.9004 14.4383C11.7078 14.7605 11.4889 15.063 11.1675 15.2666C11.0054 15.3697 10.7989 15.4489 10.6562 15.309C10.5107 15.1662 10.7144 15.0206 10.7684 14.8821C10.907 14.5245 11.216 14.2305 11.1508 13.798C11.1841 13.7923 11.2173 13.7867 11.2492 13.781L11.2353 13.7174L11.1508 13.798C10.2586 14.2305 9.61432 14.9627 8.96175 15.6835C8.19002 16.5358 7.33933 17.2807 6.24479 17.6567C5.57559 17.8857 4.89947 17.9592 4.36328 17.3245C4.36328 17.1365 4.36328 16.9486 4.36328 16.7606C4.42701 16.7026 4.50183 16.6517 4.55309 16.5839C5.21813 15.7203 6.15196 15.2637 7.10518 14.8411C7.24234 14.7803 7.43216 14.6955 7.5236 14.9005C7.59426 15.0574 7.43631 15.1577 7.33656 15.2397C7.06639 15.4588 6.78236 15.6595 6.50526 15.8687C6.39858 15.9493 6.2337 16.0284 6.29051 16.1839C6.36671 16.3945 6.56068 16.2941 6.71031 16.2758C7.87967 16.133 7.75359 14.998 8.17894 14.2828C8.28008 14.1118 8.33966 13.9436 8.2593 13.7513C8.20527 13.6227 8.15816 13.4828 8.07226 13.3796C7.83534 13.0927 7.59426 12.8072 7.32963 12.5485C6.66598 11.8983 5.93028 11.3202 5.35391 10.581C4.35081 9.29475 4.34804 8.09332 5.32066 6.79436C6.47062 5.25794 7.98081 4.24874 9.76256 3.63672H10.0397C10.6382 4.12577 10.7186 5.05582 10.1408 5.70883C9.52287 6.40707 8.76223 6.95125 8.00714 7.4926C7.89353 7.57458 7.75221 7.66928 7.63167 7.55762C7.46957 7.40779 7.62197 7.23818 7.69956 7.12652C7.92401 6.80143 8.18864 6.50743 8.42694 6.19223C8.51007 6.08198 8.58905 5.92792 8.4505 5.82049C8.37707 5.76254 8.22882 5.76254 8.12768 5.79081C6.98326 6.09753 6.1672 7.90957 6.68122 8.99934C7.04837 9.77673 7.63167 10.376 8.23852 10.9584C9.02409 11.7103 9.79304 12.4778 10.0036 13.6595C10.5287 13.3301 11.0123 13.0206 11.5041 12.7238C11.7036 12.6036 11.7701 12.4114 11.8339 12.2093C12.668 9.53079 13.617 6.87775 13.617 4.00846C13.617 3.88549 13.7223 3.7611 13.7791 3.63813C13.8719 3.63813 13.9634 3.63813 14.0562 3.63813L14.0548 3.63672ZM12.9451 11.9591C13.3635 11.8488 13.7833 11.7443 14.2003 11.6283C14.3624 11.5831 14.5051 11.504 14.4608 11.2891C14.2779 10.4213 14.0936 9.55341 13.9107 8.68555C13.5824 9.80641 13.0531 10.8297 12.9547 11.9746C12.9284 11.9831 12.9035 11.9902 12.8772 11.9987C12.8882 12.0015 12.9063 12.0128 12.9104 12.0086C12.9243 11.9958 12.934 11.9775 12.9451 11.9605V11.9591ZM17.2276 21.1493C17.2623 21.1762 17.2969 21.203 17.3329 21.2299C17.3246 21.1762 17.3177 21.1239 17.3094 21.0702C16.679 18.9769 16.0486 16.885 15.3891 14.6927C15.0662 14.9839 14.8182 15.1874 14.5938 15.415C13.4577 16.5683 13.4383 17.5281 14.5855 18.6814C15.4348 19.5352 16.2813 20.3988 17.2276 21.1479V21.1493Z"
                :class="fillColor"
            />
            <path
                d="M18.5826 11.9308C18.2667 11.863 17.9093 11.8672 17.8774 11.4785C17.858 11.2439 18.1157 11.217 18.2903 11.258C18.6034 11.3329 19.0551 11.2948 19.1202 11.6863C19.1576 11.9125 18.7434 11.8135 18.5826 11.9308Z"
                :class="fillColor"
            />
            <path
                d="M12.9464 11.9591C12.9354 11.9746 12.927 11.9944 12.9118 12.0072C12.9076 12.0114 12.8896 12.0015 12.8785 11.9973C12.9049 11.9888 12.9298 11.9817 12.9561 11.9732L12.9464 11.9591Z"
                :class="fillColor"
            />
            <path
                d="M11.1481 13.7966L11.2326 13.716L11.2464 13.7796C11.2132 13.7853 11.1799 13.7909 11.1481 13.7966Z"
                :class="fillColor"
            />
            <path
                d="M17.3094 21.0702C17.3177 21.1239 17.3246 21.1762 17.3329 21.2299C17.2983 21.203 17.2636 21.1762 17.2276 21.1493C17.2526 21.1211 17.2803 21.0942 17.308 21.0702H17.3094Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
