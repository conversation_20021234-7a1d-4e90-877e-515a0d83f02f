<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                d="M1.16602 21.8124C1.16602 22.4748 1.70298 23.0117 2.36535 23.0117H21.019C21.6814 23.0117 22.2183 22.4748 22.2183 21.8124V12.1611C22.2183 11.4987 21.6814 10.9618 21.019 10.9618H2.36535C1.70298 10.9618 1.16602 11.4987 1.16602 12.1611V21.8124ZM3.10276 14.354C3.32481 14.354 3.54468 14.3103 3.74982 14.2253C3.95496 14.1403 4.14134 14.0157 4.29833 13.8587C4.45531 13.7016 4.57982 13.5152 4.66474 13.31C4.74967 13.1049 4.79334 12.885 4.79326 12.6629H18.5905C18.5905 13.1114 18.7687 13.5416 19.0858 13.8587C19.403 14.1758 19.8331 14.354 20.2816 14.354V19.615C19.8331 19.615 19.403 19.7931 19.0858 20.1103C18.7687 20.4274 18.5905 20.8576 18.5905 21.3061H4.79326C4.79334 21.084 4.74967 20.8641 4.66474 20.659C4.57982 20.4538 4.45531 20.2674 4.29833 20.1103C4.14134 19.9533 3.95496 19.8287 3.74982 19.7437C3.54468 19.6587 3.32481 19.615 3.10276 19.615V14.354Z"
                :class="fillColor"
            />
            <path
                d="M11.6919 19.5233C13.0928 19.5233 14.2285 18.3877 14.2285 16.9867C14.2285 15.5858 13.0928 14.4501 11.6919 14.4501C10.291 14.4501 9.1553 15.5858 9.1553 16.9867C9.1553 18.3877 10.291 19.5233 11.6919 19.5233Z"
                :class="fillColor"
            />
            <path
                d="M2.27184 10.1179H23.0633V19.8438L23.5695 19.7902C24.2278 19.7204 24.7051 19.1305 24.6359 18.4721L23.6284 8.8877C23.5587 8.22456 22.9615 7.74559 22.299 7.82155L2.27184 10.1179Z"
                :class="fillColor"
            />
            <path
                d="M24.9857 5.63966C24.8592 4.98061 24.2167 4.55353 23.5601 4.69214L3.41253 8.94518L24.2495 6.75601L25.2952 16.6955L25.9133 16.547C26.5364 16.3973 26.9317 15.7843 26.811 15.1549L24.9857 5.63966Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
