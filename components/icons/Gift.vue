<template>
    <span :class="['flex', classWrapper]">
        <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="h-auto w-full"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M10.0253 5.96217C9.36527 5.22877 8.65348 4.91185 7.99304 4.92335L7.98064 4.92357C7.54033 4.92357 7.11804 5.09838 6.80669 5.40973C6.49534 5.72108 6.32043 6.14336 6.32043 6.58368C6.32043 7.02399 6.49534 7.44627 6.80669 7.75762C7.11804 8.06897 7.54033 8.24389 7.98064 8.24389C7.98344 8.24389 7.98624 8.24391 7.98903 8.24394H11.3012C10.9778 7.30846 10.5363 6.52991 10.0253 5.96217ZM11.5382 9.66698V12.0387H4.66021C4.52923 12.0387 4.42304 11.9325 4.42304 11.8015V9.90415C4.42304 9.77316 4.52923 9.66698 4.66021 9.66698H11.5382ZM4.89739 13.4618H4.66021C3.7433 13.4618 3 12.7185 3 11.8015V9.90415C3 8.98724 3.7433 8.24394 4.66021 8.24394H5.38255C5.06819 7.75198 4.89739 7.17663 4.89739 6.58368C4.89739 5.76594 5.22223 4.98171 5.80045 4.40348C6.3772 3.82673 7.15892 3.50208 7.9744 3.50043C9.14197 3.48215 10.2216 4.053 11.0831 5.01021C11.5368 5.5143 11.928 6.12155 12.2498 6.8029C12.5715 6.12155 12.9628 5.5143 13.4165 5.01021C14.2779 4.053 15.3576 3.48215 16.5251 3.50043C17.3406 3.50208 18.1223 3.82673 18.6991 4.40348C19.2773 4.98171 19.6021 5.76594 19.6021 6.58368C19.6021 7.17663 19.4313 7.75198 19.117 8.24394H19.8393C20.7562 8.24394 21.4995 8.98724 21.4995 9.90415V11.8015C21.4995 12.7185 20.7562 13.4618 19.8393 13.4618H19.6021V19.3911C19.6021 20.083 19.3273 20.7466 18.838 21.2359C18.3487 21.7251 17.6852 22 16.9932 22H7.5063C6.81437 22 6.15078 21.7251 5.66152 21.2359C5.17225 20.7466 4.89739 20.083 4.89739 19.3911V13.4618ZM11.5382 20.577H7.5063C7.19178 20.577 6.89015 20.452 6.66776 20.2296C6.44537 20.0072 6.32043 19.7056 6.32043 19.3911V13.4618H11.5382V20.577ZM12.9613 20.577H16.9932C17.3077 20.577 17.6094 20.452 17.8318 20.2296C18.0542 20.0072 18.1791 19.7056 18.1791 19.3911V13.4618H12.9613V20.577ZM12.9613 12.0387V9.66698H19.8393C19.9703 9.66698 20.0765 9.77316 20.0765 9.90415V11.8015C20.0765 11.9325 19.9703 12.0387 19.8393 12.0387H12.9613ZM16.5105 8.24394C16.5133 8.24391 16.5161 8.24389 16.5189 8.24389C16.9592 8.24389 17.3815 8.06897 17.6928 7.75762C18.0042 7.44627 18.1791 7.02399 18.1791 6.58368C18.1791 6.14336 18.0042 5.72108 17.6928 5.40973C17.3815 5.09838 16.9592 4.92346 16.5189 4.92346H16.5065C15.846 4.91195 15.1343 5.22877 14.4742 5.96217C13.9632 6.52991 13.5218 7.30846 13.1983 8.24394H16.5105Z"
                :class="fillColor"
            />
        </svg>
    </span>
</template>

<script setup>
defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
    fillColor: {
        type: String,
        default: 'fill-grey-500',
    },
})
</script>
