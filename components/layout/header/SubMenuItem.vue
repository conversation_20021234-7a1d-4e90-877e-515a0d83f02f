<template>
    <div
        class="relative aspect-[auto_90/114] cursor-pointer transition-all ease-in-out hover:-translate-y-[3px]"
        @click="redirect(item)"
    >
        <ModulesGameBadge
            size="small"
            :type="item?.type"
            class-wrapper="left-0 top-0"
            class-image="w-10"
        />
        <img class="w-[135px]" :src="`/assets/images/menu/${item.name}.webp`" :alt="item.name" />
    </div>
</template>
<script setup>
defineProps({
    item: {
        type: Object,
        default: () => {},
    },
    redirect: {
        type: Function,
        default: () => {},
    },
})
</script>
