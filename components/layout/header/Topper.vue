<template>
    <div class="top-main-header container mx-auto my-0 py-3">
        <nav class="flex items-center justify-between space-x-3 lg:h-auto">
            <NuxtLink :to="localePath('/')" class="flex flex-shrink-0">
                <img :src="`/assets/images/logo.svg?v=1`" class="h-7 xl:h-10" alt="Logo" />
            </NuxtLink>

            <div class="flex items-center">
                <template v-if="isLoggedIn">
                    <div class="flex items-center gap-2">
                        <UserNotification />

                        <NuxtLink
                            :to="localePath('/user/deposit')"
                            class="pg-3 flex items-center gap-4 rounded-full bg-gray-200 py-1 pl-3 pr-1 lg:pl-4"
                        >
                            <span class="text-base font-semibold text-grey-900">
                                {{ NumberUtils.formatAmount(balance) }}
                            </span>
                            <span
                                class="flex h-7 w-7 items-center justify-center rounded-full bg-bigg-red lg:h-9 lg:w-9"
                            >
                                <IconsPlus
                                    fill-color="fill-white"
                                    class-wrapper="w-4 h-4 lg:w-5 lg:h-5"
                                />
                            </span>
                        </NuxtLink>
                        <NuxtLink
                            :to="localePath('/user/profile')"
                            class="hidden h-11 w-11 items-center justify-center rounded-full bg-gray-200 lg:flex"
                        >
                            <IconsUser3 fill-color="fill-grey-900" />
                        </NuxtLink>
                    </div>
                </template>
                <LayoutHeaderAuthGroupBtn v-else />
            </div>
        </nav>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useUserStore } from '~/stores'
import { NumberUtils } from '~/utils'

const localePath = useLocalePath()

useAuth()

const useUserStoreInstance = useUserStore()
const { isLoggedIn, balance } = storeToRefs(useUserStoreInstance)
</script>
