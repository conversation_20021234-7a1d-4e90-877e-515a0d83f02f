<template>
    <header class="header w-full bg-slate-900">
        <div class="container">
            <nav>
                <div
                    class="relative mx-auto flex flex-nowrap items-center justify-between max-lg:h-[62px]"
                >
                    <div class="flex items-center">
                        <NuxtLink :to="localePath('/')" class="flex flex-shrink-0">
                            <img
                                :src="`/assets/images/logo.svg?v=1`"
                                class="h-9 xl:h-12"
                                alt="Logo"
                            />
                        </NuxtLink>
                    </div>
                    <div class="flex items-center gap-4">
                        <div class="order-3 hidden flex-auto xl:order-2 xl:flex 2xl:mr-2">
                            <LayoutHeaderMenu />
                        </div>
                        <div class="flex items-center xl:order-3">
                            <template v-if="isLoggedIn">
                                <div class="flex items-center gap-2">
                                    <UserNotification />

                                    <NuxtLink
                                        :to="localePath('/user/deposit')"
                                        class="pg-3 flex items-center gap-4 rounded-full border border-slate-750 py-1 pl-3 pr-1 lg:pl-4"
                                    >
                                        <span class="text-base font-semibold text-white">
                                            {{ NumberUtils.formatAmount(balance) }}
                                        </span>
                                        <span
                                            class="flex h-7 w-7 items-center justify-center rounded-full bg-rose-600 lg:h-9 lg:w-9"
                                        >
                                            <IconsPlus
                                                fill-color="fill-white"
                                                class-wrapper="w-4 h-4 lg:w-5 lg:h-5"
                                            />
                                        </span>
                                    </NuxtLink>
                                    <NuxtLink
                                        :to="localePath('/user/dashboard')"
                                        class="hidden h-12 w-12 items-center justify-center rounded-full border border-slate-750 bg-slate-800 lg:flex"
                                    >
                                        <IconsUser3 fill-color="fill-green-400" />
                                    </NuxtLink>
                                </div>
                            </template>
                            <LayoutHeaderAuthGroupBtn v-else />
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useUserStore } from '~~/stores'
import { NumberUtils } from '~/utils'

const localePath = useLocalePath()

useAuth()

const useUserStoreInstance = useUserStore()
const { isLoggedIn, balance } = storeToRefs(useUserStoreInstance)
</script>
