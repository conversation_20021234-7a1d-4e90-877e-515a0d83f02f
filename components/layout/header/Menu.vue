<template>
    <nav class="flex flex-1">
        <div id="mega-menu-full-image" class="w-full flex-1 items-center justify-between">
            <ul class="site-nav flex flex-1 items-center gap-5 max-2xl:justify-between md:mt-0">
                <li
                    class="site-nav-item"
                    v-for="headerLink in mainNavbar"
                    :key="headerLink.url"
                    :class="isRouteActive(headerLink) ? 'is-active' : ''"
                >
                    <LayoutHeaderSubMenu v-if="headerLink.sub_menu" :headerLink="headerLink" />
                    <NuxtLink
                        v-else
                        :to="SITE_URL + headerLink.url"
                        class="hover:header-hover relative block whitespace-nowrap border-transparent px-3 py-3 text-center text-xs font-semibold uppercase text-slate-300 transition-all duration-100 ease-in max-2xl:justify-between xl:px-0 xl:py-[26px]"
                        aria-current="page"
                        :class="isRouteActive(headerLink) ? 'text-green-400' : ''"
                        @click.prevent="openSubLinkV2(headerLink)"
                    >
                        {{ $t(headerLink.i18_key) }}
                        <img
                            v-if="headerLink.label"
                            class="absolute -right-2.5 top-2.5 w-[30px]"
                            :src="`/assets/images/v2/tags/${headerLink.label}.webp`"
                            :alt="headerLink.label"
                        />
                    </NuxtLink>
                </li>
            </ul>
        </div>
    </nav>
</template>

<script setup>
import { useNavBarStore } from '~/stores'
import { storeToRefs } from 'pinia'

const useNavBarStoreInstance = useNavBarStore()

const { mainNavbar } = storeToRefs(useNavBarStoreInstance)
const { SITE_URL } = useRuntimeConfig().public
const route = useRoute()
const isRouteActive = item =>
    route.fullPath === item.url || (item.urlRegex && item.urlRegex.test(route.fullPath))

const { openSubLinkV2 } = usePlayGame()
</script>
<style lang="scss">
.site-nav-item {
    @apply relative transition-all duration-300;
    &:before {
        @apply absolute bottom-0 left-1/2 h-0.5 w-5 -translate-x-1/2 transform rounded-full bg-transparent transition-all duration-300 content-[''];
    }
    &.is-active,
    &:hover {
        @apply text-green-400;
        &:before {
            @apply bg-green-400;
        }
    }
}
</style>
