<template>
    <div
        :key="item.id"
        :data-url="item.url"
        :class="[
            'item-menu relative flex h-full cursor-pointer items-center py-5 text-[18px] font-bold leading-[26px] text-white transition-all hover:opacity-60',
            {
                'pl-0': order === 'first',
                'pr-0': order === 'last',
                'is-active': determineActiveRoute(item.id),
            },
        ]"
        @mouseover="showDropdown(true, item.id)"
        @mouseleave="showDropdown(false)"
    >
        <span
            :class="[
                'flex items-center text-center',
                {
                    ['router-link-active router-link-exact-active']: determineActiveRoute(item.id),
                    active: determineActiveRoute(item.id),
                },
            ]"
            @click="determineUrl(item)"
        >
            {{ $t(item.i18_key) }}
        </span>
        <ModulesGameBadge
            size="small"
            :type="item?.type"
            class-wrapper="-right-4 top-1.5"
            class-image="w-[34px]"
        />
    </div>
</template>

<script setup>
defineProps({
    order: {
        type: String,
        default: '',
    },
    item: {
        type: Object,
        default: () => {},
    },
    showDropdown: {
        type: Function,
        default: () => {},
    },
    determineUrl: {
        type: Function,
        default: () => {},
    },
})

const { determineActiveRoute } = useMenu()
</script>
<style lang="scss" scoped>
.item-menu {
    &::before {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        height: 2px;
        width: 100%;
        transition: all 0.3s;
        background-color: transparent;
    }
    &:hover,
    &.is-active {
        &::before {
            background-color: #fff;
        }
    }
}
</style>
