<template>
    <component
        :is="headerLink?.isButton ? 'span' : 'NuxtLink'"
        :id="headerLink.url"
        class="relative inline-flex cursor-pointer items-center whitespace-nowrap border-white px-2.5 py-3 text-center text-xs font-semibold uppercase leading-4 xl:px-0 xl:py-7"
        :class="isRouteActive(headerLink) ? 'text-green-400' : 'text-slate-300'"
        :to="headerLink?.isButton ? undefined : headerLink?.url"
    >
        {{ $t(headerLink.i18_key) }}
        <img
            v-if="headerLink.label"
            class="absolute -right-2.5 top-2.5 w-[30px]"
            :src="`/assets/images/v2/tags/${headerLink.label}.webp`"
            :alt="headerLink.label"
        />
    </component>
    <div
        :id="headerLink.id"
        class="submenu content-submenu absolute top-full z-10 h-0 overflow-hidden bg-slate-900"
    >
        <div class="mx-auto flex min-w-[212px] flex-col justify-center rounded-xl text-sm">
            <div
                role="button"
                v-for="subMenu in headerLink.sub_menu"
                :key="subMenu.url"
                class="content-submenu__item flex cursor-pointer items-center gap-2 p-3 text-slate-300 hover:bg-slate-800 hover:text-white"
                @click.prevent="openSubLinkV2(subMenu)"
                :class="{
                    active: subMenu.url === $route.path,
                }"
            >
                <img
                    class="item-img h-5 w-5 transition-all duration-300 ease-in-out hover:scale-110 hover:brightness-110"
                    :src="`/assets/images/header/menu/${subMenu.id}.svg`"
                    :alt="'icon for ' + subMenu.title"
                    loading="lazy"
                />
                <div class="item-title text-sm capitalize">
                    {{ $t(subMenu.i18_key) }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useFrameStore } from '~/stores'
import { storeToRefs } from 'pinia'
import { useModalStore } from '~/stores'
import { useUserStore } from '~/stores'

const useModalStoreInstance = useModalStore()
const useUserStoreInstance = useUserStore()
const { showLoginModal, showUpdateFullnameModal } = storeToRefs(useModalStoreInstance)
const { isLoggedIn, user } = storeToRefs(useUserStoreInstance)

const { showMobile } = useCommon()

const props = defineProps(['headerLink'])
const { headerLink } = toRefs(props)

const useFrameStoreInstance = useFrameStore()
const { theLinkType } = storeToRefs(useFrameStoreInstance)

const route = useRoute()
const isRouteActive = item => {
    const res = item.sub_menu.find(e => e.link === theLinkType.value)
    return (
        item.url === route.path ||
        (res && (route.fullPath.includes(item.url) || route.path.includes(item.url)))
    )
}

const openSubLinkV2 = item => {
    if (item.loginRequired && !isLoggedIn.value) {
        showLoginModal.value = true
        return
    }
    if (user?.value?.is_updated_fullname === 0 && item?.isCheckFullName) {
        showUpdateFullnameModal.value = true
        return
    }
    // do nothing if we are on the same page
    if (theLinkType.value === item.link) return

    try {
        if (!showMobile.value) {
            // desktop
            navigateTo(item.url)
        } else {
            navigateTo('/')
        }
    } catch (error) {
        console.error('Error navigating to sub link:', error)
    }
}
</script>
