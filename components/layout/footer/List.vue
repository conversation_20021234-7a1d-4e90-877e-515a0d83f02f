<template>
    <ul class="flex flex-col items-start gap-[14px] text-sm font-medium">
        <li v-for="link in links" :key="link.i18_key" :data-key="link.i18_key">
            <nuxt-link
                :to="localePath(link.url)"
                class="text-slate-300 transition-all hover:opacity-80"
            >
                {{ $t(link.i18_key) }}
            </nuxt-link>
        </li>
    </ul>
</template>
<script setup>
defineProps({
    links: {
        type: Array,
        default: () => [],
    },
})

const localePath = useLocalePath()
</script>
