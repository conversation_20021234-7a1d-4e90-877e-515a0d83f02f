<template>
    <div class="w-full bg-slate-900">
        <div class="container">
            <div
                :class="[
                    'py-8',
                    {
                        'grid grid-cols-6 gap-5': !isShowShortenFooter,
                        'flex items-start justify-between gap-5': isShowShortenFooter,
                    },
                ]"
            >
                <LayoutFooterColWrapper
                    :class="!isShowShortenFooter ? 'col-span-2' : 'w-[calc(100%-348px)]'"
                    type="logo"
                >
                    <div class="flex w-full items-start justify-between">
                        <LayoutFooterSiteInfo v-if="!isCollapedQuickAccess" />
                        <LayoutFooterQuickAccess
                            v-if="isShowShortenFooter"
                            :links="quickLinks2"
                            :isCollapedQuickAccess="isCollapedQuickAccess"
                            @update:isCollapedQuickAccess="
                                isCollapedQuickAccess = !isCollapedQuickAccess
                            "
                        />
                    </div>
                </LayoutFooterColWrapper>

                <LayoutFooterColWrapper
                    v-if="!isShowShortenFooter"
                    class="col-span-1"
                    title="meta.gioi-thieu"
                >
                    <LayoutFooterList :links="infoLinks" />
                </LayoutFooterColWrapper>

                <LayoutFooterColWrapper
                    v-if="!isShowShortenFooter"
                    class="col-span-1"
                    title="quick_links.quick_links"
                >
                    <LayoutFooterList :links="quickLinks" />
                </LayoutFooterColWrapper>

                <LayoutFooterColWrapper
                    v-if="!isShowShortenFooter"
                    class="col-span-1"
                    title="meta.huong-dan-title"
                >
                    <LayoutFooterList :links="helpLinks" />
                </LayoutFooterColWrapper>

                <LayoutFooterColWrapper
                    :class="!isShowShortenFooter ? 'col-span-1' : 'flex-shrink-0'"
                    title="support.title"
                >
                    <LayoutFooterSocial />
                </LayoutFooterColWrapper>
            </div>
        </div>

        <LayoutFooterBank />

        <LayoutFooterCopyright />
    </div>
</template>
<script setup>
const quickLinks = [
    {
        id: 'promotions',
        url: '/khuyen-mai',
        title: 'promotions',
        i18_key: 'quick_links.promotions',
    },
    {
        id: 'events',
        url: '/su-kien',
        title: 'events',
        i18_key: 'quick_links.events',
    },
    {
        id: 'news',
        url: '/tin-tuc',
        title: 'news',
        i18_key: 'quick_links.news',
    },
    {
        id: 'bong-da',
        url: '/bong-da/ty-le-keo-bong-da',
        title: 'Tỷ lệ kèo',
        i18_key: 'pages.ty-le-keo-bong-da',
    },
]

const quickLinks2 = [
    {
        id: 'promotions',
        url: '/khuyen-mai',
        title: 'promotions',
        i18_key: 'quick_links.promotions',
    },
    {
        id: 'events',
        url: '/su-kien',
        title: 'events',
        i18_key: 'quick_links.events',
    },
    {
        id: 'news',
        url: '/tin-tuc',
        title: 'news',
        i18_key: 'quick_links.news',
    },
    {
        id: 'bong-da',
        url: '/bong-da/ty-le-keo-bong-da',
        title: 'Tỷ lệ kèo',
        i18_key: 'pages.ty-le-keo-bong-da',
    },
    {
        id: 'huong-dan-nap-tien',
        url: '/huong-dan-nap-tien',
        i18_key: 'supports.deposit',
    },
    {
        id: 'huong-dan-rut-tien',
        url: '/huong-dan-rut-tien',
        i18_key: 'supports.withdrawal',
    },
    {
        url: '/chinh-sach-bao-mat',
        i18_key: 'quick_links.privacy_policy',
    },
    {
        url: '/dieu-khoan-va-dieu-kien',
        i18_key: 'quick_links.terms_and_conditions',
    },
]

const infoLinks = [
    {
        url: '/gioi-thieu',
        i18_key: 'quick_links.about_us',
    },
    {
        url: '/chinh-sach-bao-mat',
        i18_key: 'quick_links.privacy_policy',
    },
    {
        url: '/dieu-khoan-va-dieu-kien',
        i18_key: 'quick_links.terms_and_conditions',
    },
]

const helpLinks = [
    {
        id: 'huong-dan-giao-dich',
        url: '/huong-dan-giao-dich',
        i18_key: 'supports.p2p',
    },
    {
        id: 'huong-dan-dang-ky',
        url: '/huong-dan-dang-ky',
        i18_key: 'supports.registration',
    },
    {
        id: 'huong-dan-nap-tien',
        url: '/huong-dan-nap-tien',
        i18_key: 'supports.deposit',
    },
    {
        id: 'huong-dan-rut-tien',
        url: '/huong-dan-rut-tien',
        i18_key: 'supports.withdrawal',
    },
]

const { isShowShortenFooter } = useCommon()

const isCollapedQuickAccess = ref(false)
</script>
