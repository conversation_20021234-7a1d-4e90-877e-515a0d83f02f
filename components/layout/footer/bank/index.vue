<template>
    <div class="bank">
        <div class="container">
            <div class="flex">
                <Swiper
                    class="swiper mb-4 w-full max-xl:!-mx-3 max-xl:!px-3"
                    :modules="[SwiperAutoplay]"
                    :slides-per-view="'auto'"
                    :speed="5000"
                    :free-mode="true"
                    :loop="true"
                    :autoplay="{
                        delay: 1,
                        disableOnInteraction: false,
                    }"
                >
                    <SwiperSlide
                        v-for="(group, groupIndex) in chunkArray(
                            banks,
                            !isShowShortenFooter ? 2 : 1
                        )"
                        :key="groupIndex"
                        :class="['group-item !w-[144px]', !isShowShortenFooter ? '!h-[124px]' : '']"
                    >
                        <div class="flex flex-col gap-6">
                            <span
                                v-for="bank in group"
                                :key="bank"
                                href="#"
                                class="bank-item h-[50px] w-[144px] overflow-hidden rounded"
                            >
                                <img
                                    :src="`${staticUrl}/assets/images/v2/banks/${bank.toLocaleLowerCase()}.webp`"
                                    alt="bank-title"
                                    class="h-full"
                                />
                            </span>
                        </div>
                    </SwiperSlide>
                </Swiper>
            </div>
        </div>
    </div>
</template>
<script setup>
import { chunkArray } from '~/utils/helper'
const staticUrl = useRuntimeConfig().public.staticUrl
const banks = [
    'kdg',
    'usdt',
    'viettel',
    'mobifone',
    'vinaphone',
    'vietnamobile',
    'baovietbank',
    'momo',
    'viettelmoney',
    'cbbank',
    'vcb',
    'acb',
    'sacombank',
    'mbbank',
    'techcombank',
    'bidv',
    'msb',
    'shb',
    'tpbank',
    'vpbank',
    'agribank',
    'hdbank',
    'vietinbank',
    'scb',
    // 'vietcapitalbank',
    'vikki',
    'pvcom',
    'seabank',
    'abbank',
    'pgbank',
    'ocb',
    'eximbank',
    'lpbank',
    'baca',
    'bvbank',
    'hsbc',
    'ivb',
    'kienlong',
    'nama',
    'ncb',
    'ocean',
    'public',
    'saigonbank',
    'sinhanbank',
    'standardchartered',
    'vib',
    'vietabank',
    'woori',
    'kdg',
]

const { isShowShortenFooter } = useCommon()
</script>
<style lang="scss" scoped>
.group-item {
    @apply ml-6 first-of-type:ml-0;
}
</style>
