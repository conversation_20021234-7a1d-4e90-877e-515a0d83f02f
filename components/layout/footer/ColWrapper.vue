<template>
    <section :class="['foolter-col flex gap-6', !isShowShortenFooter ? 'flex-col' : 'flow-row']">
        <div v-if="type === 'default'" class="foolter-col-title">
            {{ $t(title) }}
        </div>
        <div :class="['footer-col-body w-full', !isShowShortenFooter ? '' : 'flex items-center']">
            <slot></slot>
        </div>
    </section>
</template>
<script setup>
defineProps({
    title: {
        type: String,
        default: '',
    },
    type: {
        type: String,
        default: 'default',
    },
})

const { isShowShortenFooter } = useCommon()
</script>
<style lang="scss" scoped>
.foolter-col-title {
    @apply relative whitespace-nowrap pb-2 text-xl font-semibold text-white;
    &::after {
        @apply absolute bottom-0 left-0 block h-[2px] w-9 rounded-sm bg-green-400 content-[''];
    }
}
</style>
