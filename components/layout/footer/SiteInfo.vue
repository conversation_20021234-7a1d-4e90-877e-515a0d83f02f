<template>
    <div :class="['site-info flex flex-col items-start gap-4']">
        <NuxtLink :to="localePath('/')">
            <img :src="`/assets/images/logo.svg?v=1`" class="h-10" alt="logo" />
        </NuxtLink>
        <div :class="['text-sm text-slate-400', !isShowShortenFooter ? 'w-auto' : 'w-[490px]']">
            {{
                $t('footer.site_info', {
                    brandName,
                })
            }}
        </div>
        <div v-if="!isShowShortenFooter" class="flex flex-col gap-8">
            <div class="flex items-center gap-8">
                <img
                    :src="'/assets/images/v2/home/<USER>'"
                    width="158"
                    height="82"
                    alt=""
                />
                <img
                    :src="'/assets/images/v2/home/<USER>'"
                    width="96"
                    height="82"
                    alt=""
                />
            </div>
            <div class="flex items-center gap-6">
                <img
                    :src="'/assets/images/v2/home/<USER>'"
                    width="216"
                    height="42"
                    alt=""
                />
                <img
                    :src="'/assets/images/v2/home/<USER>'"
                    width="36"
                    height="36"
                    alt=""
                />
            </div>
        </div>
    </div>
</template>
<script setup>
const brandName = useRuntimeConfig().public.BRAND_NAME
const localePath = useLocalePath()

const { isShowShortenFooter } = useCommon()
</script>
