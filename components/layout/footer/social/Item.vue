<template>
    <div
        :class="[
            'relative flex cursor-pointer items-center text-white',
            bg,
            !isShowShortenFooter
                ? 'w-full justify-start gap-2 rounded-lg px-5 py-2.5'
                : 'h-9 w-9 justify-center rounded-full',
        ]"
        @click="action"
    >
        <img :src="icon" class="h-5 w-5" alt="" />
        <span v-if="!isShowShortenFooter" class="whitespace-nowrap text-xs font-semibold">
            {{ $t(title) }}
        </span>
    </div>
</template>
<script setup>
defineProps({
    action: {
        type: Function,
        default: () => {},
    },
    bg: {
        type: String,
        default: '',
    },
    icon: {
        type: Object,
        default: () => ({}),
    },
    title: {
        type: String,
        default: '',
    },
})

const { isShowShortenFooter } = useCommon()
</script>
