<template>
    <div :class="['flex gap-3', !isShowShortenFooter ? 'flex-col' : 'flex-row']">
        <LayoutFooterSocialItem
            v-for="social in socials"
            :key="social.title"
            :title="social.title"
            :icon="social.icon"
            :bg="social.backgroundColor"
            :action="social.action"
        />
    </div>
</template>
<script setup>
const { MESSENEGER_LINK, TELEGRAM_OFFICIAL, TELEGRAM_CSKH } = useRuntimeConfig().public

const { isShowShortenFooter } = useCommon()

const openNewTab = link => {
    const openNewTab = window.open('about:blank', '_blank')
    if (openNewTab) {
        openNewTab.location.href = link
    }
}

const localePath = useLocalePath()

const router = useRouter()

const socials = shallowRef([
    {
        title: 'footer.title.livechat',
        icon: '/assets/images/v2/home/<USER>',
        backgroundColor: 'bg-[#162943]',
        action: () => window.LiveChatWidget.call('maximize'),
    },
    {
        title: 'footer.title.telegram',
        icon: '/assets/images/v2/home/<USER>',
        backgroundColor: 'bg-[#162943]',
        action: () => openNewTab(TELEGRAM_CSKH),
    },
    {
        title: 'footer.title.telegram_official',
        icon: '/assets/images/v2/home/<USER>',
        backgroundColor: 'bg-[#162943]',
        action: () => openNewTab(TELEGRAM_OFFICIAL),
    },
    {
        title: 'footer.title.promotion',
        icon: '/assets/images/v2/home/<USER>',
        backgroundColor: 'bg-[#162943]',
        action: () => router.push(localePath('/khuyen-mai')),
    },
    {
        title: 'footer.title.messenger',
        icon: '/assets/images/v2/home/<USER>',
        backgroundColor: 'bg-[#162943]',
        action: () => openNewTab(MESSENEGER_LINK),
    },
])
</script>
