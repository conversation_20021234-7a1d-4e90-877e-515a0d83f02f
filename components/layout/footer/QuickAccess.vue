<template>
    <section :class="['flex gap-6', !isCollapedQuickAccess ? '' : 'w-full']">
        <div
            class="foolter-col-title cursor-pointer whitespace-nowrap"
            @click="emit('update:isCollapedQuickAccess')"
        >
            {{ $t('quick_links.quick_links') }}
        </div>
        <Swiper
            v-if="isCollapedQuickAccess"
            class="z-0 w-[calc(100%-172px)] lg:!ml-0 lg:!p-0 lg:!py-0 lg:!pl-0"
            slides-per-view="auto"
        >
            <SwiperSlide
                v-for="link in links"
                :key="link.i18_key"
                :data-key="link.i18_key"
                class="ml-3 !w-fit first-of-type:ml-0"
            >
                <nuxt-link
                    :to="localePath(link.url)"
                    class="inline-block h-10 whitespace-nowrap leading-10 text-slate-300 transition-all hover:opacity-80"
                >
                    {{ $t(link.i18_key) }}
                </nuxt-link>
            </SwiperSlide>
        </Swiper>
    </section>
</template>
<script setup>
defineProps({
    isCollapedQuickAccess: {
        type: Boolean,
        default: false,
    },
    links: {
        type: Array,
        default: () => [],
    },
})

const emit = defineEmits(['update:isCollapedQuickAccess'])
</script>
<style lang="scss" scoped>
.foolter-col-title {
    @apply relative whitespace-nowrap pb-2 text-xl font-semibold text-white;
    &::after {
        @apply absolute bottom-0 left-0 block h-[2px] w-9 rounded-sm bg-green-400 content-[''];
    }
}
</style>
