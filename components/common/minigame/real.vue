<script setup>
import { storeToRefs } from 'pinia'
import { useWindowSize } from '@vueuse/core'
import { useUserStore } from '~/stores'
import { useNotify } from '~/composables/use-notify'

const { width } = useWindowSize()

const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)

const { showMobile } = useCommon()
const isLoading = ref(false)
const useModalStoreInstance = useModalStore()
const { showGameDenyModal } = useNotify()
const { showUpdateFullnameModal } = storeToRefs(useModalStoreInstance)
const config = useRuntimeConfig()
const partner = config.public.BRAND_NAME.toLowerCase()
const minigameUrl = config.public.MINIGAME_URL

const KM_100 = 'WELCOME'
const startMiniGame = () => {
    const miniGame = document.querySelector('c2-minigame')
    if (miniGame) {
        const dragItem = miniGame.querySelectorAll('.c2-mn-hover')
        if (dragItem) {
            dragItem.forEach(item => {
                const imgElements = item.querySelectorAll('img')
                imgElements.forEach(img => {
                    img.addEventListener('click', function (e) {
                        const overlay = Array.from(miniGame.querySelectorAll('div')).find(div => {
                            const style = div.getAttribute('style') || ''
                            return style.includes('background: transparent')
                        })
                        if (user.value?.package_id && user.value?.package_id !== 1) {
                            e.stopPropagation()
                            e.preventDefault()

                            if (overlay) {
                                overlay.click()
                            }
                            showGameDenyModal()
                        } else if (user.value?.is_updated_fullname === 0) {
                            e.stopPropagation()
                            e.preventDefault()

                            if (overlay) {
                                overlay.click()
                            }

                            showUpdateFullnameModal.value = true
                        }
                    })
                })
            })
        }
    }
}
const forceRerender = () => {
    isLoading.value = false
    const scriptId = 'vergopjt-script'
    const scriptList = document.querySelectorAll("script[type='text/javascript']")
    const convertedNodeList = Array.from(scriptList)
    const testScript = convertedNodeList.find(script => script.id === scriptId)
    if (testScript !== undefined) {
        testScript.parentNode.removeChild(testScript)
    }

    const script = document.createElement('script')
    script.id = scriptId
    script.type = 'text/javascript'
    script.src = minigameUrl
    document.body.appendChild(script)
    isLoading.value = true

    //// Remove viewport on ipad and desktop
    if (showMobile.value) {
        return
    }
    setTimeout(() => {
        const listHead = document.getElementsByTagName('head')
        if (listHead.length < 0) {
            return
        }
        const head = listHead[0]
        const viewport = head.querySelector("[name='viewport']")
        head.removeChild(viewport)
        startMiniGame()
    }, 5000)
}

const minigameToken = computed(() => {
    let token = ''
    if (user.value) {
        token = user.value.tp_token
        if (user.value?.plan?.type === KM_100) {
            token += '1000'
        }
    }
    return token
})

const position = ref('')

const handlePositionMiniGame = windowWith => {
    let compensateWidth = 130
    const bottom = 100
    if (windowWith <= 768) {
        compensateWidth = 90
    }

    const left = windowWith - compensateWidth

    return `auto auto ${bottom}px ${left}px`
}

watch(width, newW => {
    document.querySelector('c2-minigame [draggable="true"]').style.inset =
        handlePositionMiniGame(newW)
})

onMounted(() => {
    forceRerender()
    position.value = handlePositionMiniGame(window.innerWidth)
    nextTick(() => {
        startMiniGame()
    })
})
</script>

<template>
    <div class="relative z-[54]">
        <c2-minigame
            v-if="isLoading"
            :pos="position"
            :token="minigameToken"
            :partner="partner"
        ></c2-minigame>
    </div>
</template>
