<template>
    <div v-if="showTouch" ref="draggable" class="draggable-icon mini-game">
        <img :src="'/assets/images/minigame/bg.png'" style="width: 100%; height: 100%" />
        <img
            :src="'/assets/images/close.png'"
            class="btn-close"
            @click.stop="handleCloseTouch"
            @touchend.stop
            @mouseup.stop
        />
        <img :src="'/assets/images/minigame/mini.png'" class="mini ping" />
        <img
            :src="'/assets/images/minigame/game.png'"
            class="game-text ping"
            style="animation-delay: 800ms"
        />
    </div>
</template>

<script setup lang="ts">
import { MODAL_TYPE } from '~/constants/index'
import { useModalStore, useShowErrorStore, useUserStore } from '~/stores'
import { storeToRefs } from 'pinia'
import { useWindowSize } from '@vueuse/core'

const { width } = useWindowSize()

const useUserStoreInstance = useUserStore()
const useModalStoreInstance = useModalStore()
const useShowErrorStoreInstance = useShowErrorStore()

const { t } = useI18n()
const { showMobile } = useCommon()
const { onShowErrorModal } = useShowErrorStoreInstance
const { isLoggedIn } = storeToRefs(useUserStoreInstance)
const { showLoginModal } = storeToRefs(useModalStoreInstance)
const { dragEnd, drag, dragStart, draggable } = useDrag()

const showTouch = ref(true)

const handleCloseTouch = () => {
    showTouch.value = false
    draggable.value?.removeEventListener('touchend', dragEnd)
    document.removeEventListener('mouseup', dragEnd)
    document.removeEventListener('touchmove', drag)
    document.removeEventListener('mousemove', drag)
}

const handleClick = (event: MouseEvent | TouchEvent) => {
    if (!isLoggedIn.value) {
        showLoginModal.value = true
    } else {
        onShowErrorModal({
            type: MODAL_TYPE.ERROR,
            title: t('error_modal.deny_info.title'),
            message: t('error_modal.deny_info.message'),
            confirmButton: t('error_modal.deny_info.confirm_button'),
        })
    }
    event.preventDefault()
}

const handlePositionMiniGame = windowWith => {
    if (draggable.value) {
        let compensateWidth = 130
        const bottom = 100
        if (windowWith <= 768) {
            compensateWidth = 90
        }

        const left = windowWith - compensateWidth

        draggable.value.style.left = `${left}px`
        draggable.value.style.bottom = `${bottom}px`
    }
}

watch(width, newW => {
    handlePositionMiniGame(newW)
})

onMounted(() => {
    if (draggable.value) {
        handlePositionMiniGame(window.innerWidth)

        // Mouse Events
        draggable.value.addEventListener('mousedown', dragStart)

        // Touch Events
        draggable.value.addEventListener('touchstart', dragStart)
    }
})

function useDrag() {
    const draggable = ref<HTMLDivElement | null>(null)
    const isDragging = ref(false)
    const startX = ref<number>()
    const startY = ref<number>()
    const startLeft = ref<number>()
    const startTop = ref<number>()

    const dragStart = (e: MouseEvent | TouchEvent) => {
        if (!draggable.value && isDragging.value) return

        isDragging.value = true

        const rect = draggable.value?.getBoundingClientRect()
        startLeft.value = rect?.left
        startTop.value = rect?.top

        if (e.type === 'touchstart') {
            startX.value = (e as TouchEvent).touches[0].clientX
            startY.value = (e as TouchEvent).touches[0].clientY
        } else {
            startX.value = (e as MouseEvent).clientX
            startY.value = (e as MouseEvent).clientY
        }

        draggable.value?.addEventListener('touchend', dragEnd)
        document.addEventListener('mouseup', dragEnd)
        document.addEventListener('mousemove', drag, { passive: false })
        document.addEventListener('touchmove', drag, { passive: false })
    }

    const dragEnd = async (e: MouseEvent | TouchEvent) => {
        isDragging.value = false

        await nextTick()
        const elementRect = draggable.value?.getBoundingClientRect()
        const targetX = elementRect?.x || startLeft.value!
        const targetY = elementRect?.y || startTop.value!
        const hasModalOpen = document.body.classList.contains('modal-open')
        if (
            !hasModalOpen &&
            startLeft.value! - targetX === 0 &&
            startTop.value! - targetY === 0 &&
            showTouch.value &&
            !isDragging.value
        ) {
            handleClick(e)
        }

        draggable.value?.removeEventListener('touchend', dragEnd)
        document.removeEventListener('mouseup', dragEnd)
        document.removeEventListener('touchmove', drag)
        document.removeEventListener('mousemove', drag)
    }

    const drag = (e: MouseEvent | TouchEvent) => {
        if (!isDragging.value) return
        e?.preventDefault()

        let pointerX, pointerY

        if (e.type === 'touchmove') {
            pointerX = (e as TouchEvent).touches[0].clientX
            pointerY = (e as TouchEvent).touches[0].clientY
        } else {
            pointerX = (e as MouseEvent).clientX
            pointerY = (e as MouseEvent).clientY
        }

        let newLeft = (startLeft?.value ?? 0) + (pointerX - (startX.value ?? 0))
        let newTop = (startTop?.value ?? 0) + (pointerY - (startY.value ?? 0))

        const windowWidth = window.innerWidth
        const windowHeight = window.innerHeight
        const elementRect = draggable.value?.getBoundingClientRect()
        const scrollX = showMobile.value ? 0 : 20
        newLeft = Math.min(Math.max(0, newLeft), windowWidth - scrollX - (elementRect?.width ?? 0))
        newTop = Math.min(Math.max(0, newTop), windowHeight - (elementRect?.height ?? 0))
        const newBottom = Math.min(
            Math.max(0, windowHeight - newTop - (elementRect?.height ?? 0)),
            windowHeight - (elementRect?.height ?? 0)
        )

        if (draggable.value) {
            draggable.value.style.left = `${newLeft}px`
            draggable.value.style.bottom = `${newBottom}px`
        }
    }

    return {
        draggable,
        dragStart,
        dragEnd,
        drag,
        isDragging,
        startLeft,
        startTop,
    }
}
</script>

<style scoped lang="scss">
.mini-game {
    position: fixed;
    width: 100px;
    cursor: pointer;
    z-index: 53;

    @media (max-width: 1200px) {
        width: 80px;
    }
}

.mini {
    height: 30%;
    position: absolute;
    top: 35%;
    left: -5%;
}

.game-text {
    height: 22%;
    position: absolute;
    top: 60%;
    left: 5%;
}

img:not(.btn-close) {
    pointer-events: none;
    user-drag: none;
    user-select: none;
}

.ping {
    animation: ping 5s cubic-bezier(0.15, 0.55, 0.15, 0.5) infinite;
}

@keyframes ping {
    0% {
        transform: scale(1);
    }
    12.5% {
        transform: scale(1.2);
    }
    25% {
        transform: scale(1);
    }
    27.5% {
        transform: scale(1);
    }
    50% {
        transform: scale(1);
    }
    62.5% {
        transform: scale(1);
    }
    75% {
        transform: scale(1);
    }
    87.5% {
        transform: scale(1);
    }
    100% {
        transform: scale(1);
    }
}
</style>
