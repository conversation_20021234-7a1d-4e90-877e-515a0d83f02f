<script lang="ts" setup>
import { storeToRefs } from 'pinia'

import { useUserStore, useDepositStore } from '~/stores'
import { DepositPromotion } from '~/constants/deposit'
import { useNotify } from '~/composables/use-notify'

const useUserStoreInstance = useUserStore()
const { isLoggedIn, user } = storeToRefs(useUserStoreInstance)
const useDepositInstance = useDepositStore()
const { plan } = storeToRefs(useDepositInstance)
const useModalStoreInstance = useModalStore()
const { showGameDenyModal } = useNotify()
const { showUpdateFullnameModal } = storeToRefs(useModalStoreInstance)
const isShowRealIcon = computed(() => {
    return (
        isLoggedIn.value && (plan.value?.type !== DepositPromotion.WELCOME || plan.value === null)
    )
})
watch(
    isLoggedIn,
    value => {
        if (value) {
            ;(async () => {
                await useDepositInstance.getWelcomeCommission()
            })()
        }
    },
    {
        immediate: true,
    }
)
watch(isShowRealIcon, () => {
    setTimeout(() => {
        startMiniGame()
    }, 0)
})
const startMiniGame = () => {
    const miniGame = document.querySelector('c2-minigame')
    if (miniGame) {
        const dragItem = miniGame.querySelectorAll('.c2-mn-hover')
        if (dragItem) {
            dragItem.forEach(item => {
                const imgElements = item.querySelectorAll('img')
                imgElements.forEach(img => {
                    img.addEventListener('click', function (e) {
                        const overlay = Array.from(miniGame.querySelectorAll('div')).find(div => {
                            const style = div.getAttribute('style') || ''
                            return style.includes('background: transparent')
                        })
                        if (user.value?.package_id && user.value?.package_id !== 1) {
                            e.stopPropagation()
                            e.preventDefault()

                            if (overlay) {
                                overlay.click()
                            }
                            showGameDenyModal()
                        } else if (user.value?.is_updated_fullname === 0) {
                            e.stopPropagation()
                            e.preventDefault()

                            if (overlay) {
                                overlay.click()
                            }

                            showUpdateFullnameModal.value = true
                        }
                    })
                })
            })
        }
    }
}
onMounted(() => {
    setTimeout(() => {
        startMiniGame()
    }, 0)
})
</script>
<template>
    <div v-if="isShowRealIcon">
        <CommonMinigameReal />
    </div>
    <div v-else>
        <CommonMinigameFake />
    </div>
</template>
