<template>
    <main
        :class="[
            'page relative',
            isSupportIframe ? '' : 'lg:pb-20',
            {
                [bgWrapper]: true,
            },
        ]"
    >
        <CommonPageTitle />
        <div :class="[isSupportIframe ? '' : 'container']">
            <CommonBreadcrumb
                v-if="!showMobile"
                :class="[isSupportIframe ? 'hidden' : 'mb-5 max-lg:hidden']"
            />
            <slot></slot>
        </div>
    </main>
</template>
<script setup>
defineProps({
    isLoading: {
        type: Boolean,
        default: true,
    },
    bgWrapper: {
        type: String,
        default: '',
    },
    isSupportIframe: {
        type: Boolean,
        default: false,
    },
})

const { showMobile } = useCommon()
</script>
<style lang="scss" scoped>
.page {
    min-height: calc(100dvh - 533px);
    @media screen and (max-width: 767px) {
        min-height: calc(100dvh - 162px);
    }
}
</style>
