<template>
    <h1 v-if="pageTitle" class="hidden" :data-key="`pages${i18nName}.h1`">
        {{ pageTitle }}
    </h1>
</template>
<script setup>
const route = useRoute()
const i18nName = computed(() => {
    return route.path.replace(/\//g, '.') || 'games'
})
const { t } = useI18n()
const pageTitle = computed(() => {
    return t(`pages${i18nName.value}.h1`) !== `pages${i18nName.value}.h1`
        ? t(`pages${i18nName.value}.h1`)
        : ''
})
</script>
