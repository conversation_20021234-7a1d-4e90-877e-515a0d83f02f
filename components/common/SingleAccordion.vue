<template>
    <div class="accordion flex w-full flex-col gap-4">
        <div :class="`accordion-item overflow-hidden rounded-2xl border ${borderColor} ${bgColor}`">
            <div
                :class="[
                    'flex cursor-pointer items-center justify-between gap-2 rounded-lg px-3 py-[10px]',
                ]"
                @click="toggle"
            >
                <div class="flex items-center gap-2">
                    <div class="accordion-icon flex h-8 w-8 items-center justify-center rounded-lg">
                        <component :is="iconComponent" />
                    </div>
                    <div>
                        <h3 class="mb-[2px] text-sm font-medium text-white">
                            {{ $t(title) }}
                        </h3>
                        <h4
                            v-if="subTitle"
                            class="text-[12px] font-normal leading-[18px] text-grey-900"
                        >
                            {{ $t(subTitle) }}
                        </h4>
                    </div>
                </div>
                <IconsChevronDown
                    :class="[
                        'flex-shrink-0 transition-all duration-300',
                        { 'rotate-180': isActiveIndex },
                    ]"
                    :fill-color="colorIcon"
                />
            </div>

            <transition name="accordion">
                <div v-show="isActiveIndex" class="overflow-hidden rounded-b-lg px-3 pb-4 pt-[6px]">
                    <slot></slot>
                </div>
            </transition>
        </div>
    </div>
</template>

<script setup>
const { defaultOpen } = defineProps({
    title: {
        type: String,
        default: '',
    },
    subTitle: {
        type: String,
        default: '',
    },
    iconComponent: {
        type: Object,
        required: true,
    },
    defaultOpen: {
        type: Boolean,
        default: false,
    },
    classTitle: {
        type: String,
        default: '',
    },
    classSubTitle: {
        type: String,
        default: '',
    },
    colorIcon: {
        type: String,
        default: 'fill-grey-500',
    },
    bgColor: {
        type: String,
        default: 'bg-slate-800',
    },
    borderColor: {
        type: String,
        default: 'border-slate-800',
    },
})

const isActiveIndex = ref(defaultOpen)

const toggle = () => {
    isActiveIndex.value = !isActiveIndex.value
}

watch(
    () => defaultOpen,
    value => {
        isActiveIndex.value = value
    }
)
</script>

<style scoped>
.accordion-enter-active,
.accordion-leave-active {
    transition:
        max-height 0.3s ease-in-out,
        padding 0.3s ease-in-out;
}
.accordion-enter, .accordion-leave-to /* .accordion-leave-active in <2.1.8 */ {
    max-height: 0;
    padding: 0;
}
</style>
