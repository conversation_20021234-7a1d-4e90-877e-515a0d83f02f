<template>
    <div class="relative">
        <label
            :for="name + '_id'"
            :class="[
                'absolute left-3 top-0 z-1 -translate-y-1/2 rounded px-1 text-left text-sm font-medium text-white',
                labelClass,
            ]"
        >
            {{ label }}</label
        >
        <div class="relative">
            <input
                class="block w-full appearance-none rounded-xl border border-slate-800 px-2.5 pb-3.5 pl-4 pt-4 text-sm text-white placeholder:text-slate-300 focus:border-slate-800 focus:ring-0"
                :class="`${classes} ${
                    disabled ? '!border-slate-750 bg-slate-800' : 'bg-slate-950'
                }`"
                autocomplete="off"
                :name="name"
                :id="name + '_id'"
                :type="type"
                :value="inputValue"
                :placeholder="placeholder"
                @input="onChange"
                @blur="handleBlur"
                :disabled="disabled"
                :readonly="readonly"
                :maxlength="maxlength"
            />
            <p
                v-if="errorMessage"
                class="absolute left-0 top-full mt-[2px] w-full text-xs text-red-700"
            >
                {{ $t(errorMessage) }}
            </p>
            <div v-if="paste" class="absolute inset-y-0 right-0 flex items-center pr-1.5">
                <NuxtLink
                    class="text-grey-950 cursor-pointer rounded-md bg-slate-400 px-2.5 py-1.5 text-center text-xs font-medium"
                    @click="handlePaste"
                >
                    {{ $t('common.paste') }}
                </NuxtLink>
            </div>
            <div v-if="copy" class="absolute inset-y-0 right-0 flex items-center pr-1.5">
                <NuxtLink
                    class="text-grey-950 cursor-pointer rounded-md bg-slate-400 px-2.5 py-1.5 text-center text-xs font-normal italic"
                    @click="handleCopy"
                >
                    {{ copied ? $t('common.copied') : $t('common.copy') }}
                </NuxtLink>
            </div>
            <div v-if="confirmed" class="absolute inset-y-0 right-0 flex items-center pr-1.5">
                <IconsCheck />
            </div>
            <div v-if="money" class="absolute inset-y-0 right-0 flex items-center pr-1.5">
                <div class="text-right text-sm font-medium text-grey-900">{{ moneyValue }} VND</div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { toRef } from 'vue'
import { useField } from 'vee-validate'
import { useClipboard } from '@vueuse/core'

const props = defineProps({
    type: {
        type: String,
        default: 'text',
    },
    value: {
        type: String,
        default: '',
    },
    name: {
        type: String,
        required: true,
    },
    label: {
        type: String,
        required: true,
    },
    labelClass: {
        type: String,
        default: 'bg-slate-950',
    },
    copy: {
        type: Boolean,
        default: false,
    },
    paste: {
        type: Boolean,
        default: false,
    },
    confirmed: {
        type: Boolean,
        default: false,
    },
    successMessage: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '',
    },
    classes: {
        type: String,
        default: '',
    },
    labelclasses: {
        type: String,
        default: '',
    },
    maxlength: {
        type: Boolean,
        default: false,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    money: {
        type: Boolean,
        default: false,
    },
})
const name = toRef(props, 'name')
const theValue = toRef(props, 'value')

const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
    // meta,
} = useField(name, undefined, {
    initialValue: props.value,
})

watch(
    () => theValue.value,
    // eslint-disable-next-line
    (newVal, _) => {
        if (newVal) {
            inputValue.value = newVal
        }
    }
)

const onChange = event => {
    let newValue = event.target.value
    if (props.type === 'tel') {
        newValue = event.target.value.replace(/\D+/g, '')
    }
    if (props.isFormatNumber) {
        newValue = NumberUtils.formatNumberWithDots(Number(newValue?.replace(/\,/g, '')))
    }
    event.target.value = props.isUppercase ? newValue.toUpperCase() : newValue
    handleChange(event)
}

const moneyValue = computed(() => {
    if (inputValue.value) {
        return new Number(inputValue.value * 1000).toLocaleString()
    } else {
        return 0
    }
})
const { copy: copyValue, copied, isSupported } = useClipboard()
const handleCopy = () => {
    if (inputValue.value && isSupported) {
        copyValue(inputValue.value)
    }
}
const handlePaste = async () => {
    try {
        const permission = await navigator.permissions.query({
            name: 'clipboard-read',
        })
        if (permission.state === 'denied') {
            throw new Error('Not allowed to read clipboard.')
        } else {
            const clipboardContents = (await navigator.clipboard.readText()) || ''
            inputValue.value = props.maxlength
                ? clipboardContents.slice(0, props.maxlength)
                : clipboardContents
        }
    } catch (error) {
        console.error('Failed to read clipboard contents: ', error)
        const textarea = document.createElement('textarea')
        document.body.appendChild(textarea)
        textarea.select()
        document.execCommand('paste')
        inputValue.value = textarea.value
        document.body.removeChild(textarea)
    }
}
</script>

<style>
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type='number'] {
    -moz-appearance: textfield;
}
</style>
