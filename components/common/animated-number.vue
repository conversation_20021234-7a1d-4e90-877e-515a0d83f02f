<template>
    <span
        v-bind="$attrs"
        :style="{ minWidth: number.toString().length + 1 + 'ch' }"
        :data-number="`${numberCounter}${suffix}`"
    >
        <span v-if="prefix">{{ prefix }}</span>
        <span class="number-counter">{{ numberCounter }}</span>
        <strong v-if="suffix" class="ml-1">{{ suffix }}</strong>
    </span>
</template>

<script lang="ts" setup>
import { NumberUtils } from '~/utils'
const props = defineProps({
    number: {
        type: Number,
        default: 0,
    },
    suffix: {
        type: String,
        default: '',
    },
    prefix: {
        type: String,
        default: '',
    },
    previousNumber: {
        type: Number,
        default: 0,
    },
    animationDuration: {
        type: Number,
        default: 14000,
    },
})

const numberCounter = ref<string>('0')
let animationStartTime: number | null = null
let animationFrameId: number | null = null
let previousNumber = props.previousNumber
let lastUpdateTime = 0

const animateNumber = (timestamp: number): void => {
    const animationDelay = 120
    if (timestamp - lastUpdateTime >= animationDelay) {
        lastUpdateTime = timestamp
        if (animationStartTime === null) {
            animationStartTime = timestamp
        }

        const progress = Math.min(1, (timestamp - animationStartTime) / props.animationDuration)
        const newCounterValue = Math.round(
            previousNumber + (props.number - previousNumber) * progress
        )

        numberCounter.value = NumberUtils.formatNumberWithCommas(
            isNaN(newCounterValue) ? 0 : newCounterValue
        )

        if (progress < 1) {
            animationFrameId = requestAnimationFrame(animateNumber)
        } else {
            numberCounter.value = NumberUtils.formatNumberWithCommas(Math.round(props.number))
        }
    } else {
        animationFrameId = requestAnimationFrame(animateNumber)
    }
}

onMounted(() => {
    previousNumber = props.previousNumber
    animationFrameId = requestAnimationFrame(animateNumber)
})

onUnmounted(() => {
    if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId)
    }
})

watch(
    () => props.number,
    () => {
        previousNumber = parseFloat(numberCounter.value.replace(/,/g, ''))
        animationStartTime = null
        if (animationFrameId !== null) {
            cancelAnimationFrame(animationFrameId)
        }
        animationFrameId = requestAnimationFrame(animateNumber)
    }
)
</script>
<style lang="scss" scoped>
.number-counter {
    font-variant-numeric: tabular-nums;
}
</style>
