<template>
    <div class="common-input relative" :class="className || ''">
        <div class="relative flex flex-col justify-start gap-[2px]">
            <div class="relative" :class="{ 'mb-5': errorMessage }">
                <div class="relative">
                    <label
                        v-if="label"
                        :for="name + '_id'"
                        :class="[
                            'absolute left-3 top-0 z-1 -translate-y-1/2 rounded px-1 text-left text-sm font-medium text-white',
                            labelClass,
                        ]"
                    >
                        {{ label }}</label
                    >

                    <input
                        v-bind="$attrs"
                        :class="[
                            'block w-full appearance-none rounded-lg border px-2.5 pb-3.5 pl-4 pt-4 text-sm text-white placeholder:text-slate-300 focus:ring-0',
                            'block w-full appearance-none rounded-lg border px-2.5 pb-3.5 pl-4 pt-4 text-sm text-white placeholder:text-slate-300 focus:ring-0',
                            classes,
                            {
                                '!border-slate-750 bg-slate-800': disabled,
                                'bg-slate-950': !disabled,
                                'text-left': hasNumberControl,
                                'border-slate-700 focus:border-slate-700': !errorMessage,
                                'border-red-700 focus:border-red-700': errorMessage,
                            },
                        ]"
                        autocomplete="off"
                        :name="name"
                        :id="name + '_id'"
                        :type="type"
                        :value="inputValue"
                        :placeholder="placeholder"
                        @input="onChange"
                        @blur="handleBlur"
                        :disabled="disabled"
                        :readonly="readonly"
                        v-regex="regex"
                        @focus="onFocus"
                    />
                    <div class="absolute right-1 top-1/2 flex -translate-y-1/2 items-center gap-2">
                        <button
                            v-if="hasNumberControl"
                            type="button"
                            @click="handleDecrease"
                            class="btn-number-control btn-number-control--decrease"
                            :disabled="Number(inputValue) <= 1"
                        >
                            <IconsMinus
                                classWrapper="w-4 h-4"
                                :fill-color="Number(inputValue) <= 1 ? 'fill-gray-700' : 'fill-white'"
                            />
                        </button>
                        <button
                            v-if="hasNumberControl"
                            type="button"
                            @click="handleIncrease"
                            class="btn-number-control btn-number-control--increase"
                            :disabled="Number(inputValue) >= maxAmount"
                        >
                            <IconsPlus
                                classWrapper="w-4 h-4"
                                :fill-color="Number(inputValue) >= maxAmount ? 'fill-gray-700' : 'fill-white'"
                            />
                        </button>
                    </div>
                </div>
                <p v-if="errorMessage" class="absolute left-0 top-full mt-[2px] w-full text-xs text-red-700">
                    {{ $t(errorMessage) }}
                </p>
                <div v-if="copy" class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <NuxtLink
                        class="cursor-pointer rounded-2xl bg-bigg-yellow px-3 py-1 text-center text-xs font-normal italic text-black"
                        @click="handleCopy"
                    >
                        {{ copied ? $t('common.copied') : $t('common.copy') }}
                    </NuxtLink>
                </div>
                <div v-if="confirmed" class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <IconsCheck />
                </div>
                <div v-if="money" class="absolute inset-y-0 right-0 flex items-center pr-3">
                    <div class="money-value text-right text-sm font-medium italic text-green-400">
                        =
                        {{ moneyValue }}
                        {{ currency || 'VND' }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useClipboard } from '@vueuse/core'
import { useField } from 'vee-validate'
import { toRef } from 'vue'
import { useRegex } from '~/composables/use-regex'
import { NumberUtils } from '~/utils'

const { vRegex } = useRegex()

const props = defineProps({
    type: {
        type: String,
        default: 'text',
    },
    value: {
        type: String,
        default: '',
    },
    className: {
        type: String,
        default: '',
    },
    regex: {
        type: String,
        default: '',
    },
    name: {
        type: String,
        required: true,
    },
    label: {
        type: String,
        required: true,
    },
    labelClass: {
        type: String,
        default: 'bg-slate-900',
    },
    copy: {
        type: Boolean,
        default: false,
    },
    confirmed: {
        type: Boolean,
        default: false,
    },
    successMessage: {
        type: String,
        default: '',
    },
    currency: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '',
    },
    classes: {
        type: String,
        default: '',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    money: {
        type: Boolean,
        default: false,
    },
    isFormatNumber: {
        type: Boolean,
        default: false,
    },
    isFormatUserName: {
        type: Boolean,
        default: false,
    },
    exchangingRate: {
        type: Number,
        default: 0,
    },
    isUppercase: {
        type: Boolean,
        default: false,
    },
    isLowercase: {
        type: Boolean,
        default: false,
    },
    hasNumberControl: {
        type: Boolean,
        default: false,
    },
    maxAmount: {
        type: Number,
        default: 0,
    },
})
const name = toRef(props, 'name')
const theValue = toRef(props, 'value')
const numberPattern = /\d+/g

const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
    // meta,
} = useField(name, undefined, {
    initialValue: props.value,
})

watch(
    () => theValue.value,
    // eslint-disable-next-line
    (newVal, _) => {
        if (newVal) {
            inputValue.value = newVal
        }
    }
)

const parseNumber = value => {
    const sanitizedValue = String(value).replace(/,/g, '')
    return isNaN(sanitizedValue) ? 0 : Number(sanitizedValue.match(numberPattern)?.join('') ?? 0)
}

const moneyValue = computed(() => {
    const newNumber = parseNumber(inputValue.value)
    if (!props.exchangingRate) {
        return inputValue.value ? NumberUtils.formatNumberWithDots(newNumber * 1000) : 0
    }
    return NumberUtils.formatNumberWithDots(
        NumberUtils.truncateTo2Decimals((newNumber * 1000) / props.exchangingRate),
        true
    )
})

const { copy: copyValue, copied, isSupported } = useClipboard()
const handleCopy = () => {
    if (inputValue.value && isSupported) {
        copyValue(inputValue.value)
    }
}

const onFocus = event => {
    event.stopPropagation()
    const winScroll = window.scrollY
    window.scrollTo({
        top: winScroll,
    })
}

const onChange = event => {
    let newValue = event.target.value
    if (props.type === 'tel') {
        newValue = event.target.value.replace(/\D+/g, '')
    }
    if (props.isFormatNumber) {
        newValue = NumberUtils.formatNumberWithDots(Number(newValue?.replace(/\,/g, '')))
    }

    if (props.isLowercase) {
        event.target.value = newValue.toLowerCase()
    }
    if (props.isUppercase) {
        event.target.value = newValue.toUpperCase()
    }
    if (props.money && Number(newValue?.replace(/\,/g, ''))) {
        event.target.value = NumberUtils.formatNumberWithCommas(Number(newValue.toString().replace(/,/g, '')))
    }

    if (newValue !== event.target.value) {
        event.target.value = newValue
    }

    if (props.isFormatUserName) {
        event.target.value = newValue.replace(/\s{2,}/g, ' ')
    }

    handleChange(event)
}

const handleDecrease = () => {
    if (Number(inputValue.value) <= 1) {
        return
    }
    const newValue = Number(inputValue.value) - 1
    inputValue.value = newValue
}

const handleIncrease = () => {
    if (Number(inputValue.value) >= props.maxAmount) {
        return
    }
    const newValue = Number(inputValue.value) + 1
    inputValue.value = newValue
}

onMounted(() => {
    if (props.hasNumberControl) {
        inputValue.value = 1
    }
})
</script>
<style lang="scss" scoped>
.btn-number-control {
    @apply flex h-7 w-7 items-center justify-center rounded-full border border-slate-700;
    &:disabled {
        @apply border-slate-700/30;
    }
    &--decrease {
        @apply left-3;
    }
    &--increase {
        @apply right-3;
    }
}
</style>
