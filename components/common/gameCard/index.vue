<template>
    <div
        class="game-item group relative w-full"
        :class="[
            {
                'loader-image': isloaderImage,
            },
            imageClass || '',
            itemClass || '',
        ]"
    >
        <div class="relative">
            <nuxt-img
                v-if="isIgnoreLazy"
                class="aspect-[150/102] w-full rounded-md object-cover xl:aspect-[253/175] xl:rounded-lg"
                :class="imageClass"
                :src="imageUrlLocal"
                :alt="data.alt || 'game'"
                quality="50"
                format="webp"
                sizes="(max-width: 600px) 100vw, 50vw"
            />
            <img
                v-else
                class="aspect-[150/102] w-full rounded-md object-cover xl:aspect-[253/175] xl:rounded-lg"
                :class="imageClass"
                :src="imageUrlLocal"
                @error="checkImageError"
                placeholder="/assets/images/img-defaut.png"
                :alt="data.alt || 'game'"
                loading="lazy"
                @load="removeclass"
                quality="50"
                format="webp"
                sizes="(max-width: 600px) 100vw, 50vw"
            />
            <div
                role="button"
                type="button"
                @click="handlePlayClick()"
                class="absolute top-0 h-full w-full cursor-pointer xl:hidden"
            ></div>
            <!-- hover effect -->
            <div
                class="invisible absolute top-0 z-10 hidden h-full w-full flex-col items-center justify-center space-y-2 rounded-lg bg-indigo-950 bg-opacity-75 group-hover:visible xl:flex"
            >
                <div
                    v-if="!props.showFavorite"
                    class="mb-2 text-center text-sm font-bold capitalize text-white"
                >
                    {{ String(getGameName(data.name)).toLowerCase() }}
                </div>
                <button @click="handlePlayClick()" :class="buttonPlayClass" v-if="!showMobile">
                    <div class="flex justify-center gap-2">
                        <IconsPlay class-wrapper="w-5 h-5" :fill-color="`fill-${colorTextPlay}`" />
                        <span
                            :class="`text-center text-sm font-medium capitalize text-${colorTextPlay}`"
                        >
                            {{ $t('common.play') }}
                        </span>
                    </div>
                </button>
            </div>
            <!-- Jackpot -->
            <div
                v-if="jackpotvalue > 0"
                class="absolute left-1 top-1 flex items-center justify-center"
            >
                <div class="rounded-l-full rounded-r-full bg-[#00000094] px-2 py-[2px] xl:py-1">
                    <div
                        class="flex items-center gap-1 text-[12px] font-semibold leading-4 text-[#FFB631] xl:text-[14px]"
                    >
                        <img :src="'/assets/images/game/currency.svg'" width="14" height="14" />
                        <CommonAnimatedNumber
                            :animationDuration="5000"
                            :number="jackpotvalue"
                            :previousNumber="Math.round(jackpotvalue * (3 / 5))"
                            class="inline-block text-left"
                        />
                    </div>
                </div>
            </div>
            <div class="pointer-events-none absolute right-0 top-0 flex flex-col items-end">
                <img
                    v-if="data.tags && ['new', 'hot', 'event'].includes(data.tags)"
                    :src="`/assets/images/icons/games/ic-${data.tags}-label.svg`"
                    alt="label"
                    class="h-[14px] w-[47px] rounded-tr-[5px] object-contain object-right xl:h-[19px] xl:w-[56px] xl:rounded-tr-lg"
                />
            </div>
        </div>

        <div class="mt-1 flex items-start gap-2 xl:mt-2" v-if="showFavorite">
            <div class="flex flex-1 flex-col xl:gap-[2px]" @click="handlePlayClick()">
                <p :class="['game-item__title line-clamp-1 cursor-pointer', titleClass]">
                    {{ String(getGameName(data.name)).toLowerCase() }}
                </p>
                <p :class="['cursor-pointer', providerClass]">
                    {{ data.partner_txt || data.partner_provider }}
                </p>
            </div>
            <ModulesGameFavoriteIcon
                :game="data"
                :type="favoriteType"
                :fill-color="favoriteColor"
                class="mt-1"
            />
        </div>
        <div class="absolute right-0 top-0 flex flex-col items-end">
            <img
                v-if="data.label && ['new', 'hot', 'live'].includes(data.label)"
                :src="`/assets/images/icons/games/ic-${data.label}-label.svg`"
                alt="label"
                class="rounded-tr-lg"
            />
        </div>
    </div>
</template>
<script setup>
import { useCommon } from '~/composables/use-common'
import { thumbGames } from '~/constants/thumb-games'

const { showMobile } = useCommon()

const props = defineProps({
    data: {
        type: Object,
        required: true,
    },
    jackpotvalue: {
        type: Number,
        default: 0,
    },
    itemClass: {
        type: String,
        default: '',
    },
    imageClass: {
        type: String,
        default: '',
    },
    showFavorite: {
        type: Boolean,
        default: false,
    },
    isIgnoreLazy: {
        type: Boolean,
        default: false,
    },
    isIgnoreProvider: {
        type: Boolean,
        default: false,
    },
    favoriteType: {
        type: String,
        default: 'games',
    },
    favoriteColor: {
        type: String,
        default: 'fill-grey-500',
    },
    titleClass: {
        type: String,
        default: 'text-sm font-normal capitalize text-grey-900 xl:text-base',
    },
    providerClass: {
        type: String,
        default: 'text-[10px] text-slate-300 max-xl:font-medium md:text-sm',
    },
    buttonPlayClass: {
        type: String,
        default: 'rounded-lg bg-rose-600 px-4 xl:h-9',
    },
    colorTextPlay: {
        type: String,
        default: 'white',
    },
})

const emit = defineEmits(['click'])
const imageSrc = ref('/assets/images/img-defaut.png')
const handlePlayClick = () => {
    emit('click')
}

watch(
    () => props.data,
    newVal => {
        imageSrc.value = newVal.image || newVal.image_mobile
    },
    {
        immediate: true,
    }
)

const isloaderImage = ref(!props.isIgnoreLazy)
const isLoadedJackpot = ref(false)
const removeclass = () => {
    isloaderImage.value = false
    isLoadedJackpot.value = true
}

const checkImageError = () => {
    imageSrc.value = '/assets/images/img-defaut.png'
}

const imageUrlLocal = computed(() => {
    if (imageSrc.value) {
        const thumbName = imageSrc.value.split('/').pop().split('.')[0]
        if (thumbGames.includes(thumbName.toLowerCase())) {
            return `/assets/images/v2/thumb-games/${thumbName.toLowerCase()}.webp`
        }
        return imageSrc.value
    }
    return '/assets/images/img-defaut.png'
})

const getGameName = str => {
    if (!str) return ''

    const trimmedStr = str.trim()

    if (
        (trimmedStr.startsWith('{') && trimmedStr.endsWith('}')) ||
        (trimmedStr.startsWith('[') && trimmedStr.endsWith(']'))
    ) {
        try {
            const parsed = JSON.parse(trimmedStr.replaceAll(`'`, '"'))
            return parsed.vi || parsed.en || str
        } catch (e) {
            console.error('Error parsing game name JSON:', e)
            return str
        }
    }

    return str
}
</script>
