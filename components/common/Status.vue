<template>
    <span
        :class="`status ${statusTransactionMapping[status].color} ${position} ${customStyle}`"
        :data-status="status"
        >{{ $t(statusTransactionMapping[status].text) }}</span
    >
</template>
<script setup>
import { statusTransactionMapping } from '~/constants'
defineProps({
    status: {
        type: String,
        required: true,
    },
    position: {
        type: String,
        default: 'left-1/2 bottom-full transform translate-y-1/2 -translate-x-1/2',
    },
    customStyle: {
        type: String,
        default: '',
    },
})
</script>

<style lang="scss" scoped>
.status {
    @apply absolute block overflow-hidden whitespace-nowrap rounded-full px-2 text-[8px] font-medium uppercase leading-[12px] text-white;
    &.is-maintenance {
        @apply bg-[#AFAFAF];
    }
    &.is-proposed {
        @apply h-[12px] w-[51px];
        font-size: 0;
        background: url('/assets/images/v2/tags/de-xuat.webp') no-repeat center;
        background-size: cover;
    }
}
</style>
