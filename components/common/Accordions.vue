<template>
    <div class="accordions flex w-full flex-col gap-4">
        <div
            v-for="(item, index) in items"
            :key="index"
            class="accordion-item overflow-hidden rounded-lg border border-gray-300 bg-white"
        >
            <div
                :class="[
                    'flex cursor-pointer items-center justify-between gap-2 rounded-lg px-3 py-[10px]',
                ]"
                @click="toggle(index)"
            >
                <div class="flex items-center gap-2">
                    <div
                        class="accordion-icon fle h-8 w-8 items-center justify-center rounded-lg bg-gray-500"
                    >
                        <IconsAccountGeneral />
                    </div>
                    <h3 class="text-sm font-medium text-grey-900">
                        {{ item.title }}
                    </h3>
                </div>
                <IconsChevronDown
                    :class="[
                        'flex-shrink-0 transition-all duration-300',
                        { 'rotate-180': activeIndex === index },
                    ]"
                />
            </div>

            <transition name="accordion">
                <div v-show="isOpen(index)" class="overflow-hidden rounded-b-lg px-3 pb-4 pt-[6px]">
                    <p>{{ item.content }}</p>
                </div>
            </transition>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            activeIndex: null,
            items: [
                { title: 'Accordion Item 1', content: 'Content for item 1' },
                { title: 'Accordion Item 2', content: 'Content for item 2' },
                { title: 'Accordion Item 3', content: 'Content for item 3' },
            ],
        }
    },
    methods: {
        toggle(index) {
            this.activeIndex = this.activeIndex === index ? null : index
        },
        isOpen(index) {
            return this.activeIndex === index
        },
    },
}
</script>

<style scoped>
.accordion-enter-active,
.accordion-leave-active {
    transition:
        max-height 0.3s ease-in-out,
        padding 0.3s ease-in-out;
}
.accordion-enter, .accordion-leave-to /* .accordion-leave-active in <2.1.8 */ {
    max-height: 0;
    padding: 0;
}
</style>
