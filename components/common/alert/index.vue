<template>
    <div
        id="toast"
        class="fixed left-1/2 top-12 z-[81] flex min-w-[355px] max-w-[95%] -translate-x-1/2 rounded-md border p-1.5 text-gray-500 lg:w-max xl:min-w-[448px]"
        :class="{
            'border-[#512828] bg-[#2A1212]': type === 'ERROR',
            'border-[#28512E] bg-[#031D01]': type === 'SUCCESS',
        }"
        role="alert"
        v-show="alert"
    >
        <!-- SUCCESS -->
        <div
            v-if="type === 'SUCCESS'"
            class="inline-flex flex-shrink-0 justify-center rounded-lg text-green-500"
        >
            <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <circle cx="12.0002" cy="12" r="8.68966" fill="url(#paint0_linear_4589_15167)" />
                <path
                    d="M15.6082 7.99485C15.8475 8.1298 16.0238 8.35494 16.0983 8.62075C16.1728 8.88656 16.1394 9.17127 16.0054 9.41228L12.6034 15.529C12.5173 15.684 12.3978 15.8176 12.2537 15.92C12.1096 16.0224 11.9446 16.0911 11.7707 16.121C11.5968 16.1508 11.4185 16.1412 11.2489 16.0927C11.0792 16.0441 10.9225 15.958 10.7901 15.8406L8.21189 13.5539C8.00604 13.3713 7.88068 13.1138 7.86339 12.8381C7.84609 12.5624 7.93828 12.291 8.11968 12.0838C8.30107 11.8765 8.55681 11.7503 8.83063 11.7329C9.10445 11.7155 9.37393 11.8083 9.57978 11.9909L11.3343 13.5473L14.2001 8.39481C14.2665 8.27546 14.3555 8.17043 14.4622 8.08573C14.5689 8.00104 14.6911 7.93833 14.8219 7.90119C14.9526 7.86405 15.0894 7.8532 15.2243 7.86927C15.3592 7.88534 15.4897 7.92801 15.6082 7.99485Z"
                    fill="white"
                />
                <defs>
                    <linearGradient
                        id="paint0_linear_4589_15167"
                        x1="12.0002"
                        y1="3.3103"
                        x2="12.0002"
                        y2="20.6896"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#35D299" />
                        <stop offset="1" stop-color="#22A374" />
                    </linearGradient>
                </defs>
            </svg>
        </div>

        <!-- ERROR -->
        <div
            v-if="type === 'ERROR'"
            class="inline-flex flex-shrink-0 justify-center rounded-lg text-red-500"
        >
            <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M3.31055 7.44823C3.31055 5.16292 5.16316 3.3103 7.44848 3.3103H16.5519C18.8372 3.3103 20.6899 5.16292 20.6899 7.44823V16.5517C20.6899 18.837 18.8372 20.6896 16.5519 20.6896H7.44848C5.16316 20.6896 3.31055 18.837 3.31055 16.5517V7.44823Z"
                    fill="url(#paint0_linear_4589_14494)"
                />
                <path
                    d="M3.31055 7.44823C3.31055 5.16292 5.16316 3.3103 7.44848 3.3103H16.5519C18.8372 3.3103 20.6899 5.16292 20.6899 7.44823V16.5517C20.6899 18.837 18.8372 20.6896 16.5519 20.6896H7.44848C5.16316 20.6896 3.31055 18.837 3.31055 16.5517V7.44823Z"
                    fill="url(#paint1_linear_4589_14494)"
                />
                <path
                    d="M7.75333 7.75235C7.94811 7.55763 8.21226 7.44824 8.48768 7.44824C8.7631 7.44824 9.02724 7.55763 9.22202 7.75235L12.0072 10.5375L14.7924 7.75235C14.9883 7.56315 15.2507 7.45846 15.523 7.46082C15.7954 7.46319 16.0559 7.57243 16.2484 7.76501C16.441 7.95759 16.5503 8.2181 16.5526 8.49044C16.555 8.76278 16.4503 9.02515 16.2611 9.22105L13.4759 12.0062L16.2611 14.7914C16.4503 14.9873 16.555 15.2497 16.5526 15.522C16.5503 15.7944 16.441 16.0549 16.2484 16.2475C16.0559 16.44 15.7954 16.5493 15.523 16.5517C15.2507 16.554 14.9883 16.4493 14.7924 16.2601L12.0072 13.4749L9.22202 16.2601C9.02613 16.4493 8.76375 16.554 8.49141 16.5517C8.21908 16.5493 7.95856 16.44 7.76598 16.2475C7.5734 16.0549 7.46417 15.7944 7.4618 15.522C7.45943 15.2497 7.56413 14.9873 7.75333 14.7914L10.5385 12.0062L7.75333 9.22105C7.55861 9.02626 7.44922 8.76212 7.44922 8.4867C7.44922 8.21128 7.55861 7.94713 7.75333 7.75235Z"
                    fill="white"
                />
                <defs>
                    <linearGradient
                        id="paint0_linear_4589_14494"
                        x1="12.0002"
                        y1="3.3103"
                        x2="12.0002"
                        y2="20.6896"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#F35959" />
                        <stop offset="1" stop-color="#BB1616" />
                    </linearGradient>
                    <linearGradient
                        id="paint1_linear_4589_14494"
                        x1="12.0002"
                        y1="3.3103"
                        x2="12.0002"
                        y2="20.6896"
                        gradientUnits="userSpaceOnUse"
                    >
                        <stop stop-color="#F35959" />
                        <stop offset="1" stop-color="#BB1616" />
                    </linearGradient>
                </defs>
            </svg>
        </div>

        <!-- WARNING -->
        <div
            v-if="type === 'WARNING'"
            class="inline-flex flex-shrink-0 justify-center rounded-lg text-orange-500"
        >
            <svg
                class="h-5 w-5"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 20 20"
            >
                <path
                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"
                />
            </svg>
            <span class="sr-only">Warning icon</span>
        </div>
        <div class="ml-2 text-wrap text-[12px]">
            <p class="font-[500] text-white">{{ title }}</p>
            <p class="message-text font-[400] text-slate-300">
                {{ message_key ? $t(message_key) : message }}
            </p>
        </div>
        <!-- <button
            type="button"
            class="-mx-1.5 -my-1.5 ml-auto inline-flex items-center justify-center rounded-lg bg-white p-1.5 text-gray-400"
            aria-label="Close"
            @click="closeToast"
        >
            <span class="sr-only">Close</span>
            <svg
                class="h-3 w-3"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 14"
            >
                <path
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                />
            </svg>
        </button> -->
    </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'

import { useAlertStore } from '~/stores'

const alertStore = useAlertStore()
const { type, message, alert, message_key, title } = storeToRefs(alertStore)
</script>
<style scoped lang="scss">
.message-text {
    @apply whitespace-pre-line;
}
</style>
