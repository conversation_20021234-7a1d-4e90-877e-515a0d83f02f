<template>
    <div class="password-input relative">
        <div class="relative flex flex-col justify-start gap-[2px]">
            <div class="relative" :class="{ 'mb-5': errorMessage }">
                <label
                    :for="name + '_id'"
                    :class="[
                        'absolute left-3 top-0 -translate-y-1/2 rounded px-1 text-left text-sm font-medium text-white',
                        labelClass,
                    ]"
                >
                    {{ label }}</label
                >
                <input
                    :class="[
                        'block w-full appearance-none rounded-lg border bg-slate-950 px-2.5 pb-3.5 pl-4 pt-4 text-sm text-white placeholder:text-slate-300 autofill:bg-slate-950 focus:ring-0',
                        errorMessage
                            ? 'border-red-700 focus:border-red-700'
                            : 'border-slate-700 focus:border-slate-700',
                        classes,
                    ]"
                    autocomplete="off"
                    :name="name"
                    :id="name + '_id'"
                    :type="showPass ? 'text' : 'password'"
                    :value="inputValue"
                    :placeholder="placeholder"
                    @input="handleChange"
                    @blur="handleBlur"
                    v-bind="$attrs"
                />
                <p
                    v-if="errorMessage"
                    class="absolute left-0 top-full mt-[2px] w-full text-xs text-red-700"
                >
                    {{ $t(errorMessage) }}
                </p>
                <div
                    role="button"
                    @click="showPass = !showPass"
                    class="absolute inset-y-0 right-3 top-1/2 flex -translate-y-1/2 transform items-center"
                >
                    <i
                        class="text-[24px] text-[#717589]"
                        :class="showPass ? 'icon-eye' : 'icon-eye-hide'"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useField } from 'vee-validate'
import { toRef } from 'vue'

const props = defineProps({
    type: {
        type: String,
        default: 'password',
    },
    value: {
        type: String,
        default: '',
    },
    name: {
        type: String,
        required: true,
    },
    label: {
        type: String,
        required: true,
    },
    labelClass: {
        type: String,
        default: 'bg-slate-900',
    },
    classes: {
        type: String,
        default: '',
    },
    successMessage: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '',
    },
})
const showPass = ref(false)
const name = toRef(props, 'name')

const {
    value: inputValue,
    errorMessage,
    handleBlur,
    handleChange,
    // meta,
} = useField(name, undefined, {
    initialValue: props.value,
})
</script>
