<template>
    <div class="h-full min-h-screen w-full">
        <CommonIframeLoading v-if="isFetching || !loaded || !sportUrl" />

        <div
            v-show="!isFetching && loaded && sportUrl"
            class="loader-image flex h-full min-h-screen"
            :class="{ 'loading-image': isFetching || !loaded || !sportUrl }"
        >
            <iframe id="iframe" ref="iframe" :src="sportUrl" class="flex w-full"></iframe>
        </div>
    </div>
</template>

<script setup>
import { useSportsStore } from '~/stores'
import { useUserStore } from '~/stores'
import { storeToRefs } from 'pinia'
import { useCommon } from '~/composables/use-common'
import { PAGE_URL } from '~/constants/page-urls'

const { showMobile, openNewTab } = useCommon()

const props = defineProps(['apiUrl'])

const useSportsStoreInstance = useSportsStore()
const useUserStoreInstance = useUserStore()
const { showGameMaintenanceModal } = useNotify()
const { isLoggedIn } = storeToRefs(useUserStoreInstance)
const { sportUrl } = storeToRefs(useSportsStoreInstance)

const { getSportUrl, restSportUrl } = useSportsStoreInstance

const isFetching = ref(false)
const loaded = ref(false)
const iframe = ref()
const router = useRouter()

const loadSport = async sportApi => {
    openNewTab(
        '',
        async () => {
            try {
                isFetching.value = true
                const { data } = await getSportUrl(sportApi)

                if (!data?.data?.url) {
                    showGameMaintenanceModal(() => {
                        router.push(PAGE_URL.SPORTS)
                    })

                    return ''
                }
                if (data?.status === 'OK' && !showMobile.value) {
                    let url = data?.data?.url
                    if (router.currentRoute.value.path === PAGE_URL.SABA_SPORTS) {
                        url = url.replace(/&loginUrl=[^&]+/, '')
                    }
                    sportUrl.value = url
                    return ''
                }
                if (data?.status === 'OK') {
                    if (import.meta.client) {
                        router.go(-1)
                        return data?.data?.url
                    }
                    return ''
                }
            } catch (error) {
                console.error('Error loading sport URL:', error)
                return ''
            } finally {
                isFetching.value = false
            }
        },
        false,
        true
    )
}
watchEffect(() => {
    if (iframe.value) {
        iframe.value.onload = () => {
            loaded.value = true
        }
    }
})
onBeforeUnmount(() => {
    if (sportUrl.value) {
        restSportUrl()
    }
})
onMounted(async () => {
    await nextTick(async () => {
        try {
            await loadSport(props.apiUrl)
        } catch (error) {
            console.log(error)
        }
    })
})
watch(
    () => isLoggedIn.value,
    // eslint-disable-next-line
    (newVal, _) => {
        if (newVal) {
            reloadNuxtApp()
        } else {
            navigateTo('/')
        }
    }
)
</script>

<style lang="scss" scoped>
.loading-image {
    background: #fff url(/assets/images/iframe/img-loading.jpg) center center no-repeat;
    border-radius: 14px;
}
</style>
