<template>
    <div
        class="iframe__loading flex h-full min-h-screen w-full flex-col items-center justify-center gap-2.5"
        :class="className"
    >
        <img :src="'/assets/images/iframe/img-loading.webp'" class="w-[141px]" alt="loading" />
        <div class="iframe__loading--progress mt-[30px] h-2.5 w-[250px] rounded-sm bg-slate-700">
            <div class="iframe__loading--progress-loaded h-full w-10 rounded-sm"></div>
        </div>
        <div class="iframe__loading--text text-sm font-normal text-slate-300">
            {{ $t('common.loading') }}
        </div>
    </div>
</template>
<script setup>
defineProps({
    className: {
        type: String,
        default: '',
    },
})
</script>
<style lang="scss" scoped>
.iframe {
    &__loading {
        &--progress {
            &-loaded {
                @apply bg-green-400;
                animation: progressBar 4s ease-in-out;
                animation-fill-mode: both;
            }
        }
    }
}
</style>
