<template>
    <div class="iframe h-full min-h-screen w-full">
        <CommonIframeLoading v-if="isFetching || !loaded || !theFrameUrl" />
        <div
            v-show="!isFetching && loaded && theFrameUrl"
            class="flex h-full min-h-screen"
            :class="{ 'loading-image': isFetching || !loaded || !theFrameUrl }"
        >
            <iframe id="iframe" ref="iframe" :src="theFrameUrl" class="flex w-full"></iframe>
        </div>
    </div>
</template>

<script setup>
import { useFrameStore, useUserStore } from '~/stores'
import { storeToRefs } from 'pinia'
import { useNotify } from '~/composables/use-notify'
import { useCommon } from '~/composables/use-common'
import { LODESIEUTOC } from '~/constants/apiLink'

const useFrameStoreInstance = useFrameStore()
const useUserStoreInstance = useUserStore()
const { theFrameUrl } = storeToRefs(useFrameStoreInstance)
const { isLoggedIn } = storeToRefs(useUserStoreInstance)

const router = useRouter()
const { showMobile } = useCommon()
const iframe = ref()
const { showGameMaintenanceModal } = useNotify()

const { restFrameUrl, getFrameUrl } = useFrameStoreInstance

const isFetching = ref(true)
const loaded = ref(false)
const props = defineProps(['link'])
const { link } = toRefs(props)

onMounted(async () => {
    await nextTick(async () => {
        try {
            if (!theFrameUrl.value && link.value !== LODESIEUTOC) {
                const { data } = await getFrameUrl(link.value)
                if (data.value?.status === 'OK' && showMobile.value) {
                    if (import.meta.client) {
                        setTimeout(() => {
                            window.open(data.value.data.url_mobile || data.value.data.url, '_blank')
                        })
                    }
                    router.back()
                    return
                }
                if (!theFrameUrl.value) {
                    showGameMaintenanceModal(() => {
                        navigateTo('/')
                    })
                }
            }
        } catch (error) {
            console.log(error)
        } finally {
            isFetching.value = false
        }
    })
})
watchEffect(() => {
    if (iframe.value) {
        iframe.value.onload = () => {
            loaded.value = true
        }
    }
})
onBeforeUnmount(() => {
    restFrameUrl()
})
watch(
    () => isLoggedIn.value,
    // eslint-disable-next-line
    (newVal, _) => {
        if (newVal) {
            reloadNuxtApp()
        } else {
            navigateTo('/')
        }
    }
)
</script>
<style lang="scss" scoped>
.loading-image {
    background: #fff url(/assets/images/iframe/img-loading.jpg) center center no-repeat;
    border-radius: 14px;
}
</style>
