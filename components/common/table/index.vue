<template>
    <div class="relative">
        <table class="w-full table-auto rounded-t-lg text-xs font-medium">
            <thead class="text-xs text-slate-300">
                <tr class="relative text-left">
                    <th
                        v-for="(head, index) in headerTable"
                        :key="index"
                        scope="col"
                        class="flex-1 whitespace-nowrap px-2 py-3 font-medium"
                        :class="{
                            'pl-5': 0 == index,
                        }"
                    >
                        <template v-if="!head.hidden">
                            <span v-if="head?.valueShort" class="hidden xs:block">
                                {{ $t(head.value) }}
                            </span>
                            <span v-if="head?.valueShort" class="xs:hidden">
                                {{ $t(head.valueShort) }}
                            </span>
                            <span v-else>
                                {{ $t(head.value) }}
                            </span>
                        </template>
                    </th>
                </tr>
            </thead>
            <!-- body -->
            <tbody v-if="dataTable.length === 0" class="text-center">
                <tr>
                    <td :colspan="headerTable.length" class="h-[100px]">
                        <CommonNoData />
                    </td>
                </tr>
            </tbody>
            <tbody
                v-else
                :class="[
                    'text-white',
                    {
                        'divide-y-4 divide-slate-900': isStriped,
                    },
                ]"
            >
                <tr v-for="(item, index) in dataTable" :key="index" class="">
                    <template v-if="basic">
                        <td
                            v-for="(element, eleIndex) in item"
                            :key="eleIndex"
                            class="max-px-6 py-3 text-left"
                            :class="{
                                'pl-6': 0 == eleIndex,
                                'text-center': centeredContent,
                            }"
                        >
                            {{ element }}
                        </td>
                    </template>
                    <template v-else>
                        <td
                            v-for="(head, headIndex) in headerTable"
                            :key="headIndex"
                            class="px-2 py-4 text-left"
                            :class="{
                                'rounded-l-lg pl-5': 0 == headIndex,
                                'rounded-r-lg': headerTable.length - 1 === headIndex,
                                'text-center': centeredContent,
                                'bg-slate-800': isStriped,
                            }"
                        >
                            <slot :name="head.key" :item="item" />
                        </td>
                    </template>
                </tr>
            </tbody>
        </table>
    </div>
</template>
<script setup>
defineProps({
    headerTable: {
        type: Array,
        default: () => [
            { key: 'id', value: 'table.id' },
            { key: 'name', value: 'table.name' },
            { key: 'status', value: 'table.status' },
        ],
    },
    dataTable: {
        type: Array,
        default: () => [
            {
                id: '1',
                name: 'name',
                status: 'Active',
            },
        ],
    },
    basic: {
        type: Boolean,
        default: false,
    },
    centeredContent: {
        type: Boolean,
        default: false,
    },
    isStriped: {
        type: Boolean,
        default: true,
    },
})
</script>

<style lang="scss" scoped></style>
