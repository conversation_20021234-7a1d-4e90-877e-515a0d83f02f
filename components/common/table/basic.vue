<template>
    <div class="relative overflow-x-auto">
        <table class="w-full text-center text-xs font-light text-black">
            <thead class="bg-black text-white">
                <tr>
                    <th
                        v-for="(head, index) in headerTable"
                        :key="index"
                        scope="col"
                        class="px-6 py-3"
                    >
                        {{ $t(head.value) }}
                    </th>
                </tr>
            </thead>
            <tbody v-if="dataTable.length === 0" class="text-center">
                <tr>
                    <td :colspan="headerTable.length">
                        {{ $t('common.no_data') }}
                    </td>
                </tr>
            </tbody>
            <tbody v-else class="border-gray border">
                <tr v-for="(item, index) in dataTable" :key="index" class="border-gray border-b">
                    <td v-for="(element, subIndex) in item" :key="subIndex" class="px-6 py-3">
                        {{ element }}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>
<script setup>
defineProps({
    headerTable: {
        type: Array,
        default: () => [
            { key: 'id', value: 'table.id' },
            { key: 'name', value: 'table.name' },
            { key: 'amount', value: 'table.amount' },
            { key: 'status', value: 'table.status' },
        ],
    },
    dataTable: {
        type: Array,
        default: () => [
            {
                id: '1',
                name: '232*******3231',
                amount: 'Jo*******',
                status: 'Verifying',
            },
        ],
    },
})
</script>
