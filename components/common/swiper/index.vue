<template>
    <div :id="swiperID + 'parent'" :class="swiperClass" class="common-swiper w-full">
        <Swiper
            :id="swiperID"
            :modules="[Autoplay, Pagination, FreeMode]"
            :slides-per-view="slidesPerView"
            :freeMode="freeMode"
            :loop="true"
            :effect="effect"
            :creative-effect="{
                prev: {
                    shadow: false,
                    translate: ['-20%', 0, -1],
                },
                next: {
                    translate: ['100%', 0, 0],
                },
            }"
            :pagination="paginationComputed"
            :class="swiperClass"
            :spaceBetween="spaceBetween"
            :breakpoints="breakpoints"
        >
            <template v-if="slideTemplate">
                <SwiperSlide v-for="(item, idx) in data" :key="idx">
                    <div class="flex h-full cursor-pointer justify-center">
                        <component :is="slideTemplate" :data="item" />
                    </div>
                </SwiperSlide>
            </template>
            <template v-else>
                <SwiperSlide v-for="(item, idx) in data" :key="idx">
                    <div class="flex justify-center">
                        {{ item }}
                    </div>
                </SwiperSlide>
            </template>

            <span class="swiper-pagination"></span>
            <slot v-if="displayControlles" name="controllers"></slot>
        </Swiper>
    </div>
</template>

<script setup>
import { Autoplay, Pagination, FreeMode } from 'swiper/modules'
const {
    swiperID,
    slideTemplate,
    data,
    effect = 'creative',
    paginationClass,
    paginationActiveClass,
    paginationHorizontalClass,
    swiperClass,
    slidesPerView,
    spaceBetween,
    freeMode,
    mypagination,
    breakpoints,
} = defineProps([
    'swiperID',
    'slideTemplate',
    'data',
    'effect',
    'paginationClass',
    'paginationActiveClass',
    'paginationHorizontalClass',
    'swiperClass',
    'slidesPerView',
    'freeMode',
    'mypagination',
    'spaceBetween',
    'breakpoints',
])

const displayControlles = ref(false)

const paginationComputed = computed(() => {
    if (mypagination === undefined) {
        return {
            bulletElement: 'span',
            type: 'bullets',
            clickable: true,
            bulletClass: `swiper-pagination-bullet ${paginationClass}`,
            bulletActiveClass: `swiper-pagination-bullet-active ${paginationActiveClass}`,
            horizontalClass: `${paginationHorizontalClass}`,
        }
    } else {
        return mypagination
    }
})

onMounted(async () => {
    // add delay
    await nextTick(() =>
        setTimeout(() => {
            displayControlles.value = true
        }, 150)
    )
})
</script>
