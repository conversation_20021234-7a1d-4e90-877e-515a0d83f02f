<template>
    <NuxtLink role="button" class="relative" @click="isPopoverVisible = true">
        <!-- Trigger <PERSON>ement -->
        <slot name="button"></slot>

        <!-- Popover Content -->
        <div
            v-if="isPopoverVisible"
            ref="isPopoverVisibleRef"
            class="dropdown-inner left-unset bottom-5 right-2 w-[320px] bg-slate-800 p-1 text-center text-white"
        >
            <slot name="content"></slot>
        </div>
    </NuxtLink>
</template>

<script setup>
import { onClickOutside } from '@vueuse/core'

const isPopoverVisible = ref(false)
const isPopoverVisibleRef = (ref < HTMLElement) | (null > null)

onClickOutside(isPopoverVisibleRef, () => {
    isPopoverVisible.value = false
})
</script>

<style scoped>
.dropdown-inner {
    padding: 12px 10px;
    position: absolute;
    z-index: 10;
    border-radius: 8px;
    box-shadow: 0px 1px 20px 0px rgba(0, 6, 22, 0.51);
}
</style>
