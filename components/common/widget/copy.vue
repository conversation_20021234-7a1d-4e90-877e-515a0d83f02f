<template>
    <IconsCopy
        :class-wrapper="`cursor-pointer ${classWrapper}`"
        :fill-color="value === copyValue ? 'fill-green-400' : fillColorDefault"
        @click="onCopy(value)"
    />
</template>
<script setup>
defineProps({
    value: {
        type: String,
        required: true,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    fillColorDefault: {
        type: String,
        default: 'fill-slate-300',
    },
})

const { onCopy, copyValue } = useCopy()
</script>
