<template>
    <div
        class="group relative"
        :class="{
            'loader-image': isloaderImage && !isShowVideo,
        }"
    >
        <div class="relative">
            <template v-if="isShowVideo">
                <div
                    class="relative z-10 w-full cursor-pointer overflow-hidden rounded-md bg-cover bg-center object-cover xl:rounded-lg"
                    :class="ratio"
                    :style="{
                        backgroundImage: `url(${data.image || data.image_mobile})`,
                    }"
                >
                    <div
                        :class="[
                            {
                                'aspect-[150/102] xl:aspect-[253/175]': !videoClass,
                            },
                            videoClass,
                        ]"
                        :id="`${data.partner_provider === 'vingame' ? 'techplay' : data.partner_provider}_${data.partner_game_id}`"
                    >
                        <div
                            class="absolute top-3 z-10 flex w-full items-center justify-between px-2 lg:top-3 lg:px-3"
                            v-if="isInlineInfo"
                        >
                            <div class="flex items-center gap-2">
                                <div
                                    class="favorite-wrapper flex h-5 w-5 items-center justify-center rounded-full bg-[rgba(0,0,0,0.5)]"
                                >
                                    <ModulesGameFavoriteIcon
                                        :game="data"
                                        :type="favoriteType"
                                        :fill-color="favoriteColor"
                                        size="w-[14px] h-[14px]"
                                    />
                                </div>

                                <div
                                    class="flex h-5 w-5 items-center justify-center rounded-full bg-[rgba(0,0,0,0.5)]"
                                >
                                    <div
                                        class="button-sound icon-mute"
                                        :id="`soundon-${data.partner_provider}_${data.partner_game_id}`"
                                        @click.stop="
                                            $setSoundOnEl(
                                                data.partner_provider + '_' + data.partner_game_id
                                            )
                                        "
                                        @touchend.stop="
                                            $setSoundOnEl(
                                                data.partner_provider + '_' + data.partner_game_id
                                            )
                                        "
                                    >
                                        <img
                                            :src="`${
                                                useRuntimeConfig().public.staticUrl
                                            }/assets/images/icons/icon-sound-off.svg`"
                                            class="w-3"
                                        />
                                    </div>
                                    <div
                                        class="button-sound"
                                        style="display: none"
                                        :id="`soundoff-${data.partner_provider}_${data.partner_game_id}`"
                                        @click.stop="
                                            $setSoundOffEl(
                                                data.partner_provider + '_' + data.partner_game_id
                                            )
                                        "
                                        @touchend.stop="
                                            $setSoundOffEl(
                                                data.partner_provider + '_' + data.partner_game_id
                                            )
                                        "
                                    >
                                        <img
                                            :src="`${
                                                useRuntimeConfig().public.staticUrl
                                            }/assets/images/icons/icon-sound-on.svg`"
                                            class="w-3"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div
                                class="flex items-center gap-1 rounded bg-[rgba(0,0,0,0.5)] px-2 py-[2px]"
                            >
                                <span class="flex items-center gap-[2px] lg:gap-1">
                                    <span
                                        class="flex h-[6px] w-[6px] rounded-full bg-[#F71B26]"
                                    ></span>
                                    <span class="text-xs font-medium uppercase text-white">
                                        LIVE
                                    </span>
                                </span>
                                <template v-if="userActive">
                                    <span
                                        class="flex h-[18px] w-[1px] bg-[#FBFCFF33] bg-opacity-20"
                                    ></span>
                                    <span class="flex items-center gap-[2px] lg:gap-1">
                                        <V2IconsUsers class-wrapper="w-[14px] h-[14px]" />
                                        <span class="text-xs font-medium uppercase text-white">
                                            {{ userActive }}
                                        </span>
                                    </span>
                                </template>
                            </div>
                        </div>
                        <img
                            v-if="isInlineInfo"
                            :src="'/assets/images/icons/' + data.partner_provider + '.png'"
                            :alt="data.partner_provider"
                            class="absolute right-3 top-10 z-10 w-[20px]"
                        />

                        <button
                            v-if="isInlineInfo"
                            class="btn btn-primary absolute bottom-3 right-3 z-10 max-w-fit !px-4 max-lg:bottom-2"
                            type="button"
                        >
                            {{ $t('common.play') }}
                        </button>

                        <div
                            v-if="isInlineInfo"
                            class="absolute bottom-1 left-2 z-10 flex transform flex-col items-start gap-1 lg:bottom-3 lg:left-4"
                        >
                            <h3 class="text-sm font-medium uppercase text-white lg:text-base">
                                {{ String(data.name).toLowerCase() }}
                            </h3>
                            <p
                                class="flex h-6 items-center gap-1 rounded-full bg-black bg-opacity-50 px-[10px]"
                            >
                                <span class="text-xs font-medium text-[#F2D49A]">
                                    Cơ hội thắng
                                </span>
                                <img
                                    :src="`/assets/images/v2/live-casinos/x30.webp?v=2`"
                                    alt="x30"
                                    class="relative w-[33px]"
                                />
                            </p>
                        </div>
                        <button
                            type="button"
                            @click="handlePlayClick()"
                            class="absolute top-0 z-20 h-full w-full"
                        ></button>
                    </div>
                </div>
            </template>
            <img
                v-else
                class="aspect-25/20 w-full rounded-md object-cover xl:rounded-lg"
                :class="imageClass"
                :src="data.image || data.image_mobile"
                :alt="data.alt"
                onerror='this.onerror = null; this.src="/assets/images/img-defaut.png";'
                :onended="removeclass()"
                loading="lazy"
            />

            <button
                type="button"
                @click="handlePlayClick()"
                class="absolute top-0 h-full w-full xl:hidden"
            ></button>
            <!-- hover effect -->
            <div
                v-if="!isShowVideo"
                class="invisible absolute top-0 z-10 hidden h-full w-full flex-col items-center justify-center space-y-2 rounded-lg bg-indigo-950 bg-opacity-75 group-hover:visible xl:flex"
            >
                <div
                    v-if="!props.showFavorite"
                    class="mb-2 text-center text-sm font-bold capitalize text-white"
                >
                    {{ String(data.name).toLowerCase() }}
                </div>
                <button @click="handlePlayClick()" :class="buttonPlayClass">
                    <div class="flex justify-center gap-2">
                        <IconsPlay class-wrapper="w-5 h-5" :fill-color="`fill-${colorTextPlay}`" />
                        <span
                            :class="`text-center text-sm font-medium capitalize text-${colorTextPlay}`"
                        >
                            {{ $t('common.play') }}
                        </span>
                    </div>
                </button>
            </div>
            <!-- Jackpot -->

            <template v-if="!isInlineInfo">
                <div class="absolute left-1 top-1 z-10 sm:left-3 sm:top-1">
                    <div v-if="jackpotvalue" class="z-10 flex items-center justify-center sm:mt-1">
                        <div
                            class="rounded-l-full rounded-r-full bg-[#00000094] px-1 py-0.5 lg:py-1 2xl:px-2"
                        >
                            <div
                                class="flex items-center justify-center gap-1 text-[10px] font-semibold leading-4 text-[#FFB631] 2xl:text-xs"
                            >
                                <img
                                    :src="'/assets/images/game/currency.svg'"
                                    width="14"
                                    height="14"
                                />
                                <CommonAnimatedNumber
                                    :animationDuration="5000"
                                    :number="jackpotvalue"
                                    :previousNumber="Math.round(jackpotvalue * (3 / 5))"
                                    class="inline-block text-left"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="absolute bottom-1 right-1 z-10 sm:bottom-3 sm:right-3">
                    <div class="flex justify-end" v-if="isShowVideo">
                        <img
                            class="h-[24px] sm:h-[28px]"
                            :src="'/assets/images/icons/' + data.partner_provider + '.png'"
                            :alt="data.partner_provider"
                            srcset=""
                        />
                    </div>
                </div>

                <div class="absolute bottom-1 left-1 z-40 flex gap-1 sm:bottom-3 sm:left-3">
                    <div
                        v-if="userActive"
                        class="inline-block rounded-[4px] bg-[#FFFFFF26] px-2 py-1 text-xs uppercase text-white backdrop-blur-sm"
                    >
                        <i class="icon-user"></i>
                        {{ userActive }}
                    </div>

                    <template v-if="isShowVideo && showVoiceButton">
                        <div
                            class="button-sound icon-mute rounded-[4px] bg-[#FFFFFF26] backdrop-blur-sm"
                            :id="`soundon-${data.partner_provider}_${data.partner_game_id}`"
                            @click.stop="
                                $setSoundOnEl(data.partner_provider + '_' + data.partner_game_id)
                            "
                            @touchend.stop="
                                $setSoundOnEl(data.partner_provider + '_' + data.partner_game_id)
                            "
                        >
                            <img
                                :src="`${
                                    useRuntimeConfig().public.staticUrl
                                }/assets/images/icons/icon-sound-off.svg`"
                            />
                        </div>
                        <div
                            class="button-sound rounded-[4px] bg-[#FFFFFF26] backdrop-blur-sm"
                            style="display: none"
                            :id="`soundoff-${data.partner_provider}_${data.partner_game_id}`"
                            @click.stop="
                                $setSoundOffEl(data.partner_provider + '_' + data.partner_game_id)
                            "
                            @touchend.stop="
                                $setSoundOffEl(data.partner_provider + '_' + data.partner_game_id)
                            "
                        >
                            <img
                                :src="`${
                                    useRuntimeConfig().public.staticUrl
                                }/assets/images/icons/icon-sound-on.svg`"
                            />
                        </div>
                    </template>
                </div>
            </template>

            <div v-if="!isInlineInfo" class="absolute right-0 top-0 flex flex-col items-end">
                <img
                    v-if="
                        (data.label && ['new', 'hot', 'live'].includes(data.label)) || isShowVideo
                    "
                    :src="`/assets/images/icons/games/ic-${
                        isShowVideo ? 'live' : data.label
                    }-label.svg`"
                    alt="label"
                    class="z-10 h-[19px] w-[56px] rounded-tr-[5px] object-contain xl:rounded-tr-lg"
                />
            </div>
        </div>

        <div v-if="showFavorite && !isInlineInfo" class="mt-1 flex items-start gap-2 xl:mt-2">
            <div class="flex-1" @click="handlePlayClick()">
                <p :class="['line-clamp-1 cursor-pointer', titleClass]">
                    {{ String(data.name).toLowerCase() }}
                </p>
                <p :class="['cursor-pointer', providerClass]">
                    {{ data.partner_txt || data.partner_provider }}
                </p>
            </div>
            <ModulesGameFavoriteIcon
                :game="data"
                :type="favoriteType"
                :fill-color="favoriteColor"
                class="mt-1"
            />
        </div>
        <div class="absolute right-0 top-0 flex flex-col items-end">
            <img
                v-if="data.tags"
                :src="`/assets/images/icons/games/ic-${data.tags}-label.svg`"
                alt="label"
                class="rounded-tr-lg"
            />
        </div>
    </div>
</template>
<script setup>
const { $verifyToken, $loadNanoPlayer, $setSoundOffEl, $setSoundOnEl } = useNuxtApp()

const props = defineProps({
    data: {
        type: Object,
        required: true,
    },
    jackpotvalue: {
        type: Number,
        default: 0,
    },
    showFavorite: {
        type: Boolean,
        default: false,
    },
    videoClass: {
        type: String,
        default: '',
    },
    isIgnoreProvider: {
        type: Boolean,
        default: false,
    },
    isInlineInfo: {
        type: Boolean,
        default: false,
    },
    userActive: {
        type: String,
        default: '',
    },
    imageClass: {
        type: String,
        default: '',
    },
    ratio: {
        type: String,
        default: 'aspect-[150/102]',
    },
    showVoiceButton: {
        type: Boolean,
        default: true,
    },
    favoriteType: {
        type: String,
        default: 'casinos',
    },
    favoriteColor: {
        type: String,
        default: 'fill-grey-500',
    },
    titleClass: {
        type: String,
        default: 'text-sm font-normal capitalize text-grey-900 xl:text-base',
    },
    providerClass: {
        type: String,
        default: 'text-[10px] text-slate-300 max-xl:font-medium md:text-sm',
    },
    buttonPlayClass: {
        type: String,
        default: 'rounded-lg bg-rose-600 px-4 xl:h-9',
    },
    colorTextPlay: {
        type: String,
        default: 'white',
    },
})

const emit = defineEmits(['click'])
const handlePlayClick = () => {
    emit('click')
}

const isloaderImage = ref(true)
const removeclass = () => {
    isloaderImage.value = false
}

const isShowVideo = computed(() => {
    return [
        'vgmn_108',
        'vgmn_109',
        'vgmn_110',
        'vgmn_111',
        'qs_xocdia-102',
        'G1X_305',
        'G1X_306',
        'G1S_305',
        'G1S_306',
        'bc_77784',
        'sb_77783',
        'bacca_77778',
        'xd_77786',
    ].includes(props.data.partner_game_id)
})

const timeoutId = ref(null)

onMounted(async () => {
    const tokenMap = new Map([
        ['rik_vgmn_108', { id: '41b93f00-3f85-4008-86e2-8e297e6799aa', key: 'XpjSI-uWCBa' }],
        ['rik_vgmn_109', { id: '867f07d4-2a67-4aa0-9c9a-306489ac3ca5', key: 'XpjSI-Xl7Hj' }],
        ['rik_vgmn_110', { id: 'dbfca645-f428-4157-858d-a52a7fd026e3', key: 'XpjSI-PkUx8' }],
        ['rik_vgmn_111', { id: 'da3844de-812d-446d-b1e9-158eb10819c4', key: 'XpjSI-t7no7' }],
        ['go_vgmn_110', { id: '360d8af8-5d64-43df-9bd9-fa91ad6f9c60', key: 'XpjSI-X3Chu' }],
        ['go_vgmn_109', { id: '9291199a-50c5-434b-9e97-5aeb670927d1', key: 'XpjSI-MsM1Y' }],
        ['b52_vgmn_108', { id: 'f61fbb0a-2595-450e-ae70-19b04bdc5710', key: 'XpjSI-5emz7' }],
        ['b52_vgmn_109', { id: 'ff9599e8-8cc4-40a2-ae0a-6f6c43984ccc', key: 'XpjSI-vlkTG' }],
        ['b52_vgmn_110', { id: '946b8871-2b8d-4dfd-9fe7-4ffd835fe98a', key: 'XpjSI-h2HI7' }],
        ['789club_G1X_305', { id: 'f23545d4-bbb0-416f-bbb7-972619813f2c', key: '7MhcV-weX7L' }],
        ['789club_G1X_306', { id: 'fd2186c3-26f3-40ac-9864-4287052b0ec3', key: '7MhcV-fwQ2N' }],
        ['sunwin_G1S_305', { id: '7652d5a1-11fd-4f52-bb74-933631fd78df', key: '7MhcV-Pi0uG' }],
        ['sunwin_G1S_306', { id: 'c88ff431-0ee8-4faf-b375-1b515b20ae68', key: '7MhcV-ZvYvo' }],
        ['techplay_bc_77784', { id: 'c639fc58-878b-414c-9ba3-9536088d430a', key: 'XpjSI-h4o0U' }],
        ['techplay_sb_77783', { id: '9cb5fda4-cd6c-4279-a9d6-eefa396b2d92', key: 'XpjSI-gzDej' }],
        [
            'techplay_bacca_77778',
            { id: '28859792-d423-45e9-ac2f-b4065415d93e', key: 'XpjSI-G2lCE' },
        ],
        ['techplay_xd_77786', { id: '4d0d259f-4fb2-4254-9d97-1aba8d5da2385', key: 'XpjSI-MsrYO' }],
        ['go_vgmn_111', { id: '', key: '' }],
    ])

    const key = `${props.data.partner_provider === 'vingame' ? 'techplay' : props.data.partner_provider}_${props.data.partner_game_id}`
    const tokenInfo = tokenMap.get(key)
    if (tokenInfo) {
        timeoutId.value = setTimeout(async () => {
            if (typeof window !== 'undefined' && !window.NanoPlayer) {
                await $loadNanoPlayer()
            }

            const domain = window.location.hostname
            $verifyToken(key, tokenInfo.id, tokenInfo.key, domain)
        }, 10000)
    }
})

onBeforeUnmount(() => {
    if (timeoutId.value) {
        clearTimeout(timeoutId.value)
        timeoutId.value = null
    }
})
</script>
<style lang="scss" scoped>
.button-sound {
    z-index: 999;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.background-gradient {
    &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50%;
        z-index: 10;
        border-bottom-right-radius: 6px;
        border-bottom-left-radius: 6px;
        background: linear-gradient(0deg, #21232a 22.47%, rgba(139, 164, 253, 0) 100%);
    }
}
</style>
