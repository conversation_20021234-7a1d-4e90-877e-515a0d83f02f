<template>
    <div v-if="showMobile" class="game-types relative xl:hidden">
        <div class="no-scrollbar flex gap-2 overflow-auto text-nowrap">
            <div
                class="game-item"
                v-for="(game, index) in types"
                :key="index"
                :ref="el => (gameRefs[index] = el)"
                :class="{
                    active: checkActive(game?.key),
                }"
                @click="handleUpdateGameType(game?.key)"
            >
                <i
                    :class="`icon-${
                        game.key === 'all' ? 'type-all' : game.key
                    } text-icon text-[20px]`"
                ></i>
                <span class="game-item-name">
                    {{ game.value === 'all' ? $t('filter.all') : game.name.toLowerCase() }}
                </span>
            </div>
        </div>
        <div
            @click="isOpen = !isOpen"
            class="absolute right-0 top-0 flex h-[36px] w-[36px] items-center justify-center rounded-lg bg-slate-900"
        >
            <i class="icon-filler-down text-[20px] text-slate-300" />
        </div>
        <div
            v-if="isOpen"
            class="dropdown-box absolute right-0 top-full z-50 mt-2 w-full rounded-lg bg-slate-900 px-3 py-2 shadow-[0_4px_10px_0_#48484D26]"
            :class="isOpen ? 'block' : 'hidden'"
        >
            <div class="mb-2 flex items-center justify-between">
                <p class="text-xs font-medium text-white">Chọn loại game</p>
                <i class="icon-close-mb text-[20px] text-slate-300" @click="isOpen = false" />
            </div>
            <div>
                <ul class="grid grid-cols-2 gap-2">
                    <li
                        v-for="(game, index) in types"
                        :key="index"
                        class="game-item"
                        :class="{
                            active: checkActive(game?.key),
                        }"
                        @click="handleUpdateGameType(game?.key)"
                    >
                        <i
                            :class="`icon-${
                                game.key === 'all' ? 'type-all' : game.key
                            } text-icon text-[20px]`"
                        ></i>
                        <span class="text-xs capitalize text-[#717589]">
                            {{
                                game.value === 'all'
                                    ? $t('filter.all')
                                    : `${game.name}`.toLowerCase()
                            }}
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useGamesStore, useCasinoStore } from '~/stores'
import { GAME_TYPE_MAPPING } from '~/constants/game'

const route = useRoute()
const isCasino = route.name.includes('casino')

const gamesStoreInstance = useGamesStore()
const { gameTypes, type: gameType, sort: gameSort } = storeToRefs(gamesStoreInstance)
const { setType: setGameType } = gamesStoreInstance

const casinoStoreInstance = useCasinoStore()
const { casinoTypes, type: casinoType, sort: casinoSort } = storeToRefs(casinoStoreInstance)
const { setType: setCasinoType } = casinoStoreInstance

const types = computed(() => {
    return isCasino ? casinoTypes.value : gameTypes.value
})

const { showMobile } = useCommon()

const isOpen = ref(false)
const gameRefs = reactive([])

const updateGameType = key => {
    if (isCasino) {
        setCasinoType(key)
    } else {
        setGameType(key)
    }
}

const handleUpdateGameType = key => {
    if (isOpen.value) {
        isOpen.value = false
    }
    updateGameType(key)
}

const checkActive = key => {
    if (!isCasino) {
        if (gameSort.value === 'favorite') {
            return key === gameSort.value
        }

        if (GAME_TYPE_MAPPING[key]) {
            return GAME_TYPE_MAPPING[key] === gameType.value
        }
        return key === gameType.value
    } else {
        if (casinoSort.value === 'favorite') {
            return key === casinoSort.value
        }

        return key === casinoType.value
    }
}
</script>
<style lang="scss" scoped>
.game-item {
    @apply flex items-center gap-1 text-nowrap py-2 text-xs capitalize text-slate-300;
    &.active {
        @apply relative text-green-400;
        &::before {
            @apply absolute bottom-0 left-1/2 block h-[2px] w-[15px] -translate-x-1/2 transform bg-green-400 content-[''];
            border-radius: 2px 2px 0 0;
        }
    }

    &-name {
        @apply whitespace-nowrap;
    }
}

.dropdown-box {
    .game-item {
        @apply py-1;
        &.active {
            &::before {
                @apply hidden;
            }
        }
    }
}
</style>
