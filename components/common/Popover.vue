<template>
    <div class="relative">
        <div class="relative">
            <button type="button" @click="dropdown = true">
                <slot name="button">Default <PERSON><PERSON></slot>
            </button>
            <div v-if="dropdown" ref="dropdownRef" class="dropdown-inner" :class="customClass">
                {{ message }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'

defineProps<{
    message: string
    customClass: string
}>()

const dropdown = ref(false)
const dropdownRef = ref<HTMLElement | null>(null)

onClickOutside(dropdownRef, () => {
    dropdown.value = false
})
</script>

<style scoped>
.dropdown-inner {
    padding: 0.5em;
    position: absolute;
    z-index: 10;
    border-radius: 5px;
    box-shadow: 2px 2px 5px rgba(10, 10, 10, 0.1);
}
</style>
