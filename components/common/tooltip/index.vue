<template>
    <div
        :id="id"
        role="tooltip"
        class="absolute right-0 top-0 z-20 inline-block -translate-y-4 transform rounded-lg bg-[#717589] px-3 py-2 text-xs text-white shadow-sm"
    >
        {{ $t(message) }}
        <div class="tooltip-arrow" data-popper-arrow></div>
    </div>
</template>
<script setup>
defineProps({
    id: {
        type: String,
        default: 'tooltip_common',
    },
    message: {
        type: String,
        defautl: '',
        required: true,
    },
})
</script>
