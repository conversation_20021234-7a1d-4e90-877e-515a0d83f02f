<template>
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
            <div class="flex h-8 w-8 items-center justify-center rounded-xl bg-slate-800">
                <slot name="icon"></slot>
            </div>
            <h2 class="box-title text-sm font-semibold capitalize text-white">
                {{ $t(title) }}
                <span v-if="number" class="text-green-400"> ({{ number }}) </span>
            </h2>
        </div>
        <slot name="right-content"></slot>
    </div>
</template>

<script setup>
defineProps({
    title: {
        type: String,
        default: 'title',
    },
    number: {
        type: Number,
        default: 0,
    },
})
</script>
