<template>
    <ModulesGameTagsFilter
        v-if="route?.query?.sort !== 'favorite' && !showMobile"
        class="max-lg:hidden"
        :items="gameFilters"
    />
    <CommonDropdown
        v-if="route?.query?.sort !== 'favorite' && showMobile"
        :data="gameFilters"
        className="!normal-case lg:hidden !text-slate-300"
        wrapperClassName="!rounded-lg !border-slate-900 !bg-slate-900"
        selectedIconClass="hidden lg:flex"
        :selected="selectedFilter"
        @change="updateSort"
    />
</template>
<script setup>
import { useGamesStore, useCasinoStore } from '~/stores'
import { GAME_FILTER_KEYWORD } from '~/resources/games'

const { t } = useI18n()
const route = useRoute()
const isCasino = route.name.includes('casino')
const { showMobile } = useCommon()

const gamesStoreInstance = useGamesStore()
const { isLoading: isLoadingGames, sort: gameSort } = storeToRefs(gamesStoreInstance)
const { setSort: setSortGames } = gamesStoreInstance

const casinoStoreInstance = useCasinoStore()
const { isLoading: isLoadingCasinos, sort: casinoSort } = storeToRefs(casinoStoreInstance)
const { setSort: setSortCasinos } = casinoStoreInstance

const gameFilters = computed(() => {
    return GAME_FILTER_KEYWORD.map(item => ({
        ...item,
        label: t(item.label),
        icon: 'icon',
    }))
})

const selectedFilter = computed(() => {
    if (!gameFilters?.value.length) return null

    const existProvider = gameFilters?.value?.find(item => {
        if (isCasino) {
            return item.value === casinoSort.value
        }
        return item.value === gameSort.value
    })
    return existProvider
})

const updateSort = item => {
    if (!isCasino) {
        if (isLoadingGames.value) return
        setSortGames(item.value)
    } else {
        if (isLoadingCasinos.value) return
        setSortCasinos(item.value)
    }
}
</script>
