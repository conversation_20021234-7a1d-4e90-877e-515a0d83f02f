<template>
    <CommonDropdown
        v-if="isLoadedProvider && providerList.length > 2"
        :data="providerList"
        :placeholder="{
            title: $t('filter.provider'),
            icon: '/assets/images/icons/partners/all.svg',
        }"
        className="!normal-case !text-slate-300"
        wrapperClassName="!rounded-lg !border-slate-900 !bg-slate-900"
        selectedIconClass="hidden lg:flex"
        :selected="selectedProvider"
        @change="updateProvider"
    />
</template>
<script setup>
import { useGamesStore, useCasinoStore } from '~/stores'
import capitalize from '~/utils/string/capitalize.ts'

const { providers } = defineProps({
    providers: {
        type: Array,
        default: () => [],
    },
})

const route = useRoute()
const isCasino = route.name.includes('casino')

const gamesStoreInstance = useGamesStore()
const { setPartner: setPartnerGame } = gamesStoreInstance
const { partner: gamePartner } = storeToRefs(gamesStoreInstance)

const casinoStoreInstance = useCasinoStore()
const { setPartner: setPartnerCasino } = casinoStoreInstance
const { partner: casinoPartner } = storeToRefs(casinoStoreInstance)

const isLoadedProvider = ref(false)
const providerList = ref([])

watch(
    () => providers,
    newVal => {
        try {
            providerList.value = newVal?.map(item => ({
                ...(item || {}),
                label: capitalize(item?.name),
                value: item?.key === 'all' ? '' : item?.key,
                icon: 'icon',
            }))

            isLoadedProvider.value = true
        } catch (error) {
            console.error('Error when watch providers', error)
        }
    },
    { immediate: true }
)

const selectedProvider = computed(() => {
    if (!providerList?.value.length) return null

    const existProvider = providerList?.value?.find(item => {
        if (isCasino) {
            return item.key === casinoPartner.value
        }
        return item.key === gamePartner.value
    })

    if (!existProvider) {
        return {
            ...providerList.value?.[0],
            icon: 'icon',
        }
    }

    return {
        ...existProvider,
        icon: 'icon',
    }
})

const updateProvider = item => {
    if (!isCasino) {
        setPartnerGame(item.value)
    } else {
        setPartnerCasino(item.value)
    }
}
</script>
