<template>
    <div
        v-if="totalItems > GAME_DEFAUL_LIMIT && page < totalPage"
        class="mt-5 flex flex-col items-center justify-center pb-0 xl:!mt-8 xl:pb-8"
    >
        <CommonButton
            :loading="isLoading"
            type="button"
            class="max-xl:h-[36px] max-xl:min-w-[120px] max-xl:rounded-lg max-xl:px-4 max-xl:text-sm"
            @click="handleShowMore"
        >
            {{ $t('common.show_more') }} ({{ items }} / {{ totalItems }})
        </CommonButton>
    </div>
</template>
<script setup>
import { GAME_DEFAUL_LIMIT } from '~/constants/game'
import { useGamesStore, useCasinoStore } from '~/stores'

const route = useRoute()
const isCasino = route.name.includes('casino')

const gamesStoreInstance = useGamesStore()
const {
    isLoading: isLoadingGames,
    games,
    totalItems: totalItemsGame,
    page: pageGames,
    totalPage: totalPageGame,
} = storeToRefs(gamesStoreInstance)
const { setPage: setPageGames } = gamesStoreInstance

const casinoStoreInstance = useCasinoStore()
const {
    isLoading: isLoadingCasinos,
    casinos,
    totalItems: totalItemsCasino,
    page: pageCasino,
    totalPage: totalPageCasino,
} = storeToRefs(casinoStoreInstance)
const { setPage: setPageCasino } = casinoStoreInstance

const isLoading = computed(() => {
    if (isCasino) {
        return isLoadingCasinos.value
    }
    return isLoadingGames.value
})

const items = computed(() => {
    if (isCasino) {
        return casinos.value.length
    }
    return games.value.length
})

const totalItems = computed(() => {
    if (isCasino) {
        return totalItemsCasino.value
    }
    return totalItemsGame.value
})

const page = computed(() => {
    if (isCasino) {
        return pageCasino.value
    }
    return pageGames.value
})

const totalPage = computed(() => {
    if (isCasino) {
        return totalPageCasino.value
    }
    return totalPageGame.value
})

const handleShowMore = () => {
    if (isLoading.value || page.value === totalPage.value) return

    if (isCasino) {
        setPageCasino(Number(page.value) + 1)
    } else {
        setPageGames(Number(page.value) + 1)
    }
}
</script>
