<script setup>
import { storeToRefs } from 'pinia'

import { useGamesGoStore } from '~/stores'
import { GAME_TYPE } from '~/constants'

const useUserStoreInstance = useUserStore()
const useModalStoreInstance = useModalStore()
const gamesGoStoreInstance = useGamesGoStore()
const { gamesGo } = storeToRefs(gamesGoStoreInstance)
const { isLoggedIn } = storeToRefs(useUserStoreInstance)
const { showLoginModal } = storeToRefs(useModalStoreInstance)
const commonStoreInstance = useCommonStore()
const { setCurrentGame } = commonStoreInstance
const { currentGame } = storeToRefs(commonStoreInstance)

const model = defineModel({ default: false })

const ModalWindow = ref(null)

const closeModal = () => {
    model.value = false
}

const jackpotGamesDisplay = computed(() => {
    return gamesGo.value.filter(item => item?.amount > 0)
})

const isLiveGame = id => {
    return ['vgmn_110', 'vgmn_109'].includes(id)
}

const { handleShowGame } = useGame()

const { handleShowGame: handleShowGameCasino } = useCasino()

const playGame = game => {
    if (isLoggedIn.value) {
        if (game.api_url.startsWith('/gameUrl')) {
            handleShowGame(game)
        } else {
            handleShowGameCasino(game)
        }
    } else {
        const toSend = game
            ? {
                  partnerProvider: game.partner_provider,
                  partnerGameId: game.partner_game_id,
                  name: game?.name,
                  image: game?.image,
              }
            : currentGame.value.data
        setCurrentGame(
            game.api_url.startsWith('/gameUrl') ? GAME_TYPE.game : GAME_TYPE.casino,
            toSend
        )
        showLoginModal.value = true
    }
}
</script>
<template>
    <div v-if="model" class="modal-open relative z-[79]">
        <div
            class="pointer-events-none fixed inset-0 overscroll-none bg-black bg-opacity-70 transition-opacity"
        ></div>

        <div class="fixed inset-0 z-10 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-3 sm:p-0">
                <div ref="ModalWindow" class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body">
                            <div class="mega-jackpot-modal">
                                <div class="effect-box"></div>
                                <div class="mega-jackpot-modal__close" @click="closeModal">
                                    <i class="icon-close-mb" />
                                </div>
                                <div class="modal-jackpot__content">
                                    <div class="game-list">
                                        <div
                                            class="game-item"
                                            v-for="(game, index) in jackpotGamesDisplay"
                                            :key="index"
                                            @click.stop="playGame(game)"
                                        >
                                            <div
                                                class="absolute left-1/2 top-[6px] z-[2] -translate-x-1/2"
                                            >
                                                <div
                                                    v-if="game.amount"
                                                    class="z-10 flex items-center justify-center"
                                                >
                                                    <div
                                                        class="min-w-[132px] rounded-l-full rounded-r-full bg-[#00000094]"
                                                    >
                                                        <div
                                                            class="flex items-center justify-center gap-1 px-[6px] py-[2px] text-[10px] font-semibold leading-4 text-[#FFB631] 2xl:text-xs"
                                                        >
                                                            <img
                                                                :src="'/assets/images/game/currency.svg'"
                                                                width="14"
                                                                height="14"
                                                            />
                                                            <CommonAnimatedNumber
                                                                :animationDuration="5000"
                                                                :number="game.amount"
                                                                :previousNumber="
                                                                    Math.round(
                                                                        game.amount * (3 / 5)
                                                                    )
                                                                "
                                                                class="inline-block text-left"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="game-item__img"
                                                :class="{
                                                    live: isLiveGame(game.id),
                                                }"
                                            >
                                                <img
                                                    :src="`/assets/images/common/mega-jackpot/modal/go_${game.id}.png`"
                                                    alt="game-item"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.modal-dialog {
    @apply w-full max-w-[612px] transition-all;

    @media (max-width: 1199px) and (max-aspect-ratio: 1/1) {
        @apply max-w-[343px];
    }

    @media (max-height: 370px) {
        margin: 4dvh auto;
    }
}

.modal-content {
    @apply relative overflow-hidden rounded-[16px] bg-[#eab045];
    background: url('/assets/images/common/mega-jackpot/modal-bg.jpg') no-repeat center top / 100%
        100%;

    &::before {
        content: '';
        @apply absolute left-1/2 top-1/2 h-full w-full -translate-x-1/2 -translate-y-1/2 transform;
        background: url('/assets/images/common/mega-jackpot/modal-object1.png') no-repeat center
            center / 100% auto;
    }

    &::after {
        content: '';
        @apply pointer-events-none absolute bottom-0 left-1/2 z-[1] h-[254px] w-[612px] -translate-x-1/2 transform;
        background: url('/assets/images/common/mega-jackpot/modal-object2.png') no-repeat center
            center / 100% 100%;
    }
}

.modal-body {
    @apply relative z-[2] bg-transparent p-0;
}

.mega-jackpot-modal {
    @apply relative;

    .effect-box {
        @apply pointer-events-none absolute -top-[44px] h-[109px] w-full overflow-hidden;

        @media (max-width: 1199px) {
            @media (min-aspect-ratio: 1/1) {
                display: none;
            }

            @apply top-0;
        }

        &:before {
            content: '';
            background: url('/assets/images/common/mega-jackpot/modal-effect1.png') no-repeat center
                center/ 100% auto;
            animation: spin 6s linear infinite;
            @apply pointer-events-none absolute -bottom-[280px] left-1/2 h-[544px] w-[540px] -translate-x-1/2;
        }
    }

    &__close {
        @apply absolute right-[16px] top-[-29px] z-[2] flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-full bg-[#ffd78f];

        &:hover {
            @apply scale-110 transform;
        }
        i {
            @apply text-[16px] font-semibold text-[#1e1e1e];
        }

        @media (max-width: 1199px) and (max-aspect-ratio: 1/1) {
            @apply right-[8px] top-[8px];
        }
    }

    .modal-jackpot__content {
        @apply relative m-[44px_28px_27px] flex h-full items-end pt-[65px];

        @media (max-width: 1199px) {
            @apply pt-0;

            @media (max-aspect-ratio: 1/1) {
                @apply m-[0_12px_12px] pt-[109px];
            }
        }
    }

    .game-list {
        background: url('/assets/images/common/mega-jackpot/modal-content-bg.png') no-repeat center
            top/ 100% auto;
        background-color: #f3e6b4;
        @apply relative flex min-h-[172px] w-full flex-wrap justify-center gap-[12px] rounded-[16px] p-[40px_20px_20px];

        @media (max-width: 1199px) and (max-aspect-ratio: 1/1) {
            background: url('/assets/images/common/mega-jackpot/mb-modal-content-bg.png') no-repeat
                center top/ 100% 100%;
            @apply min-h-[164px] p-[40px_12px_12px];
        }
    }

    .game-item {
        @apply relative h-[112px] w-[164px] cursor-pointer overflow-hidden rounded-[8px];

        @media (max-width: 1199px) and (max-aspect-ratio: 1/1) {
            @apply aspect-[141/112] w-[47.9%];
        }

        .amount {
            background: url('/assets/images/common/mega-jackpot/modal/bg-amount.png') no-repeat
                center center/ 100% 100%;
            @apply absolute left-1/2 top-[8px] flex h-[24px] min-w-[128px] -translate-x-1/2 transform items-center justify-center gap-[6.75px] overflow-hidden rounded-[4px] p-[0_4px] text-[16px] font-medium text-[#ffdb21];

            @media (max-width: 1199px) {
                @apply text-[14px];
            }
        }
        .game-item__img {
            @apply h-full;

            &.live {
                @apply relative overflow-hidden;
                animation: lightAnimation 1s ease-in-out infinite;

                &:before,
                &:after {
                    @apply absolute size-full rounded-lg content-[''];

                    border: 3px solid rgba(255, 255, 255, 0.5);
                    animation: borderRotate 3s linear infinite;
                }

                &:before {
                    animation-delay: -1.5s;
                }
            }

            img {
                @apply h-full w-full object-cover;
            }
        }
    }
    &:after {
        @apply pointer-events-none absolute left-1/2 top-[-6px] h-[92px] w-[162px] -translate-x-1/2 transform;
        content: '';
        background: url('/assets/images/common/mega-jackpot/modal-content-logo.png') no-repeat
            center center/ 100% 100%;

        @media (max-width: 1199px) {
            @media (max-aspect-ratio: 1/1) {
                @apply top-[44px] h-[81px] w-[145px];
            }

            @apply -top-[57px];
        }
    }
}

@keyframes spin {
    0% {
        transform: translateX(-50%) rotate(0deg) scale(1);
    }
    50% {
        transform: translateX(-50%) rotate(180deg) scale(2);
    }
    100% {
        transform: translateX(-50%) rotate(360deg) scale(1);
    }
}

@keyframes borderRotate {
    0% {
        clip-path: polygon(0 0, 30% 0, 30% 100%, 0 100%);
    }
    25% {
        clip-path: polygon(0 0, 100% 0, 100% 30%, 0 30%);
    }
    50% {
        clip-path: polygon(70% 0, 100% 0, 100% 100%, 70% 100%);
    }
    75% {
        clip-path: polygon(0 70%, 100% 70%, 100% 100%, 0 100%);
    }
    100% {
        clip-path: polygon(0 0, 30% 0, 30% 100%, 0 100%);
    }
}
</style>
