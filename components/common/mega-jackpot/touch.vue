<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useGamesGoStore, useUserStore } from '~/stores'
import { useWindowSize } from '@vueuse/core'

const { width } = useWindowSize()

const useGamesGoStoreInstance = useGamesGoStore()
const { gamesGo, bigWin, totalJackpot } = storeToRefs(useGamesGoStoreInstance)

const useUserStoreInstance = useUserStore()
const { isLoggedIn } = storeToRefs(useUserStoreInstance)

const { showMobile } = useCommon()
const { get, set } = useLocalStorage()
const { dragEnd, drag, dragStart, draggable, isDragging } = useDrag()
const { triggerAlert, alertRef, alertBoxRef, checkShowPosition } = useWinnerAlert()

const showTouch = ref(get('mega-jackpot-touch-show') ?? true)
const visibleModal = ref(false)

const totalJackpotDisplay = computed(() => {
    if (totalJackpot.value > 1_000_000_000) {
        return {
            value: Number(formatToBillion(totalJackpot.value)),
            currency: 'tỷ',
        }
    }
    return {
        value: Number(formatToMillion(totalJackpot.value)),
        currency: 'triệu',
    }
})

const formatToBillion = (amount: number) => {
    const billion = 1_000_000_000
    const result = amount / billion
    if (Number.isInteger(result)) {
        return `${result}`
    }
    return `${result.toFixed(2)}`
}

const formatToMillion = (amount: number) => {
    const million = 1_000_000
    const result = amount / million
    if (Number.isInteger(result)) {
        return `${result}`
    }
    return `${result.toFixed(2)}`
}

const openModal = () => {
    if (!showTouch.value || isDragging.value || visibleModal.value) return
    visibleModal.value = true
}

const handleCloseTouch = () => {
    showTouch.value = false

    draggable.value?.removeEventListener('touchend', dragEnd)
    document.removeEventListener('mouseup', dragEnd)
    document.removeEventListener('touchmove', drag)
    document.removeEventListener('mousemove', drag)
}

watch(showTouch, status => {
    set('mega-jackpot-touch-show', status)

    if (status) {
        const position = checkShowPosition()

        if (position === 'show-right') {
            alertBoxRef.value?.classList.remove('left')
            alertBoxRef.value?.classList.add('right')

            return
        }

        alertBoxRef.value?.classList.remove('right')
        alertBoxRef.value?.classList.add('left')
    }
})

watch(gamesGo, (newVal, oldVal) => {
    if (newVal?.length > oldVal?.length) {
        showTouch.value = true
    }
})

watch(
    bigWin,
    newVal => {
        if (!showTouch.value) {
            showTouch.value = true
        }

        if (newVal?.username) {
            triggerAlert()
        }
    },
    { deep: true }
)

watch(isDragging, status => {
    if (!status) {
        const position = checkShowPosition()

        if (position === 'show-right') {
            alertBoxRef.value?.classList.remove('left')
            alertBoxRef.value?.classList.add('right')

            return
        }

        alertBoxRef.value?.classList.remove('right')
        alertBoxRef.value?.classList.add('left')
    }
})

watch(
    () => isLoggedIn.value,
    newVal => {
        if (newVal) {
            localStorage.setItem('mega-jackpot-touch-show', 'true')
            showTouch.value = true
        }
    }
)

const handlePositionMiniGame = windowWith => {
    if (draggable.value) {
        let compensateWidth = 140
        let bottom = 220
        if (windowWith <= 768) {
            bottom = 180
            compensateWidth = 110
        }

        const left = windowWith - compensateWidth

        draggable.value.style.left = `${left}px`
        draggable.value.style.bottom = `${bottom}px`
    }
}

watch(width, newW => {
    handlePositionMiniGame(newW)
})

onMounted(() => {
    if (draggable.value) {
        handlePositionMiniGame(window.innerWidth)

        // Mouse Events
        draggable.value.addEventListener('mousedown', dragStart)

        // Touch Events
        draggable.value.addEventListener('touchstart', dragStart)
    }
})

function useDrag() {
    const draggable = ref<HTMLDivElement | null>(null)
    const isDragging = ref(false)
    const startX = ref<number>()
    const startY = ref<number>()
    const startLeft = ref<number>()
    const startTop = ref<number>()

    const dragStart = (e: MouseEvent | TouchEvent) => {
        if (!draggable.value && isDragging.value) return

        isDragging.value = true

        const rect = draggable.value?.getBoundingClientRect()
        startLeft.value = rect?.left
        startTop.value = rect?.top

        if (e.type === 'touchstart') {
            startX.value = (e as TouchEvent).touches[0].clientX
            startY.value = (e as TouchEvent).touches[0].clientY
        } else {
            startX.value = (e as MouseEvent).clientX
            startY.value = (e as MouseEvent).clientY
        }

        draggable.value?.addEventListener('touchend', dragEnd)
        document.addEventListener('mouseup', dragEnd)
        document.addEventListener('mousemove', drag, { passive: false })
        document.addEventListener('touchmove', drag, { passive: false })
    }

    const dragEnd = async () => {
        isDragging.value = false

        await nextTick()
        const elementRect = draggable.value?.getBoundingClientRect()
        const targetX = elementRect?.x || startLeft.value!
        const targetY = elementRect?.y || startTop.value!

        if (
            startLeft.value! - targetX === 0 &&
            startTop.value! - targetY === 0 &&
            showTouch.value &&
            !isDragging.value
        ) {
            openModal()
        }

        draggable.value?.removeEventListener('touchend', dragEnd)
        document.removeEventListener('mouseup', dragEnd)
        document.removeEventListener('touchmove', drag)
        document.removeEventListener('mousemove', drag)
    }

    const drag = (e: MouseEvent | TouchEvent) => {
        if (!isDragging.value) return
        e?.preventDefault()

        let pointerX, pointerY

        if (e.type === 'touchmove') {
            pointerX = (e as TouchEvent).touches[0].clientX
            pointerY = (e as TouchEvent).touches[0].clientY
        } else {
            pointerX = (e as MouseEvent).clientX
            pointerY = (e as MouseEvent).clientY
        }

        let newLeft = (startLeft?.value ?? 0) + (pointerX - (startX.value ?? 0))
        const newTop = (startTop?.value ?? 0) + (pointerY - (startY.value ?? 0))

        const windowWidth = window.innerWidth
        const windowHeight = window.innerHeight
        const elementRect = draggable.value?.getBoundingClientRect()
        const scrollX = showMobile.value ? 0 : 20
        newLeft = Math.min(Math.max(0, newLeft), windowWidth - scrollX - (elementRect?.width ?? 0))
        const newBottom = Math.min(
            Math.max(0, windowHeight - newTop - (elementRect?.height ?? 0)),
            windowHeight - (elementRect?.height ?? 0)
        )

        if (draggable.value) {
            draggable.value.style.left = `${newLeft}px`
            draggable.value.style.bottom = `${newBottom}px`
        }
    }

    return {
        draggable,
        dragStart,
        dragEnd,
        drag,
        isDragging,
        startLeft,
        startTop,
    }
}

function useLocalStorage() {
    const get = (key: 'mega-jackpot-touch-show'): boolean | number | string | null => {
        const value = localStorage.getItem(key)
        if (value === null) return null
        if (value === 'true') return true
        if (value === 'false') return false
        if (!isNaN(Number(value))) return Number(value)
        try {
            return JSON.parse(value)
        } catch {
            return value
        }
    }
    const set = (key: 'mega-jackpot-touch-show', value: boolean | number | string) => {
        localStorage.setItem(key, JSON.stringify(value))
    }
    const remove = (key: 'mega-jackpot-touch-show') => {
        localStorage.removeItem(key)
    }
    return { get, set, remove }
}

function useWinnerAlert() {
    const alertRef = ref<HTMLDivElement | null>(null)
    const alertBoxRef = ref<HTMLDivElement | null>(null)

    const triggerAlert = (duration: number = 5 * 1000) => {
        activeAlert()

        setTimeout(() => {
            addClassInActive()
        }, duration)
    }

    const activeAlert = () => {
        alertRef.value?.classList.remove('hidden')
    }

    const addClassInActive = () => {
        if (!draggable.value || !alertRef.value) return

        alertRef.value.classList.add('hidden')

        bigWin.value = undefined
    }

    const checkShowPosition = () => {
        if (!draggable.value || !alertRef.value) return

        const draggableRect = draggable.value?.getBoundingClientRect()
        const left = draggableRect?.left
        const WIDTH_ALERT = showMobile.value ? 230 : 280

        if (left < WIDTH_ALERT) {
            return 'show-right'
        }

        return 'show-left'
    }

    return {
        triggerAlert,
        alertRef,
        alertBoxRef,
        checkShowPosition,
    }
}
</script>

<template>
    <div v-show="showTouch && totalJackpot > 0" ref="draggable" class="fixed z-[53]">
        <div class="mega-jackpot-touch">
            <div class="close-jackpot" @click.stop="handleCloseTouch" @touchend.stop @mouseup.stop>
                <img :src="'/assets/images/close.png'" alt="close-jackpot-icon" />
            </div>
            <div class="jackpot-display">
                <img :src="'/assets/images/common/mega-jackpot/touch-icon.png'" alt="touch-icon" />
            </div>
            <div class="jackpot-logo">
                <img :src="'/assets/images/common/mega-jackpot/touch-logo.png'" alt="icon" />
                <p>Hũ Sắp Nổ</p>
            </div>
            <div class="jackpot-amount">
                <strong>
                    <CommonAnimatedNumber
                        :animationDuration="2000"
                        :number="totalJackpotDisplay.value"
                        :previousNumber="Math.round(totalJackpotDisplay.value * (3 / 5))"
                        :suffix="` ${totalJackpotDisplay.currency}`"
                    />
                </strong>
            </div>
        </div>
        <div class="winner-alert-box left" ref="alertBoxRef">
            <div class="winner-alert-box-wrapper">
                <div class="winner-alert hidden" ref="alertRef">
                    <p>Chúc mừng {{ bigWin?.username }}</p>
                    <p>
                        Chiến thắng <span>{{ bigWin?.winlost_txt }}</span> từ {{ bigWin?.product }}!
                    </p>
                </div>
            </div>
        </div>
    </div>

    <LazyCommonMegaJackpotModal v-model="visibleModal" />
</template>

<style scoped lang="scss">
@import url('https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap');

.mega-jackpot-touch {
    background: url('/assets/images/common/mega-jackpot/touch-bg.png') no-repeat center 4px/114px
        92px;

    @apply h-[114px] w-[114px] cursor-pointer select-none p-[0_7px];

    .close-jackpot {
        @apply absolute right-0 top-0 z-[3] flex h-10 w-10 items-center justify-center p-2.5;
    }

    .jackpot-display img {
        animation: wobble-hor-bottom 1.1s both infinite;

        @apply pointer-events-none m-0 mx-auto mt-[6px] h-[46px] w-auto object-scale-down;
    }

    .jackpot-logo {
        @apply pointer-events-none relative z-1 -mt-[14px] h-[38px];

        img {
            @apply mx-auto h-[20px] w-auto;
        }

        p {
            @apply text-center font-openSans text-xs font-semibold italic text-dark-900;
        }
    }

    .jackpot-amount {
        background: url('/assets/images/common/mega-jackpot/touch-button.png') no-repeat center
            center / 58px 22px;
        font-family: 'Roboto Condensed', serif;

        @apply pointer-events-none relative mx-auto flex h-[22px] w-[58px] items-center justify-center;
        @apply text-sm font-black italic text-[#F84E50];
    }
}

.winner-alert-box {
    @apply pointer-events-none absolute bottom-[20px] -z-[1] flex h-[60px] w-[230px] flex-col justify-center overflow-hidden sm:w-[280px];

    &.left {
        @apply -left-[170px] sm:-left-[227px];

        .winner-alert {
            @apply pr-[45px] text-right;
        }
    }

    &.right {
        @apply -right-[170px] sm:-right-[227px];

        .winner-alert-box-wrapper {
            @apply right-0;
        }

        .winner-alert {
            @apply ml-auto pl-[45px] text-left;

            &:before {
                transform: scaleX(-1);
            }
        }
    }
}

.winner-alert-box-wrapper {
    @apply absolute h-full w-[460px] sm:w-[560px];
}

.winner-alert {
    @apply relative flex h-full w-[230px] flex-col justify-center text-xs text-[#B9590F] transition-all duration-300 sm:w-[280px];

    p {
        @apply relative font-bold italic;
    }

    span {
        font-family: 'Roboto Condensed', serif;
        @apply text-sm font-black text-[#D32D2F] sm:text-base;
    }

    &::before {
        @apply absolute left-0 top-0 h-full w-full content-[''];
        background: url('/assets/images/common/mega-jackpot/winner-alert-bg.png') no-repeat center
            right / 234px 70px;
    }

    &.hidden {
        @apply ml-60 mr-60;
    }
}

@keyframes wobble-hor-bottom {
    0%,
    100% {
        -webkit-transform: translateX(0%);
        transform: translateX(0%);
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
    }
    15% {
        transform: translateX(-4px) rotate(-4deg);
    }
    30% {
        transform: translateX(3px) rotate(4deg);
    }
    45% {
        transform: translateX(-3px) rotate(-3.6deg);
    }
    60% {
        transform: translateX(3px) rotate(2.4deg);
    }
    75% {
        transform: translateX(-2px) rotate(-1.2deg);
    }
}
</style>
