<template>
    <div class="data-table-footer flex items-center justify-between">
        <span class="info"> Trang {{ page }}/{{ totalPages }} </span>

        <div class="data-table-nav flex items-center gap-2">
            <span class="nav" @click="handleChangePrevPage">
                <IconsChevronLeft
                    :class-wrapper="`w-4 ${page === 1 || isLoading ? 'cursor-not-allowed' : ''}`"
                    :fill-color="page === 1 ? 'fill-slate-600' : 'fill-white'"
                />
            </span>
            <span class="info px-3">
                <span class="text-white">{{ page }}</span>
                /<span class="text-slate-300">{{ totalPages }}</span></span
            >
            <span class="nav" @click="handleChangeNextPage">
                <IconsChevronRight
                    :class-wrapper="`w-4 ${page === totalPages || isLoading ? 'cursor-not-allowed' : ''}`"
                    :fill-color="page === totalPages || isLoading ? 'fill-slate-600' : 'fill-white'"
                />
            </span>
        </div>
    </div>
</template>
<script setup>
import { PAGINATION } from '~/constants'

const { isLoading, limit, totalPage } = defineProps({
    isLoading: {
        type: Boolean,
        default: false,
    },
    limit: {
        type: Number,
        required: true,
    },
    totalPage: {
        type: Number,
        required: true,
    },
})

const totalPages = computed(() => {
    return Math.ceil(totalPage / limit)
})

const page = defineModel({ default: PAGINATION.START_PAGE, required: true })

const handleChangePrevPage = () => {
    if (page.value === 1 || isLoading) return
    page.value -= 1
}

const handleChangeNextPage = () => {
    if (page.value === totalPages.value || isLoading) return
    page.value += 1
}
</script>
<style lang="scss" scoped>
.data-table-nav > span {
    @apply flex h-8 cursor-pointer items-center justify-center rounded-lg border border-slate-950 bg-slate-950;
}
.data-table-nav .nav {
    @apply w-8 cursor-pointer;
}

.data-table-footer .info {
    @apply text-xs font-semibold leading-[18px] text-slate-300;
}
</style>
