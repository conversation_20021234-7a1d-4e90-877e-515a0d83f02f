<template>
    <div class="relative w-full">
        <div v-if="dataSet.length > 0" class="flex flex-row justify-between divide-x border-b">
            <template v-for="(data, index) in dataSet" :key="index">
                <div
                    class="relative flex h-[200px] w-full flex-col items-center justify-end pb-2"
                    :data-tooltip-target="`tooltip-default-${data.day}`"
                    data-tooltip-placement="top"
                >
                    <!-- tooltip -->
                    <div
                        :id="`tooltip-default-${data.day}`"
                        role="tooltip"
                        class="tooltip invisible absolute z-10 inline-block rounded bg-white px-3 py-2 text-sm font-medium opacity-0 shadow"
                    >
                        <div class="flex flex-col">
                            <div class="flex items-center gap-2">
                                <div class="h-2 w-2 rounded-full bg-emerald-300"></div>
                                <div class="min-w-max">
                                    {{ $t('common.bet') }}
                                </div>
                                <div class="text-nowrap">
                                    {{ NumberUtils.formatAmount(data.bet, '') }}
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <div class="h-2 w-2 rounded-full bg-[#FFBF0D]"></div>
                                <div class="min-w-max">
                                    {{ $t('common.cashback') }}
                                </div>
                                <div class="text-nowrap">
                                    {{ NumberUtils.formatAmount(data.return, '') }}
                                </div>
                            </div>
                        </div>

                        <div class="tooltip-arrow" data-popper-arrow />
                    </div>
                    <!-- vertical progress bar -->
                    <!-- bet part -->
                    <div
                        class="bg-gradient-to-br absolute w-6 rotate-180 overflow-hidden rounded-xl from-[#5FD1BA] to-[#5FD1BA]"
                        :style="`height: ${maxBetHeight(data.bet)}px; z-index: ${coverNumber(data.bet) > coverNumber(data.return) ? 1 : 2}`"
                    ></div>
                    <!-- return part -->
                    <div
                        class="bg-gradient-to-br absolute h-full w-6 rounded-xl from-[#FFBF0D] to-rose-600"
                        :style="`height: ${maxReturnPart(data.return)}px; z-index: ${coverNumber(data.return) > coverNumber(data.bet) ? 1 : 2}`"
                    ></div>
                    <!-- <div class="mt-2 text-sm font-semibold">
                        {{ data.date }}
                    </div> -->
                </div>
            </template>
        </div>
        <div v-if="dataSet.length > 0" class="flex flex-row justify-between">
            <template v-for="(data, index) in dataSet" :key="index">
                <div class="relative flex w-full flex-col items-center">
                    <div class="mt-2 text-sm font-semibold">
                        {{ data.date }}
                    </div>
                </div>
            </template>
        </div>
        <div v-else>{{ $t('common.no_data') }}</div>
    </div>
</template>
<script setup>
import { NumberUtils } from '~/utils'
import { initTooltips } from 'flowbite'
const { dataSet } = defineProps({
    dataSet: {
        type: Array,
        default: () => [
            { date: '21/08', day: 'MON', bet: 500, return: 5 },
            { date: '22/08', day: 'TUE', bet: 200, return: 2 },
            { date: '23/08', day: 'WED', bet: 300, return: 3 },
            { date: '24/08', day: 'THU', bet: 700, return: 7 },
            { date: '25/08', day: 'FRI', bet: 50, return: 0.5 },
            { date: '26/08', day: 'SAT', bet: 200, return: 2 },
            { date: '27/08', day: 'SUN', bet: 300, return: 3 },
        ],
    },
})
const coverNumber = number => {
    return Math.round(parseFloat(String(number).replace(/,/g, '')))
}
const maxBet = computed(() => {
    return Math.max(...dataSet.map(o => coverNumber(o.bet)))
})
const maxBetHeight = number => {
    const newNumber = coverNumber(number)
    // 200 is the maximum height in px
    return Math.round((newNumber * 200) / maxBet.value)
}

const maxReturnPart = number => {
    if (maxBet.value === 0) return 0
    const newNumber = coverNumber(number)
    // 200 is the maximum height in px
    return Math.round(200 * (newNumber / maxBet.value))
}
onMounted(() => {
    initTooltips()
})
</script>
