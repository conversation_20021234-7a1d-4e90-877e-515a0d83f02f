<template>
    <div
        v-if="show"
        :class="[
            classWrapper,
            'custom-modal relative',
            {
                'is-slide-modal z-[55] lg:hidden': type === MODAL_TYPE_STYLE.slide,
                'is-popup-modal z-[80]': type === MODAL_TYPE_STYLE.popup,
            },
        ]"
    >
        <div
            v-if="type === MODAL_TYPE_STYLE.popup"
            class="custom-modal-backdrop pointer-events-none fixed inset-0 z-[1002] overscroll-none bg-black bg-opacity-70 transition-opacity"
        ></div>

        <div
            :class="[
                'custom-modal-content fixed inset-0 overflow-y-auto',
                {
                    'height-slide-modal z-[1001]': type === MODAL_TYPE_STYLE.slide,
                    'z-[1002]': type === MODAL_TYPE_STYLE.popup,
                },
            ]"
        >
            <div
                :class="[
                    'flex justify-center sm:p-0 lg:items-center lg:p-4',
                    {
                        'animate-slide-right height-slide-modal':
                            type === MODAL_TYPE_STYLE.slide && isAnimationSlideModal,
                        'height-slide-modal': type === MODAL_TYPE_STYLE.slide,
                        'animate-slide-up h-full items-end lg:items-center': type === MODAL_TYPE_STYLE.popup,
                    },
                ]"
            >
                <div
                    ref="ModalWindow"
                    data-modal-content
                    :class="[
                        classes,
                        'custom-modal-content-inner relative w-full transform overflow-hidden bg-white transition-all lg:my-8',
                        {
                            'sm:max-w-sm': size === 'sm' && type === MODAL_TYPE_STYLE.popup,
                            'sm:max-w-md': size === 'md' && type === MODAL_TYPE_STYLE.popup,
                            'sm:max-w-lg': size === 'lg' && type === MODAL_TYPE_STYLE.popup,
                            'sm:max-w-xl': size === 'xl' && type === MODAL_TYPE_STYLE.popup,
                            'sm:max-w-2xl': size === 'xxl' && type === MODAL_TYPE_STYLE.popup,
                            'rounded-t-3xl lg:rounded-2xl': type === MODAL_TYPE_STYLE.popup,
                        },
                    ]"
                >
                    <div :class="['custom-modal-header relative text-center', classHeaderWrapper]">
                        <div
                            v-if="$slots['header'] || type === MODAL_TYPE_STYLE.slide"
                            :class="[
                                'relative z-1 text-center',
                                {
                                    'flex items-end justify-center px-4 pb-0 pt-5 lg:px-8 lg:pt-8':
                                        type === MODAL_TYPE_STYLE.popup,
                                    'px-3 py-4': type === MODAL_TYPE_STYLE.slide,
                                },
                            ]"
                        >
                            <IconsChevronLeft
                                v-if="type === MODAL_TYPE_STYLE.slide"
                                class-wrapper="w5 h5 absolute left-3 top-4"
                                fill-color="fill-white"
                                @click="emit('back')"
                            />
                            <h2
                                v-if="type === MODAL_TYPE_STYLE.slide"
                                class="custom-modal-title text-base font-semibold capitalize text-white"
                            >
                                {{ $t(title) }}
                            </h2>
                            <slot name="header"></slot>
                        </div>
                        <div
                            v-if="!hideClose && type === MODAL_TYPE_STYLE.popup"
                            class="absolute right-3 top-3 z-10 flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-full bg-slate-700"
                            @click="emit('close')"
                        >
                            <IconsClose fill-color="fill-slate-300" class-wrapper="w-4 h-4" />
                        </div>
                    </div>
                    <div
                        :class="[
                            'custom-modal-body',
                            classContentWrapper,
                            {
                                'pt-5 lg:pt-6': !!$slots['header'] && type === MODAL_TYPE_STYLE.popup,
                                'pt-0': !$slots['header'] && type === MODAL_TYPE_STYLE.popup,
                                'pb-5 lg:pb-6': !!$slots['footer'],
                                'pb-6 lg:pb-8': !$slots['footer'] && type === MODAL_TYPE_STYLE.popup,
                                'px-4 lg:px-8': type === MODAL_TYPE_STYLE.popup,
                                'px-3 pt-4': type === MODAL_TYPE_STYLE.slide,
                            },
                        ]"
                    >
                        <slot></slot>
                    </div>
                    <div
                        v-if="$slots['footer']"
                        :class="`custom-modal-footer flex flex-col gap-5 px-4 pb-5 pt-0 lg:gap-6 lg:px-8 lg:pb-8 ${classFooterrWrapper}`"
                    >
                        <slot name="footer"></slot>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { onClickOutside } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { MODAL_TYPE_STYLE } from '~/constants'
import { useModalStore, useShowErrorStore } from '~/stores'

const { show, sticky, type } = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: '',
    },
    hideClose: {
        type: Boolean,
        default: false,
    },
    sticky: {
        type: Boolean,
        default: false,
    },
    size: {
        type: String,
        default: 'lg',
    },
    classes: {
        type: String,
        default: '',
    },
    type: {
        type: String,
        default: MODAL_TYPE_STYLE.popup,
    },
    classContentWrapper: {
        type: String,
        default: '',
    },
    classHeaderWrapper: {
        type: String,
        default: '',
    },
    classFooterrWrapper: {
        type: String,
        default: '',
    },
    classWrapper: {
        type: String,
        default: '',
    },
})

const useModalStoreInstance = useModalStore()
const { showErrorModal } = storeToRefs(useModalStoreInstance)

const useShowErrorStoreInstance = useShowErrorStore()
const { modalData } = storeToRefs(useShowErrorStoreInstance)

const route = useRoute()

const emit = defineEmits(['close'])
const ModalWindow = ref(null)

const isAnimationSlideModal = computed(() => {
    return ![
        '/user/transaction-history',
        '/user/bet-history',
        '/huong-dan-giao-dich',
        '/huong-dan-dang-ky',
        '/huong-dan-nap-tien',
        '/huong-dan-rut-tien',
    ].includes(route.path)
})

onClickOutside(
    ModalWindow,
    () => {
        if (!ModalWindow.value) return

        if (showErrorModal.value && !modalData.value?.clickOutSide) return

        if ((!sticky && type === MODAL_TYPE_STYLE.popup) || modalData.value?.clickOutSide) {
            emit('close', {
                routePath: route.path,
            })
        }
    },
    { ignore: ['data-modal-content'] }
)
</script>
<style lang="scss" scoped>
.is-popup-modal .custom-modal-content {
    &-inner {
        background: linear-gradient(248.4deg, #05101b -10.91%, #0c2033 45.39%, #081a2c 101.17%);
        border: 1px solid rgba($color: #fff, $alpha: 0.1);
    }
}

.animate-slide-up {
    transform: translateY(100%);
    animation: slide-up 0.3s forwards;
    @media screen and (min-width: 1023px) {
        transform: none;
        animation: none;
    }
}

.animate-slide-right {
    transform: translateX(100%);
    animation: slide-right 0.3s forwards;
    @media screen and (min-width: 1023px) {
        transform: none;
        animation: none;
    }
}

.is-slide-modal .custom-modal-body {
    height: calc(100dvh - 80px - 57px);
    overflow-y: auto;
    overflow-x: hidden;
}

.height-slide-modal {
    height: 100dvh;
    overflow: hidden;
}

.notification-modal {
    &.is-slide-modal {
        .custom-modal-body {
            height: calc(100dvh - 57px);
        }
    }
    .height-slide-modal {
        margin-bottom: 0;
    }
}

@keyframes slide-up {
    0% {
        transform: translateY(100%);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes slide-right {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(0);
    }
}
</style>
