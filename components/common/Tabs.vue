<template>
    <div :class="`custom-tabs ${classWrapper}`">
        <!-- Tab Navigation -->
        <div
            :class="`nav-tabs max-lg:flex max-lg:w-screen max-lg:overflow-x-auto max-lg:overflow-y-hidden lg:grid lg:grid-cols-12 ${classNavWrapper}`"
        >
            <div
                :class="`nav-tabs-inner ${tabs[activeTabIndex].classNavInner ? tabs[activeTabIndex].classNavInner : 'col-span-12 col-start-1'}`"
            >
                <div
                    v-for="(tab, index) in tabs"
                    :key="index"
                    :class="[
                        'nav-tab-item',
                        classTabWrapper,
                        {
                            active: index === activeTabIndex,
                            'is-maintenance': tab.isMaintenance,
                        },
                    ]"
                    @click="setActiveTab(index)"
                >
                    <component
                        :is="tab.icon"
                        v-if="tab.icon && checkComponentValidity(tab.icon)"
                        :fill-color="index === activeTabIndex ? 'fill-green-400' : 'fill-white'"
                        class-wrapper="w-5"
                    />
                    <span
                        class="nav-item-title whitespace-nowrap first-letter:uppercase"
                        :data-title="tab.title"
                    >
                        {{ $t(tab.title) }}
                    </span>
                    <img
                        v-if="tab.isMaintenance"
                        class="w-13 absolute left-1/2 top-0 h-3 -translate-x-1/2 -translate-y-1/2 transform"
                        :src="'/assets/images/v2/tags/bao-tri.webp'"
                        alt="maintenance"
                    />
                </div>
            </div>
        </div>

        <!-- Tab Content -->
        <div :class="`tab-content lg:grid lg:grid-cols-12 ${classContentWrapper}`">
            <div
                :class="
                    tabs[activeTabIndex].classContentInner
                        ? tabs[activeTabIndex].classContentInner
                        : `col-span-12 col-start-1`
                "
            >
                <keep-alive>
                    <component :is="tabs[activeTabIndex].content" />
                </keep-alive>
            </div>
        </div>
    </div>
</template>
<script setup>
const { tabs } = defineProps({
    tabs: {
        type: Array,
        required: true,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    classNavWrapper: {
        type: String,
        default: '',
    },
    classTabWrapper: {
        type: String,
        default: '',
    },
    classContentWrapper: {
        type: String,
        default: '',
    },
    classContentInner: {
        type: String,
        default: '',
    },
})

const localePath = useLocalePath()
const router = useRouter()
const route = useRoute()

const activeTabIndex = ref(0)

const setActiveTab = index => {
    const activeTab = tabs[index]
    if (activeTab.isMaintenance) {
        return
    }

    if (activeTab?.link) {
        router.push(localePath(activeTab.link))
    }

    activeTabIndex.value = index
}

const checkComponentValidity = component => {
    const isValidComponent = typeof component === 'object' && component.__name

    return isValidComponent
}

watch(
    () => route.href,
    value => {
        const indexOfNewtab = tabs.findIndex(tab => value.startsWith(tab.link))
        activeTabIndex.value = indexOfNewtab === -1 ? 0 : indexOfNewtab
    },
    { immediate: true }
)
</script>
<style lang="scss">
.nav-tabs-inner {
    @apply flex items-center gap-4 border-b border-b-slate-750 max-lg:justify-center lg:gap-6;
}

.nav-tab-item {
    @apply relative flex cursor-pointer items-center gap-1 py-[6px] text-sm font-medium text-slate-300 transition-all lg:py-[10px] lg:text-base;
    &:before {
        @apply absolute -bottom-[1px] left-0 h-[2px] w-full bg-transparent content-[''];
    }
}

.nav-tab-item {
    &.active {
        @apply text-white;
    }
    &.is-maintenance {
        cursor: not-allowed !important;
        @apply relative text-gray-700;
    }
}

.tab-content {
    @apply py-4 lg:pt-6;
}

:deep() {
    .nav-tab-item:not(.is-maintenance):hover,
    .nav-tab-item.active {
        &::before {
            @apply bg-rose-600;
        }
        svg {
            path {
                @apply fill-green-400;
            }
        }
    }
}

.account-box-wrapper {
    @media (min-width: 1024px) {
        .nav-tabs-inner {
            @apply justify-center rounded-full border-b-0 bg-slate-700;
        }
        .nav-tab-item {
            @apply rounded-full text-white lg:px-5 lg:py-3;
            &:not(.is-maintenance) {
                @apply hover:bg-green-400 hover:text-gray-950;
            }
            &::before {
                @apply hidden;
            }
            &.active {
                @apply bg-green-400 text-gray-950;
                svg path {
                    fill: #081321;
                }
            }
        }
    }
}

.user-profile,
.account-box-wrapper {
    @media (max-width: 1023px) {
        .nav-tabs {
            @apply bg-slate-700 pt-0;
        }
        .nav-tabs-inner {
            @apply border-b-0;
        }
        .nav-tab-item {
            padding-top: 14px;
            padding-bottom: 14px !important;
            &::before {
                @apply left-1/2 h-1 w-[18px] -translate-x-1/2 transform rounded-full;
            }
            &.active {
                @apply text-green-400;
                &:before {
                    @apply bottom-0 bg-green-400;
                }
            }
        }
    }
}
</style>
