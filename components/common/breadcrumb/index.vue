<template>
    <nav aria-label="Breadcrumb">
        <ol
            itemscope
            itemtype="https://schema.org/BreadcrumbList"
            :class="`breadcrumb w-[95%] overflow-hidden ${classWrapper}`"
        >
            <li
                v-for="(item, index) in filteredList"
                :key="index"
                itemprop="itemListElement"
                itemscope
                itemtype="https://schema.org/ListItem"
                :class="index === lastIndex ? 'truncate' : ''"
            >
                <NuxtLink
                    v-if="item?.url"
                    :to="localePath(item?.url)"
                    itemprop="item"
                    class="truncate"
                >
                    <span itemprop="name">
                        {{ $t(item?.name) }}
                    </span>
                </NuxtLink>
                <span v-if="!item?.url && item?.name" itemprop="name" class="truncate">
                    {{ $t(item?.name) }}
                </span>
                <meta itemprop="position" :content="index + 1" />
            </li>
        </ol>
    </nav>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useBreadcrumbStore } from '~/stores'
import { EVENT_TABS } from '~/constants/events'
import { HUONG_DAN_NAVS, HELP_NAVS } from '~/constants/staticPage'
import { tabToTitleMap } from '~/resources/football-schedules'

defineProps({
    classWrapper: {
        type: String,
        default: '',
    },
})

const route = useRoute()

const useBreadcrumbInstance = useBreadcrumbStore()

const { list } = storeToRefs(useBreadcrumbInstance)

const localePath = useLocalePath()

const defaultItem = [
    {
        name: 'common.home',
        url: '/',
    },
]

const breadcrumbs = ref(defaultItem)

const filteredList = computed(() => {
    if (list.value && list.value.length > breadcrumbs.value.length) {
        return list.value.filter(item => item?.name)
    }
    return breadcrumbs.value.filter(item => item?.name)
})

const lastIndex = computed(() => filteredList.value.length - 1)

watch(
    () => route,
    newVal => {
        // console.log('Breadcrumb route changed:', newVal.name)
        const eventRoutes = EVENT_TABS.map(item => item.id)
        const helpRoutes = [...HELP_NAVS, ...HUONG_DAN_NAVS].map(item => item.key)

        if (eventRoutes.includes(newVal.name)) {
            const foundTab = EVENT_TABS.find(tab => tab.id === newVal.name)
            breadcrumbs.value = [
                ...defaultItem,
                {
                    url: foundTab?.url || '/su-kien-va-khuyen-mai',
                    name: foundTab?.i18n_key || 'events.all',
                },
            ]
            return
        }

        if (newVal.name === 'promo-slug') {
            breadcrumbs.value = [
                ...defaultItem,
                {
                    url: '/khuyen-mai',
                    name: 'events.promotion',
                },
                {
                    url: `/${route.params.slug}`,
                    name: `meta.${route.params.slug}.breadcrumb`,
                },
            ]
            return
        }

        if (helpRoutes.includes(newVal.name)) {
            const foundTab = [...HELP_NAVS, ...HUONG_DAN_NAVS].find(tab => tab.key === newVal.name)
            breadcrumbs.value = [
                ...defaultItem,
                {
                    url: foundTab?.url || '/gioi-thieu',
                    name: foundTab?.i18n_key || 'pages.help.about_us',
                },
            ]
            return
        }

        const breadcrumbMapping = {
            'cong-game-type': {
                url: `/cong-game/${newVal.params?.type || 'all'}`,
                name: !newVal.params?.type
                    ? 'header.nav_menu.games'
                    : `pages.cong-game.${newVal.params?.type}.breadcrumb`,
            },
            'casino-type': {
                url: `/casino/${newVal.params?.type || 'all'}`,
                name: !newVal.params?.type
                    ? 'header.nav_menu.live_casino'
                    : `pages.casino.${newVal.params?.type}.breadcrumb`,
            },
            'tro-giup': {
                url: `/tro-giup`,
                name: 'breadcrumb.support',
            },
            link: {
                url: '/link',
                name: 'breadcrumb.link',
            },
            'bong-da-tab': {
                url: `/bong-da/${route.params?.tab}`,
                name: tabToTitleMap[route.params?.tab] ?? 'pages.football-schedules',
            },
            'im-play': {
                url: '/im-play',
                name: 'pages.im-play.breadcrumb',
            },
            'im-sports': {
                url: '/im-sports',
                name: 'pages.im-sports.breadcrumb',
            },
            'virtual-k-sports': {
                url: '/virtual-k-sports',
                name: 'pages.virtual-k-sports.breadcrumb',
            },
            'virtual-pp-sports': {
                url: '/virtual-pp-sports',
                name: 'pages.virtual-pp-sports.breadcrumb',
            },
            'virtual-saba-sports': {
                url: '/virtual-saba-sports',
                name: 'pages.saba-sports.breadcrumb',
            },
            'virtual-sports': {
                url: '/virtual-sports',
                name: 'header.nav_menu.virtual-sports',
            },
            asports: {
                url: '/asports',
                name: 'pages.asports.breadcrumb',
            },
            'ca-cuoc-the-thao': {
                url: `/ca-cuoc-the-thao`,
                name: 'header.nav_menu.sport',
            },
            cockfight: {
                url: '/cockfight',
                name: 'header.nav_menu.cockfight',
            },
            ksports: {
                url: '/ksports',
                name: 'pages.ksports.breadcrumb',
            },
            'lo-de': {
                url: '/lo-de',
                name: 'header.nav_menu.lode',
            },
            'lo-de-type': {
                url: newVal.params?.type ? `/lo-de/${newVal.params?.type}` : '/lo-de',
                name: newVal.params?.type
                    ? `pages.lode.${newVal.params?.type}.breadcrumb`
                    : 'header.nav_menu.lode',
            },
            'lo-de-lode3mien': {
                url: '/lo-de/lode3mien',
                name: 'pages.lode3mien.breadcrumb',
            },
            'lo-de-lode-sieutoc': {
                url: '/lo-de/lode-sieutoc',
                name: 'pages.lode-sieutoc.breadcrumb',
            },
            'lo-de-lodemd5': {
                url: '/lo-de/lodemd5',
                name: 'pages.lodemd5.breadcrumb',
            },
            'quay-so': {
                url: '/quay-so',
                name: 'breadcrumb.quayso',
            },
            'saba-sports': {
                url: '/saba-sports',
                name: 'pages.saba-sports.breadcrumb',
            },
            ssports: {
                url: '/ssports',
                name: 'pages.ssports.breadcrumb',
            },
            'tin-tuc-cat': {
                url: newVal.params?.cat ? `/tin-tuc/${newVal.params?.cat}` : '/tin-tuc',
                name: newVal.params?.cat ? `breadcrumb.${newVal.params?.cat}` : 'news.news',
            },
        }

        if (breadcrumbMapping[newVal.name]) {
            breadcrumbs.value = [...defaultItem, breadcrumbMapping[newVal.name]]
            return
        }
    },
    { deep: true, immediate: true }
)
</script>
<style lang="scss" scoped>
.breadcrumb {
    @apply flex list-none max-lg:pl-3 max-lg:pt-3 lg:pt-5;
}

.breadcrumb li {
    @apply relative flex items-center text-xs font-semibold text-white;
    position: relative;
    &:last-child {
        &::after {
            @apply absolute left-0 top-0 h-full w-full cursor-default content-[''];
        }
        a {
            @apply text-white;
        }
    }
}

.breadcrumb li:not(:last-child)::after {
    @apply mx-2 text-white content-['/'];
}

.breadcrumb a {
    @apply font-normal text-slate-300 no-underline;
}

.breadcrumb a:hover {
    @apply underline;
}
</style>
