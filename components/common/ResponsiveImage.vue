<template>
    <picture :class="pictureClass">
        <source
            v-if="srcLarge1x"
            media="(min-width: 1200px)"
            :srcset="`${srcLarge1x} 1x${srcLarge2x ? `, ${srcLarge2x} 2x` : ''}`"
        />
        <source
            v-if="srcSmall1x"
            media="(max-width: 1199px)"
            :srcset="`${srcSmall1x} 1x${srcSmall2x ? `, ${srcSmall2x} 2x` : ''}`"
        />
        <img :src="src" :alt="alt" :loading="lazy ? 'lazy' : 'eager'" />
    </picture>
</template>

<script setup>
defineProps({
    src: {
        type: String,
        required: true,
    },
    srcLarge1x: {
        type: String,
        default: '',
    },
    srcLarge2x: {
        type: String,
        default: '',
    },
    srcSmall1x: {
        type: String,
        default: '',
    },
    srcSmall2x: {
        type: String,
        default: '',
    },
    alt: {
        type: String,
        default: '',
    },
    lazy: {
        type: Boolean,
        default: true,
    },
    pictureClass: {
        type: [String, Array, Object],
        default: '',
    },
})
</script>

<style scoped>
img {
    display: block;
    width: 100%;
    height: auto;
}
</style>
