<script setup lang="ts">
import { IMAGE_LINK_DEFAULT } from '~/constants/index'
import VLazyImage from 'v-lazy-image'
import { addVersionToImageUrl } from '~/utils/image'

const props = defineProps({
    src: {
        type: String,
        require: true,
        default: '',
    },
    srcMb: {
        type: String,
        require: true,
        default: '',
    },
    imageDefault: {
        type: String,
        default: '',
    },
    alt: {
        type: String,
        default: '',
    },
    lazyload: {
        type: Boolean,
        default: false,
    },
    lazyLoadingPicture: {
        type: String as PropType<'lazy' | 'eager' | undefined>,
        default: undefined,
    },
    classWrapper: {
        type: String,
        default: '',
    },
    class: {
        type: String,
        default: '',
    },
    max: {
        type: String,
        default: '(max-width: 1199px)',
    },
    sizes: {
        type: String,
        default: '',
    },
    srcset: {
        type: String,
        default: '',
    },
    version: {
        type: String,
        default: '0.0.0',
    },
    fetchpriority: {
        type: String,
        default: '',
    },
})
const imageSrc = ref(props.src)
const emit = defineEmits(['load'])
const imageSrcDefault = computed(() => props.imageDefault || IMAGE_LINK_DEFAULT)
watchEffect(() => {
    imageSrc.value = addVersionToImageUrl(imageSrc.value, props.version)
})

const checkImageError = () => {
    imageSrc.value = imageSrcDefault.value
}

const convertFileNameExtension = (filePath = '', extension = ''): string => {
    const pos = filePath.lastIndexOf('.')
    return `${filePath.slice(0, pos < 0 ? filePath.length : pos)}${extension}`
}

const srcMb = computed(() => addVersionToImageUrl(props.srcMb, props.version))

const webpSrc = computed(() =>
    addVersionToImageUrl(convertFileNameExtension(props.src, '.webp'), props.version)
)
const avifSrc = computed(() =>
    addVersionToImageUrl(convertFileNameExtension(props.src, '.avif'), props.version)
)

const webpSrcMobile = computed(() =>
    addVersionToImageUrl(convertFileNameExtension(props.srcMb, '.webp'), props.version)
)
// const avifSrcMobile = computed(() =>
//     addVersionToImageUrl(convertFileNameExtension(props.srcMb, '.avif'), props.version)
// )

const className = computed(() => props.class)
const imgWidth = ref(0)
const baseImageRef = ref(null)

const handleImageLoaded = () => {
    emit('load')
}

const checkImageComplete = (element: HTMLImageElement) => {
    if (element.complete) {
        handleImageLoaded()
        return true
    }
    return false
}

onMounted(() => {
    if (baseImageRef.value) {
        imgWidth.value = baseImageRef.value?.clientWidth

        const imgElements = baseImageRef.value.getElementsByTagName('img')
        for (const img of imgElements) {
            if (checkImageComplete(img)) {
                continue
            }

            img.addEventListener('load', handleImageLoaded)
            img.addEventListener('error', checkImageError)
        }
    }
})

onBeforeUnmount(() => {
    if (baseImageRef.value) {
        const imgElements = baseImageRef.value.getElementsByTagName('img')
        for (const img of imgElements) {
            img.removeEventListener('load', handleImageLoaded)
            img.removeEventListener('error', checkImageError)
        }
    }
})
</script>
<template>
    <div class="base-image common-image" ref="baseImageRef" :class="classWrapper">
        <picture v-if="!lazyload" v-bind="props">
            <template v-if="srcMb && !src.includes('_logo')">
                <!-- <source
                    :srcset="avifSrcMobile"
                    type="image/avif"
                    :media="max"
                /> -->
                <source :srcset="webpSrcMobile" type="image/webp" :media="max" />
                <img
                    :src="imageSrc"
                    :alt="alt"
                    :class="className"
                    :sizes="sizes"
                    :srcset="srcset"
                    :loading="lazyLoadingPicture"
                    @load="handleImageLoaded"
                    :fetchpriority="fetchpriority"
                />
            </template>
            <template v-else-if="imageSrc.includes('_logo')">
                <source v-if="srcMb" :srcset="srcMb" :media="max" />
                <img
                    :src="imageSrc"
                    :alt="alt"
                    :class="className"
                    :sizes="sizes"
                    :srcset="srcset"
                    :loading="lazyLoadingPicture"
                    :fetchpriority="fetchpriority"
                />
            </template>
            <template v-else>
                <source :srcset="avifSrc" type="image/avif" />
                <source :srcset="webpSrc" type="image/webp" />
                <img
                    :src="imageSrc"
                    :alt="alt"
                    :class="className"
                    :sizes="sizes"
                    :srcset="srcset"
                    :loading="lazyLoadingPicture"
                    @load="handleImageLoaded"
                    :fetchpriority="fetchpriority"
                />
            </template>
        </picture>
        <v-lazy-image
            v-else
            v-bind="$attrs"
            :src="imageSrc"
            @error="checkImageError"
            :class="className"
            :src-placeholder="imageSrcDefault"
            :alt="alt"
            loading="lazy"
        />
    </div>
</template>

<style lang="scss" scoped src=""></style>
