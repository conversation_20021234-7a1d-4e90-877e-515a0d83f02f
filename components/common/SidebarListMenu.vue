<template>
    <div
        :class="[
            'sidebar-list-menu flex flex-col',
            {
                'single-item': items.length === 1,
            },
        ]"
    >
        <NuxtLink
            v-for="item in items"
            :key="item?.key"
            :to="localePath(item.link[0])"
            :class="[
                'menu-item',
                {
                    active: checkActive(item),
                },
            ]"
        >
            <component
                :is="item?.iconComponent"
                v-if="item?.iconComponent"
                :fill-color="checkActive(item) ? 'fill-green-400' : 'fill-slate-300'"
                class-wrapper="w-6 h-6"
            />
            <span>{{ $t(item.name) }}</span>
        </NuxtLink>
    </div>
</template>
<script setup>
const { items, keyToCompare, condition } = defineProps({
    items: {
        type: Array,
        required: true,
    },
    keyToCompare: {
        type: String,
        required: true,
    },
    condition: {
        type: String,
        required: true,
    },
})
const localePath = useLocalePath()

const checkActive = item => {
    if (typeof item[keyToCompare] === 'string') {
        return item[keyToCompare] === condition
    } else if (Array.isArray(item[keyToCompare])) {
        return item[keyToCompare].includes(condition)
    }
    return
}
</script>
<style lang="css" scoped>
.menu-item {
    @apply relative flex cursor-pointer items-center gap-2 whitespace-nowrap rounded-lg px-4 py-3 text-sm font-normal text-slate-300 transition-colors hover:bg-slate-700 hover:text-green-400;
}

.menu-item.active,
.menu-item.router-link-active {
    @apply bg-slate-700 !text-green-400;
}

.menu-item:hover:before,
.menu-item.active::before,
.menu-item.router-link-active::before {
    @apply absolute -left-5 top-0 h-full w-2 rounded-r-lg bg-slate-700 content-[''];
}

:deep() {
    .menu-item:hover {
        svg {
            path {
                @apply fill-green-400;
            }
        }
    }
}
</style>
