<template>
    <div class="grid w-full gap-4" :class="`grid-cols-${cols} xl:grid-cols-${lgcols}`">
        <div
            v-for="load in count"
            class="flex min-h-[135px] animate-pulse items-center justify-center rounded-lg bg-bigg-gray text-center font-medium leading-none text-sky-500 duration-100"
            :key="load"
        >
            <img
                :src="`${useRuntimeConfig().public.staticUrl}/assets/images/img/loader/spinner.svg`"
                alt="loading-spinner"
                class="h-[50px] w-[50px]"
                srcset=""
            />
        </div>
    </div>
</template>
<script setup>
defineProps({
    cols: {
        type: Number,
        default: 2,
    },
    lgcols: {
        type: Number,
        default: 4,
    },
    count: {
        type: Number,
        default: 4,
    },
})
</script>
