<template>
    <div
        class="dropdown relative h-full"
        ref="dropdownRef"
        v-bind="$attrs"
        :class="{
            'dropdown-checked': options?.length === 1 && !alwaysOpen,
        }"
    >
        <div
            role="button"
            @click="showDropdown = !showDropdown && (options?.length > 1 || alwaysOpen)"
            :class="[
                'dropdown__button flex h-full min-h-9 w-full cursor-pointer items-center justify-between rounded-full border border-gray-400 pl-3',
                wrapperClassName,
                { buttonClass: true, 'small-icon': props.isDisplayIcon },
            ]"
        >
            <div
                class="dropdown__button--label flex items-center gap-2 text-xs font-normal capitalize text-[#494C55]"
                :class="className"
            >
                <template
                    v-if="
                        (selectedValue && typeof selectedValue === 'string') ||
                        (selectedValue &&
                            typeof selectedValue === 'object' &&
                            (selectedValue?.label || selectedValue?.icon))
                    "
                >
                    <template v-if="isWithdrawBank">
                        <img
                            v-if="selectedValue.icon"
                            class="select-img"
                            :src="selectedValue.icon"
                            alt="icon-dropdown"
                        />
                    </template>
                    <template v-else>
                        <i
                            v-if="selectedValue.icon"
                            :class="
                                'select-icon ' +
                                selectedIconClass +
                                ' icon-' +
                                (selectedValue.value ? selectedValue.value?.toLowerCase() : 'all')
                            "
                        ></i>
                    </template>
                    <div v-if="!isDisplayIcon">
                        {{ capitalize(selectedValue?.label || selectedValue) }}
                    </div>
                    <div v-if="isDisplayIcon" class="flex items-center gap-1">
                        <!-- <img
                            v-if="selectedValue.icon"
                            class=""
                            :src="selectedValue.icon"
                            alt="icon-dropdown"
                        /> -->
                        <i
                            class="select-icon-display"
                            :class="
                                'icon-' +
                                (selectedValue.value
                                    ? selectedValue.value.toLowerCase()
                                    : 'all-type')
                            "
                        />
                        <p class="line-clamp-1 max-w-[60px] text-nowrap">
                            {{ capitalize(selectedValue?.label || selectedValue) }}
                        </p>
                    </div>
                </template>
                <template v-else-if="placeholder?.title">
                    <img v-if="placeholder?.icon" :src="placeholder.icon" alt="icon" />
                    <div>
                        {{ capitalize(placeholder.title) }}
                    </div>
                </template>
            </div>
            <div
                class="dropdown__button--arrow pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"
            >
                <i
                    class="icon-angle-down size-5 text-[18px] font-normal text-[#B1B9CB] transition-all"
                    :class="{
                        'rotate-180 transform': showDropdown,
                    }"
                ></i>
            </div>
        </div>
        <div
            v-if="showDropdown && (options?.length > 1 || alwaysOpen)"
            class="scrollbar absolute z-[9999] mt-1 max-h-[300px] w-fit min-w-full overflow-y-auto rounded-lg border border-slate-900 bg-slate-900 shadow-lg transition-all"
        >
            <ul class="py-2">
                <li v-if="isHasSearch" class="px-2">
                    <div class="relative">
                        <input
                            type="text"
                            id="games_keyword"
                            v-model="keyword"
                            class="input-text flex h-[36px] w-full items-center rounded-lg border border-[#DFE3EC] bg-white p-2.5 pl-9 text-sm text-[#717589] focus:border-rose-600"
                            :placeholder="$t('common.search')"
                            maxlength="20"
                            @keyup="$event => onSearch($event)"
                        />
                        <button
                            class="absolute left-0 top-1/2 h-full -translate-y-1/2 p-2.5 text-sm font-medium text-slate-400 focus:outline-none"
                        >
                            <img
                                :src="'/assets/images/icons/games/ic-search.svg'"
                                alt="search"
                                class="h-5 w-5"
                            />
                        </button>
                    </div>
                </li>
                <li
                    v-for="item in options"
                    :key="item.value"
                    class="dropdown-item flex cursor-pointer flex-col gap-2 px-3 py-[10px] text-xs font-normal text-slate-300 hover:bg-[#102542] hover:text-white"
                    @click="onChange(item)"
                    :class="{
                        active: selectedValue?.value === item.value || selectedValue === item,
                    }"
                >
                    <div class="flex items-center">
                        <template v-if="isWithdrawBank">
                            <img
                                v-if="item.icon"
                                class="select-img"
                                :src="item.icon"
                                alt="icon-dropdown"
                            />
                        </template>
                        <template v-else>
                            <i
                                v-if="item.icon"
                                class="select-icon pr-2"
                                :class="
                                    'icon-' + (item.value ? item.value.toLowerCase() : 'all-type')
                                "
                            ></i>
                        </template>
                        <div
                            class="dropdown-item__label text-nowrap-custom text-nowrap font-normal capitalize"
                            :class="dropdownItemClass"
                        >
                            {{ item.label || item }}
                        </div>
                        <div v-if="isShowInfo" class="absolute right-2">
                            <span
                                :class="
                                    bankStatuses[item.bank_status - 1]?.className ||
                                    'text-[#DF7B04]'
                                "
                            >
                                {{
                                    bankStatuses[item.bank_status - 1]?.text
                                        ? $t(bankStatuses[item.bank_status - 1]?.text)
                                        : $t(`user.bank.verifying`)
                                }}
                            </span>
                        </div>
                    </div>
                    <div v-if="isShowInfo">
                        <span>{{ item?.bank_account_no }}</span>
                        <span class="px-2">|</span>
                        <span>{{ item?.bank_account_name }}</span>
                    </div>
                </li>
            </ul>
            <slot></slot>
        </div>
    </div>
</template>
<script setup>
import { onClickOutside } from '@vueuse/core'
import capitalize from '~/utils/string/capitalize.ts'

const props = defineProps({
    data: {
        type: Array,
        default: () => [],
    },
    alwaysOpen: {
        type: Boolean,
        default: false,
    },
    selected: {
        type: [String, Number, Object],
        default: null,
    },
    placeholder: {
        type: Object,
        default: () => ({
            title: '',
            icon: '',
        }),
    },
    className: {
        type: String,
        default: '',
    },
    buttonClass: {
        type: String,
        default: '',
    },
    dropdownItemClass: {
        type: String,
        default: '',
    },
    selectedIconClass: {
        type: String,
        default: '',
    },
    isWithdrawBank: {
        type: Boolean,
        default: false,
    },
    isDisplayIcon: {
        type: Boolean,
        default: false,
    },
    isShowInfo: {
        type: Boolean,
        default: false,
    },
    isHasSearch: {
        type: Boolean,
        default: false,
    },
    wrapperClassName: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['change'])
const showDropdown = ref(false)
const dropdownRef = ref(null)
const selectedValue = ref(props.selected || null)
const isWithdrawBank = ref(props.isWithdrawBank || false)
const isShowInfo = ref(props.isShowInfo || false)
const isHasSearch = ref(props.isHasSearch || false)
const options = ref(props.data || [])

watch(
    () => props.data,
    newData => {
        options.value = newData
    },
    { deep: true }
)

const bankStatuses = [
    {
        text: 'user.bank.verifying',
        className: 'text-[#DF7B04]',
    },
    {
        text: 'user.bank.verified',
        className: 'text-[#10895E]',
    },
    {
        text: 'user.bank.rejected',
        className: 'text-[#F23B2F]',
    },
]
watch(
    () => props.selected,
    value => {
        selectedValue.value = value
    }
)

const onChange = item => {
    emit('change', item)
    showDropdown.value = false
    selectedValue.value = item
}
onClickOutside(dropdownRef, () => {
    showDropdown.value = false
})

const onSearch = event => {
    const searchValue = event.target.value
    options.value = props.data.filter(
        item =>
            item.label.toLowerCase().includes(searchValue.toLowerCase()) ||
            item.value.toLowerCase().includes(searchValue.toLowerCase()) ||
            !searchValue.trim()
    )
}
</script>

<style lang="scss" scoped>
.select-icon {
    @apply text-[20px];
    background: linear-gradient(180deg, #d8deea 0%, #a1a9bb 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
}
.select-icon-display {
    @apply text-[20px];
    background: linear-gradient(180deg, #d8deea 0%, #a1a9bb 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.select-img {
    object-fit: contain;
    margin-right: 8px;
    max-width: 24px;
    border-radius: 50%;
}

.dropdown-item {
    &.active {
        .select-icon {
            -webkit-text-fill-color: #00d486;
        }
        .dropdown-item__label {
            @apply font-medium text-green-400;
        }
    }
    &:not(:last-child) {
        @apply border-b border-solid border-[#162943];
    }
}

.scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.scrollbar::-webkit-scrollbar-track {
    box-shadow: 0 0 1px 1px #f1f3f9;
    border-radius: 20px;
}

.scrollbar::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #f1f3f9;
    -webkit-box-shadow: inset 0 0 1px #f1f3f9;
}
@media (max-width: 389px) {
    .dropdown-item {
        @apply pl-2 pr-1;
    }
}
.dropdown-checked {
    @apply pointer-events-none;
    .icon-angle-down {
        @apply bg-[url('/assets/images/img/icons/check.svg')] bg-contain bg-center bg-no-repeat before:hidden;
    }
}
.small-icon {
    @apply pl-1;
}
.text-nowrap-custom {
    white-space: nowrap;
}
.dropdown__button--arrow {
    @media (max-width: 370px) {
        padding-right: 5px;
    }
}
.dropdown--filter {
    :deep(.icon-all-type) {
        &::before {
            content: '\e951';
        }
    }
}
</style>
