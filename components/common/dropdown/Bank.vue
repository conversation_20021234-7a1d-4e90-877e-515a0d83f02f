<template>
    <div
        class="dropdown relative h-full"
        ref="dropdownRef"
        v-bind="$attrs"
        :class="{
            'dropdown-checked': banksToAddNew?.length === 1 && !alwaysOpen,
        }"
    >
        <div
            role="button"
            @click="showDropdown = !showDropdown && (banksToAddNew?.length > 1 || alwaysOpen)"
            class="dropdown__button flex h-full min-h-9 w-full cursor-pointer items-center justify-between rounded-xl border border-slate-700 bg-slate-950 pl-3"
        >
            <div
                class="dropdown__button--label flex h-[50px] items-center gap-2 text-sm font-normal capitalize text-white"
            >
                <template v-if="selectedValue?.bank_code">
                    <img
                        class="select-img"
                        :src="`/assets/images/v2/banks/icon-logo/${bankCodeToName(
                            selectedValue?.bank_code
                        )}.webp?v=1`"
                        alt="icon-dropdown"
                    />
                    {{
                        bankCodeMapping[selectedValue.bank_code?.toLowerCase()] ||
                        selectedValue.bank_code
                    }}
                </template>
                <template v-else-if="placeholder?.title">
                    <img v-if="placeholder?.icon" :src="placeholder.icon" alt="icon" />
                    <div>
                        {{ placeholder.title }}
                    </div>
                </template>
            </div>

            <div
                class="dropdown__button--arrow pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3"
            >
                <IconsChevronDown
                    :classWrapper="`transition-all transform ${showDropdown ? 'rotate-180' : ''}`"
                />
            </div>
        </div>
        <div
            v-if="showDropdown && (banksToAddNew?.length > 1 || alwaysOpen)"
            class="scrollbar absolute z-20 mt-1 max-h-[300px] w-fit min-w-full overflow-y-auto rounded-lg border border-slate-800 bg-slate-800 px-3 py-2.5 shadow-lg transition-all"
        >
            <div class="search-box relative mb-3">
                <input
                    type="text"
                    id="games_keyword"
                    class="input-text flex h-[36px] w-full items-center rounded-lg border border-slate-900 bg-slate-900 p-2.5 pl-9 text-sm text-white focus:border-slate-900 focus:ring-0"
                    :placeholder="$t('common.search')"
                    maxlength="20"
                    :value="keyword"
                    @keyup="$event => onSearch($event)"
                />
                <button
                    class="absolute left-0 top-1/2 h-full -translate-y-1/2 p-2.5 focus:outline-none"
                >
                    <IconsSearch classWrapper="h-5 w-5" />
                </button>
            </div>

            <UserBanks
                v-if="isWithdrawBank"
                class="mb-3"
                is-support-select-bank
                :userBanks="userBankAccounts"
                :selected="selectedValue"
                @change="onChange"
            />

            <label v-if="isWithdrawBank" class="text-left text-sm font-medium text-white">
                {{ $t('user.bank.bank_other') }}
            </label>

            <div class="banks-to-add-new grid w-full grid-cols-2 py-2">
                <div
                    v-for="item in banksToAddNew"
                    :key="item.value"
                    class="dropdown-item"
                    @click="onChange(item)"
                    :class="{
                        active: selectedValue?.value === item.value || selectedValue === item,
                    }"
                >
                    <img v-if="item.icon" class="select-img" :src="item.icon" alt="icon-dropdown" />
                    <div class="dropdown-item__label text-nowrap font-normal">
                        {{ item.label || item }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useBankStore, useWithdrawStore } from '~/stores'
import { onClickOutside } from '@vueuse/core'
import { bankCodeMapping } from '~/constants/withdraw.ts'

defineProps({
    data: {
        type: Array,
        default: () => [],
    },
    alwaysOpen: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: Object,
        default: () => ({
            title: '',
            icon: '',
        }),
    },
    isWithdrawBank: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['change'])
const showDropdown = ref(false)
const dropdownRef = ref(null)
const selectedValue = ref({})
const keyword = ref('')

const useBankStoreInstance = useBankStore()
const { userBankAccounts } = storeToRefs(useBankStoreInstance)

const useWithdrawStoreInstnce = useWithdrawStore()
const { withdrawBanksList } = storeToRefs(useWithdrawStoreInstnce)

const differenceBy = (array, values, iteratee) => {
    const excludeSet = new Set(values.map(iteratee))
    return array.filter(item => !excludeSet.has(iteratee(item)))
}

const withdrawBanksListComputed = computed(() => {
    return withdrawBanksList.value.map(item => ({
        ...item,
        value: item.bank_code,
        label: bankCodeMapping[item.bank_code?.toLowerCase()] || item.bank_code,
        icon: `/assets/images/v2/banks/icon-logo/${bankCodeToName(item.bank_code)}.webp?v=1`,
    }))
})

const banksToAddNew = computed(() => {
    const searchValue = keyword.value
    const banksValid = differenceBy(withdrawBanksListComputed.value, userBankAccounts.value, bank =>
        bank.bank_code.toLowerCase()
    )

    return banksValid.filter(
        item =>
            item.label.toLowerCase().includes(searchValue.toLowerCase()) ||
            item.value.toLowerCase().includes(searchValue.toLowerCase()) ||
            !searchValue.trim()
    )
})

const onSearch = event => {
    keyword.value = event.target.value
}

watch(
    () => userBankAccounts.value,
    newData => {
        if (newData.length) {
            selectedValue.value = newData[0]
            emit('change', newData[0])
        }
    },
    { deep: true }
)

const onChange = item => {
    emit('change', item)
    showDropdown.value = false
    selectedValue.value = item
}

onClickOutside(dropdownRef, () => {
    showDropdown.value = false
})
</script>

<style lang="scss" scoped>
.select-icon {
    @apply text-[20px];
    background: linear-gradient(180deg, #d8deea 0%, #a1a9bb 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
}
.select-icon-display {
    @apply text-[20px];
    background: linear-gradient(180deg, #d8deea 0%, #a1a9bb 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.select-img {
    object-fit: contain;
    max-width: 24px;
    border-radius: 50%;
}

.dropdown-item {
    @apply flex cursor-pointer items-center gap-2 rounded-lg p-3 text-xs font-normal text-white hover:bg-slate-700;
    &.active {
        @apply bg-slate-900 text-green-400;
    }
}

.scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.scrollbar::-webkit-scrollbar-track {
    box-shadow: 0 0 1px 1px #f1f3f9;
    border-radius: 20px;
}

.scrollbar::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #f1f3f9;
    -webkit-box-shadow: inset 0 0 1px #f1f3f9;
}
@media (max-width: 389px) {
    .dropdown-item {
        @apply pl-2 pr-1;
    }
}
.dropdown-checked {
    @apply pointer-events-none;
    .icon-angle-down {
        @apply bg-[url('/assets/images/img/icons/check.svg')] bg-contain bg-center bg-no-repeat before:hidden;
    }
}
.small-icon {
    @apply pl-1;
}
.text-nowrap-custom {
    white-space: nowrap;
}
.dropdown__button--arrow {
    @media (max-width: 370px) {
        padding-right: 5px;
    }
}
.dropdown--filter {
    :deep(.icon-all-type) {
        &::before {
            content: '\e951';
        }
    }
}

#games_keyword::placeholder {
    color: #757575;
}
</style>
