<template>
    <div class="flex items-center justify-center">
        <input
            type="checkbox"
            :name="name"
            :id="`${name}_id`"
            :value="inputValue"
            v-model="inputValue"
            :placeholder="placeholder"
            @input="handleChange"
            @blur="handleBlur"
            :disabled="disabled"
            :readonly="readonly"
            class="h-5 w-5 rounded border border-slate-750 bg-slate-950"
        />
        <label :for="name + '_id'" class="ml-2 text-sm font-normal text-slate-300">
            {{ label }}</label
        >
    </div>
</template>
<script setup>
import { toRef } from 'vue'
import { useField } from 'vee-validate'

const props = defineProps({
    value: {
        type: Boolean,
        default: true,
    },
    name: {
        type: String,
        required: true,
    },
    label: {
        type: String,
        required: true,
    },
    placeholder: {
        type: String,
        default: '',
    },
    classes: {
        type: String,
        default: '',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    readonly: {
        type: Boolean,
        default: false,
    },
})
const name = toRef(props, 'name')
const theValue = toRef(props, 'value')

const {
    value: inputValue,
    handleBlur,
    handleChange,
} = useField(name, undefined, {
    initialValue: props.value,
})

watch(
    () => theValue.value,
    // eslint-disable-next-line
    (newVal, _) => {
        if (newVal) {
            inputValue.value = newVal
        }
    }
)
</script>
<style lang="scss" scoped>
[type='checkbox'] {
    outline: none;
    box-shadow: none;
    &:checked {
        @apply bg-slate-700;
    }
    &:focus ~ label {
        @apply text-slate-300;
    }
}
</style>
