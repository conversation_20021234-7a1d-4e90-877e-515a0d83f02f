<template>
    <button
        :type="type"
        class="btn btn-primary relative w-full max-w-fit"
        :class="classes"
        :disabled="isDisabled || loading"
    >
        <slot />
        <div
            v-if="loading"
            class="absolute inset-0 z-10 flex w-full items-center justify-center rounded-lg bg-gray-500 bg-opacity-50 transition-opacity"
        >
            <img
                :src="`${useRuntimeConfig().public.staticUrl}/assets/images/img/loader/spinner.svg`"
                alt="loading-spinner"
                class="size-1/2"
            />
            <span class="sr-only">Loading...</span>
        </div>
    </button>
</template>
<script setup>
const { type, loading, classes } = defineProps({
    type: {
        type: String,
        default: 'submit',
    },
    loading: {
        type: Boolean,
        default: false,
    },
    isDisabled: {
        type: Boolean,
        default: false,
    },
    classes: {
        type: String,
        default: '',
    },
})
</script>
