<template>
    <NuxtLink :to="localePath(link)" class="flex items-center gap-1.5">
        <span class="text-sm font-medium text-white">{{ $t(title) }}</span>
        <IconsChevronRight fillColor="fill-white" classWrapper="h-5 w-5" />
    </NuxtLink>
</template>
<script setup>
defineProps({
    link: {
        type: String,
        default: '',
    },
    title: {
        type: String,
        default: 'common.show_more',
    },
})

const localePath = useLocalePath()
</script>
