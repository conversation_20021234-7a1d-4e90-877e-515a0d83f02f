<template>
    <div>
        <div class="mb-6 max-xl:rounded-2xl max-xl:bg-slate-900 max-xl:p-3">
            <Veeform
                ref="withdrawForm"
                v-slot="{ meta }"
                class="flex flex-col gap-7"
                @submit="handelWithdrawBank"
                :validation-schema="withdrawBankSchema(balance, unverifiedBank, bankAccountNoToSet)"
                :initialValues="initialValues"
            >
                <CommonBoxHeader title="user.bank.title">
                    <template #icon>
                        <IconsAccountBank class-wrapper="w-5" />
                    </template>
                </CommonBoxHeader>
                <!-- <p>{{ meta }}</p>
                <p>{{ values }}</p>
                <p>{{ errors }}</p> -->
                <div class="relative">
                    <label
                        for="bank_code"
                        class="absolute left-3 top-0 z-1 -translate-y-1/2 rounded bg-slate-900 px-1 text-left text-sm font-medium text-white"
                    >
                        {{ $t('user.withdraw.bank.bank') }}
                    </label>
                    <CommonDropdownBank
                        @change="onChangeBankFormWithdraw"
                        :data="updatedBankList"
                        isWithdrawBank
                        :placeholder="{
                            title: $t('user.withdraw.bank.select_bank_placeholder'),
                            icon: '/assets/images/icons/withdraw/ic-bank.svg',
                        }"
                        :alwaysOpen="true"
                    />
                </div>

                <div class="flex items-center justify-between gap-5 max-xl:flex-col">
                    <div class="w-full flex-1">
                        <CommonTextInput
                            name="account_holder"
                            :placeholder="$t('user.withdraw.bank.enter_account_holder')"
                            :label="$t('user.withdraw.bank.account_holder')"
                            classes="text-sm font-medium h-14 uppercase placeholder:normal-case placeholder:capitalize"
                            labelClass="bg-slate-900"
                            :value="bankAccountNameToSet ?? ''"
                            :disabled="bankAccountNameToSet ? true : false"
                            :id="!userBankAccounts?.length ? 'accountNameInput' : ''"
                            :maxlength="MAX_LENGTH.BANK_ACCOUNT_NAME"
                            :regex="/^[a-zA-Z\s]+$/"
                        />
                    </div>
                    <div class="w-full flex-1">
                        <CommonTextInput
                            name="account_number"
                            :placeholder="$t('user.withdraw.bank.enter_account_number')"
                            :label="$t('user.withdraw.bank.account_number')"
                            classes="text-sm font-medium h-14"
                            labelClass="bg-slate-900"
                            :value="bankAccountNoToSet ?? ''"
                            :disabled="bankAccountNoToSet ? true : false"
                            :id="!bankAccountNoToSet ? 'accountNumberInput' : ''"
                            :maxlength="MAX_LENGTH.BANK_ACCOUNT_NO"
                            :regex="/\d+/g"
                        />
                    </div>
                </div>

                <CommonTextInput
                    v-if="unverifiedBank"
                    type="tel"
                    name="phone"
                    :label="$t('user.withdraw.bank.phone_label')"
                    :placeholder="$t('user.withdraw.bank.phone_placeholder')"
                    classes="text-sm font-medium h-[52px] crypto-input"
                    labelClass="bg-slate-900"
                    maxlength="5"
                    :regex="/\d+/g"
                />

                <CommonTextInput
                    name="amount_withdraw"
                    type="tel"
                    :label="$t('user.withdraw.bank.enter_amount_withdraw')"
                    classes="text-sm font-medium h-14"
                    labelClass="bg-slate-900"
                    money
                    maxlength="8"
                    :isFormatNumber="true"
                    :regex="/\d+/g"
                />

                <SpecificAccountDepositCodepayAmounts
                    :amount-list="amountWithdrawSuggests"
                    :selected-amount="selectedAmount"
                    :handle-number-input="handleNumberInput"
                />

                <div class="flex justify-center">
                    <CommonButton
                        :loading="isSubmitting"
                        :isDisabled="isSubmitting || balance < 1 || !meta.dirty || !meta.valid"
                        classes="btn-submit min-w-[210px] max-lg:!max-w-none"
                    >
                        {{ $t('user.withdraw.bank.confirm_withdrawal') }}
                    </CommonButton>
                </div>
            </Veeform>
        </div>
        <SpecificAccountWithdrawDepositGuideline />
    </div>
</template>
<script setup>
import { Form as Veeform } from 'vee-validate'
import { withdrawBankSchema } from '~/forms/withdrawBank.schema'
import { useWithdrawStore, useAlertStore, useUserStore, useBankStore } from '~/stores'
import { storeToRefs } from 'pinia'
import { MAX_LENGTH } from '~/constants/user'
import { amountWithdrawSuggests } from '~/constants/withdraw.ts'
import { useNotify } from '~/composables/use-notify'
import { useBankService } from '~/services'

const { showWithdrawSuccessModal, showWithdrawFailedModal } = useNotify()
const useAlertStoreInstance = useAlertStore()

const useUserStoreInstance = useUserStore()
const { balance } = storeToRefs(useUserStoreInstance)

const bankService = useBankService()

const isLoading = ref(false)
const isSubmitting = ref(false)
const initialValues = ref({})

const useWithdrawStoreInstance = useWithdrawStore()
const { createBankWithdraw } = useWithdrawStoreInstance

const useBankStoreInstance = useBankStore()
const { getUserBankAccounts } = useBankStoreInstance
const { userBankAccounts } = storeToRefs(useBankStoreInstance)

const withdrawForm = ref(null)
const selectedAmount = ref('')
const unverifiedBank = ref(false)
const bankAccountNoToSet = ref('')
const bankAccountNameToSet = ref('')

const onChangeBankFormWithdraw = value => {
    const bankCodeToSet = value?.bank_code ?? ''
    bankAccountNoToSet.value = value?.bank_account_no ?? ''
    unverifiedBank.value = value?.bank_status !== 2
    withdrawForm.value.setFieldValue('bank_code', bankCodeToSet)
    withdrawForm.value.setFieldValue('account_number', bankAccountNoToSet.value)
    if (bankAccountNoToSet.value) {
        bankAccountNameToSet.value = value?.bank_account_name ?? ''
        withdrawForm.value.setFieldValue('account_holder', bankAccountNameToSet.value)
    }
}

const handelWithdrawBank = async (payload, { resetForm }) => {
    const payloadDataWithdraw = {
        to_bank_code: payload.bank_code,
        amount_withdraw: Number(payload.amount_withdraw.replace(/\,/g, '')),
        amount_withdraw_mask: NumberUtils.formatNumberWithDots(
            payload.amount_withdraw.replace(/\,/g, '') * 1000
        ),
        to_bank_no: payload.account_number,
        to_bank_name: payload.account_holder,
    }

    const payloadDataCreateUserBank = {
        bank_code: payload.bank_code,
        bank_account_no: payload.account_number,
        bank_account_name: payload.account_holder,
    }

    if (unverifiedBank.value) {
        payloadDataWithdraw.phone = payload.phone
    }

    try {
        isSubmitting.value = true
        const respDataCreateUserBank = {}
        if (!bankAccountNoToSet.value) {
            const { data } = await bankService.createBank(payloadDataCreateUserBank)
            respDataCreateUserBank.value = data.value
        }

        if (respDataCreateUserBank.value?.status === 'OK' || bankAccountNoToSet.value) {
            const { data: respDataWithdraw } = await createBankWithdraw(payloadDataWithdraw)
            if (respDataWithdraw.value?.status === 'OK') {
                showWithdrawSuccessModal()
            } else if (respDataWithdraw.value.message) {
                showWithdrawFailedModal(respDataWithdraw.value.message)
            } else {
                throw respDataWithdraw
            }
        } else {
            throw respDataCreateUserBank
        }

        resetForm({
            values: {
                ...payload,
                amount_withdraw: '',
            },
        })
    } catch (error) {
        useAlertStoreInstance.showMessageError(error)
    } finally {
        isSubmitting.value = false
    }
}

const handleNumberInput = (value, index) => {
    selectedAmount.value = `${index}_${value}`
    withdrawForm.value.setFieldValue(
        'amount_withdraw',
        NumberUtils.formatNumberWithDots(value.toString()?.replace(/\,/g, ''))
    )
}

onMounted(async () => {
    await nextTick(async () => {
        try {
            isLoading.value = true
            await getUserBankAccounts()
        } catch (error) {
            useAlertStoreInstance.showMessageError(error)
        } finally {
            isLoading.value = false
        }
    })
})

watch(
    () => withdrawForm.value?.values?.amount_withdraw,
    value => {
        const amount = Number(value?.toString()?.replace(/\,/g, ''))
        const index = amountWithdrawSuggests.findIndex(item => item.value === amount)
        if (index !== -1) {
            selectedAmount.value = `${index}_${value}`
        } else {
            selectedAmount.value = ''
        }
    }
)
</script>
