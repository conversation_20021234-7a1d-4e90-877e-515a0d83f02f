<template>
    <div>
        <div v-if="isLoading && !showMobile" class="text-center">
            <CommonSpinner />
        </div>
        <div v-else>
            <Veeform
                ref="formRef"
                class="mb-6 flex flex-col max-xl:rounded-2xl max-xl:bg-slate-900 max-xl:p-3 max-lg:gap-5 lg:gap-6"
                v-slot="{ meta }"
                @submit="handelWithdrawPhonecard"
                :validation-schema="withdrawPhonecardSchema(t, user.balance)"
                :initialValues="initialValues"
            >
                <SpecificAccountDepositPhonecardSelectNetwork
                    :deposit-phonecard-list="withdrawPhonecardList"
                    :handle-phonecard-selected="handlePhonecardSelected"
                    :handle-reset-form="handleResetForm"
                />

                <div>
                    <div class="mb-2 text-sm font-medium text-white">
                        {{ $t('user.withdraw.phonecard.select_amount') }}
                    </div>
                    <SpecificAccountDepositCodepayAmounts
                        :amount-list="paymenAmount"
                        :selected-amount="selectedCardAmount"
                        :handle-number-input="handleSelectCardAmount"
                        name-input="card_amount_unit"
                    />

                    <ErrorMessage name="card_amount_unit" v-slot="{ message }">
                        <div v-if="message" class="w-fit py-2 text-xs font-semibold text-red-500">
                            {{ $t(message) }}
                        </div>
                    </ErrorMessage>
                </div>

                <CommonTextInput
                    name="phonecard_number"
                    type="tel"
                    :label="$t('user.withdraw.phonecard.phonecard_number')"
                    classes="text-sm font-medium h-14"
                    labelClass="bg-slate-900"
                    maxlength="2"
                    :regex="/\d+/g"
                    :maxAmount="10"
                    hasNumberControl
                />

                <div class="flex justify-center">
                    <CommonButton
                        :loading="isSubmitting"
                        :isDisabled="Number(user.balance) < 1 || !meta.valid"
                        classes="btn-submit min-w-[210px] max-lg:!max-w-none"
                    >
                        {{ $t('user.withdraw.phonecard.create_withdraw_ticket') }}
                    </CommonButton>
                </div>
            </Veeform>

            <SpecificAccountPhoneCardGuideline />
        </div>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { ErrorMessage, Form as Veeform } from 'vee-validate'
import { useNotify } from '~/composables/use-notify'
import { withdrawPhonecardSchema } from '~/forms/withdrawPhonecard.schema'
import { useAlertStore, useUserStore, useWithdrawStore } from '~/stores'

const { showWithdrawSuccessModal, showWithdrawFailedModal } = useNotify()
const useAlertStoreInstance = useAlertStore()
const useUserStoreInstance = useUserStore()
const { balance, user } = storeToRefs(useUserStoreInstance)
const router = useRouter()
const { showMobile } = useCommon()

const isLoading = ref(false)
const isSubmitting = ref(false)
const formRef = ref(null)
const initialValues = ref({
    phonecard_number: 1,
})

const paymenAmount = ref([])

const useWithdrawStoreInstance = useWithdrawStore()
const { getWithdrawPhonecardList, createPhonecardWithdraw } = useWithdrawStoreInstance
const { withdrawPhonecardList } = storeToRefs(useWithdrawStoreInstance)

const selectedPhonecard = ref({})
const selectedCardAmount = ref('')

const handleSelectCardAmount = value => {
    selectedCardAmount.value = value
}

const handlePhonecardSelected = payload => {
    selectedPhonecard.value = payload
    paymenAmount.value = withdrawPhonecardList.value[payload]?.value
}

const handleResetForm = () => {
    formRef.value.resetForm()
    selectedCardAmount.value = ''
    formRef.value?.setValues({
        phonecard_number: 1,
    })
}

const handelWithdrawPhonecard = async (payload, { resetForm }) => {
    const toSend = {
        to_telcom_code: payload.bank_code,
        card_amount_unit: payload.card_amount_unit,
        card_number: Number(payload.phonecard_number),
        card_status: 1,
    }
    try {
        isSubmitting.value = true
        const { data } = await createPhonecardWithdraw(toSend)
        if (data.value?.status === 'OK') {
            showWithdrawSuccessModal()
            // setTimeout(() => {
            //     router.push('/user/transaction-history')
            // }, 1000)
        } else if (data.value.message) {
            showWithdrawFailedModal(data.value.message)
        } else {
            throw data
        }
        resetForm()
        selectedCardAmount.value = ''
    } catch (error) {
        useAlertStoreInstance.showMessageError(error)
    } finally {
        isSubmitting.value = false
    }
}

onMounted(async () => {
    await nextTick(async () => {
        try {
            isLoading.value = true
            await getWithdrawPhonecardList()

            if (Object.keys(withdrawPhonecardList.value).length > 0) {
                Object.assign(initialValues.value, {
                    bank_code: Object.keys(withdrawPhonecardList.value)[0],
                })
                handlePhonecardSelected(Object.keys(withdrawPhonecardList.value)[0])
            }
        } catch (error) {
            useAlertStoreInstance.showMessageError(error)
        } finally {
            isLoading.value = false
        }
    })
})
</script>
<style lang="scss" scoped>
.amount__list {
    label {
        @apply border-[transparent] bg-[#F7F7F9];
    }
    input:checked + label {
        @apply bg-[#fff];
    }
}
</style>
