<template>
    <div class="crypto">
        <div v-if="isLoading && !showMobile" class="text-center">
            <CommonSpinner />
        </div>
        <Veeform
            ref="cryptoForm"
            v-else
            class="crypto__form relative flex flex-col gap-7 max-xl:rounded-2xl max-xl:bg-slate-900 max-xl:p-3 xl:pb-0"
            @submit="onCreateTransaction"
            :initialValues="initialValues"
            :validation-schema="formCryptoSchema($t, balance, (currencyActive?.min || 0) / 1000)"
        >
            <div class="crypto__form--currency">
                <div class="mb-2 text-base font-medium text-white">
                    {{ $t('user.withdraw.crypto.label.crypto_currency') }}
                </div>
                <div class="crypto__form--currency-list">
                    <ul class="grid w-full grid-cols-2 items-center justify-center gap-3">
                        <li v-for="item in currencyList" class="relative" :key="item.currency">
                            <Field
                                type="radio"
                                :id="item.currency"
                                name="currency"
                                :value="item.currency"
                                class="peer hidden"
                                @click="onChangeCurrency(item)"
                            />

                            <label
                                :for="item.currency"
                                class="currency-item flex h-[50px] cursor-pointer items-center justify-start gap-3 rounded-xl border border-slate-800 bg-slate-800 p-3 peer-checked:border-green-400 peer-checked:bg-slate-900 xl:h-16"
                            >
                                <img
                                    class="h-7 w-7 rounded-full xl:h-10 xl:w-10"
                                    :src="`/assets/images/img/crypto/${item.currency.toLowerCase()}.svg`"
                                    :alt="item.currency"
                                />
                                <div
                                    class="flex flex-col items-start gap-0.5 text-center text-xs font-normal xl:text-sm xl:leading-[18px]"
                                >
                                    <div class="text-sm font-medium text-white">
                                        {{ item.currency }} ({{ item.network.join(', ') }})
                                    </div>
                                    <div
                                        class="whitespace-nowrap text-xs leading-[18px] text-slate-300"
                                    >
                                        ≈
                                        {{ NumberUtils.formatNumberWithCommas(item.price) }}
                                        {{ $t('user.withdraw.crypto.label.local_currency') }}
                                    </div>
                                </div>
                            </label>
                        </li>
                    </ul>

                    <ErrorMessage name="currency" v-slot="{ message }">
                        <div v-if="message" class="mb-2 mt-2 w-fit rounded-lg text-xs text-red-500">
                            {{ $t(message) }}
                        </div>
                    </ErrorMessage>
                </div>
            </div>

            <div class="crypto__form--address">
                <CommonTextInput
                    name="address"
                    :label="$t('user.withdraw.crypto.label.address')"
                    classes="text-sm font-medium  h-[52px] crypto-input"
                    labelClass="bg-slate-900"
                    :placeholder="$t('user.withdraw.crypto.placeholder.address')"
                />
            </div>

            <div class="flex w-full gap-7 max-xl:flex-col">
                <div class="crypto__form--amount flex-1">
                    <CommonTextInput
                        name="amount"
                        type="tel"
                        :placeholder="$t('user.withdraw.crypto.placeholder.amount')"
                        :label="$t('user.withdraw.crypto.label.amount')"
                        classes="text-sm font-medium h-[52px]"
                        className="crypto-input"
                        labelClass="bg-slate-900"
                        money
                        :currency="currencyActive?.currency || ''"
                        :exchangingRate="currencyActive?.price || 0"
                        maxlength="11"
                        :isFormatNumber="true"
                        :regex="/\d+/g"
                    />
                </div>
                <div class="crypto__form--phone flex-1">
                    <CommonTextInput
                        type="tel"
                        name="phone"
                        :label="$t('user.withdraw.crypto.label.phone')"
                        :placeholder="$t('user.withdraw.crypto.placeholder.phone')"
                        classes="text-sm font-medium h-[52px] crypto-input"
                        labelClass="bg-slate-900"
                        maxlength="5"
                        :regex="/\d+/g"
                    />
                </div>
            </div>

            <div class="crypto__form--submit flex items-center justify-center">
                <CommonButton
                    :loading="isSubmitting"
                    :isDisabled="!cryptoForm?.meta?.valid || isSubmitting || balance < 1"
                    classes="btn-submit min-w-[210px] max-lg:!max-w-none"
                >
                    {{ $t('user.withdraw.crypto.label.submit') }}
                </CommonButton>
            </div>
        </Veeform>

        <div class="mt-6 flex flex-col gap-4 pb-4">
            <SpecificAccountDepositCryptoGuideline />
            <SpecificAccountDepositCryptoMarkets />
        </div>
    </div>
</template>
<script setup>
import { Field, Form as Veeform, ErrorMessage } from 'vee-validate'
import { NumberUtils } from '~/utils'
import { formCryptoSchema } from '~/forms/withdrawCrypto.schema.js'
import { useUserStore, useWithdrawStore, useAlertStore } from '~/stores'
import { storeToRefs } from 'pinia'
import { useNotify } from '~/composables/use-notify'

const { showWithdrawSuccessModal, showWithdrawFailedModal } = useNotify()
const useUserInstances = useUserStore()
const { balance } = storeToRefs(useUserInstances)
const router = useRouter()
const { showMobile } = useCommon()

const useWithdrawStoreInstances = useWithdrawStore()
const { currencyList } = storeToRefs(useWithdrawStoreInstances)
const { createCryptoWithdrawn } = useWithdrawStoreInstances

const useAlertStoreInstance = useAlertStore()

const cryptoForm = ref(null)
const isSubmitting = ref(false)

const initialValues = {
    currency: currencyList.value?.[0]?.currency || '',
    address: '',
    amount: 0,
    phone: '',
}

const currencyActive = ref(currencyList.value?.[0])

const onChangeCurrency = item => {
    currencyActive.value = item
}

const onCreateTransaction = async (payload, { resetForm }) => {
    isSubmitting.value = true
    try {
        const { data } = await createCryptoWithdrawn({
            ...payload,
            wallet_address: payload.address,
            network: currencyActive.value?.network.join(', '),
            ex_rate: currencyActive.value.price,
            amount_withdraw: (payload.amount?.toString()?.replace(/\,/g, '') || 0) * 1000,
        })
        if (data.value?.status === 'OK') {
            showWithdrawSuccessModal()
            resetForm()
            // setTimeout(() => {
            //     router.push('/user/transaction-history')
            // }, 1000)
        } else if (data.value?.message) {
            showWithdrawFailedModal(data.value.message)
        } else {
            throw data
        }
    } catch (error) {
        useAlertStoreInstance.showMessage({
            type: 'ERROR',
            message_key: error.value.data.message || error,
        })
    } finally {
        isSubmitting.value = false
    }
}
</script>
