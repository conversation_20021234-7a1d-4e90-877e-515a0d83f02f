<template>
    <div>
        <div class="relative">
            <div class="relative">
                <label
                    for="email_verify_id"
                    class="absolute left-3 top-0 z-1 -translate-y-1/2 rounded bg-slate-950 px-1 text-left text-sm font-medium text-white"
                >
                    {{ $t('modal.email.email') }}</label
                >
                <input
                    class="block w-full appearance-none rounded-lg border border-slate-700 bg-slate-950 px-2.5 pb-3.5 pl-4 pt-4 text-sm text-white placeholder:text-slate-300 focus:border-slate-700 focus:ring-0"
                    :class="{ 'bg-bigg-gray': user?.is_verify_email }"
                    autocomplete="off"
                    name="email_verify"
                    id="email_verify_id"
                    type="text"
                    :value="user?.is_verify_email ? user?.email : ''"
                    placeholder=""
                    disabled
                />

                <div class="absolute inset-y-0 right-0 flex items-center pr-1.5">
                    <ClientOnly>
                        <div v-if="user?.is_verify_email">
                            <IconsCheck />
                        </div>
                        <CommonButtonVerify v-else @click="openEmailVerifyModal">
                            {{ $t('modal.email.title') }}
                        </CommonButtonVerify>
                    </ClientOnly>
                </div>
            </div>
        </div>
        <!-- modal -->
        <Teleport to="body">
            <ModalVerifyEmail :show="showVerifyEmailModal" />
            <ModalVerifyEmailOtp :show="showVerifyEmailOtpModal" />
            <ModalEmailVerified :show="showEmailVerifiedModal" />
        </Teleport>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useModalStore, useUserStore } from '~/stores'
const useModalStoreInstance = useModalStore()
const useUserStoreInstance = useUserStore()
const { showVerifyEmailModal, showVerifyEmailOtpModal, showEmailVerifiedModal } =
    storeToRefs(useModalStoreInstance)
const { user } = storeToRefs(useUserStoreInstance)

const openEmailVerifyModal = () => {
    showVerifyEmailModal.value = true
}
onUnmounted(() => {
    showVerifyEmailModal.value = false
    showVerifyEmailOtpModal.value = false
    showEmailVerifiedModal.value = false
})
</script>
<style lang="scss" scoped>
input {
    position: relative;
    background-image: url('/assets/images/v2/icons/email.webp');
    background-size: 24px 24px;
    background-repeat: no-repeat;
    background-position: 12px 14px;
    padding-left: 56px;
}
</style>
