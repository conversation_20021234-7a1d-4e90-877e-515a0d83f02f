<template>
    <CommonErrorWrapper>
        <CommonBoxWrapper class="col-span-1">
            <CommonBoxHeader title="user.profile.personal_info" class="mb-6">
                <template #icon>
                    <IconsUser2 class-wrapper="w-5" fill-color="fill-green-400" />
                </template>
            </CommonBoxHeader>

            <div class="relative">
                <ClientOnly>
                    <Veeform class="flex flex-col gap-7">
                        <CommonTextInput
                            type="text"
                            name="username"
                            :label="$t('user.profile.profile_info.username')"
                            id="username"
                            :placeholder="user?.username"
                            disabled
                        />
                        <div class="relative">
                            <CommonTextInput
                                v-if="user?.fullname.toLowerCase() !== user?.username.toLowerCase()"
                                type="text"
                                name="fullname"
                                :label="$t('user.profile.profile_info.display_name')"
                                :placeholder="user?.fullname"
                                :classes="isUpdateFullName ? 'pr-[83px]' : ''"
                                disabled
                            />
                            <div
                                v-if="isUpdateFullName"
                                class="absolute right-2 top-1/2 flex -translate-y-1/2 transform items-center"
                            >
                                <span
                                    class="cursor-pointer p-2 text-[14px] text-sm font-medium capitalize leading-[20px] text-orange-700"
                                    @click="openFullName"
                                >
                                    Cập nhật
                                </span>
                            </div>
                        </div>
                        <CommonTextInput
                            type="text"
                            name="phone_number"
                            :label="$t('user.profile.profile_info.phone_number')"
                            id="phone_number"
                            :placeholder="user?.phone"
                            disabled
                        />
                        <UserProfileVerifyEmail />
                        <UserProfileVerifyTelegram />
                    </Veeform>
                </ClientOnly>
            </div>
        </CommonBoxWrapper>
    </CommonErrorWrapper>
</template>
<script setup>
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore, useBankStore, useWithdrawStore } from '~/stores'
import { Form as Veeform } from 'vee-validate'

const useUserStoreInsatance = useUserStore()
const { user } = storeToRefs(useUserStoreInsatance)
const bankStoreInstance = useBankStore()
const { getUserBankAccounts } = bankStoreInstance

const useWithdrawStoreInstance = useWithdrawStore()
const { getWithdrawBanksList } = useWithdrawStoreInstance
const useModalStoreInstance = useModalStore()
const { showUpdateFullnameModal } = storeToRefs(useModalStoreInstance)

const isUpdateFullName = computed(() => user?.value?.is_updated_fullname === 0)
const openFullName = () => {
    showUpdateFullnameModal.value = true
}
onMounted(async () => {
    await nextTick(async () => {
        await Promise.all([getWithdrawBanksList(), getUserBankAccounts()])
    })
})
</script>
