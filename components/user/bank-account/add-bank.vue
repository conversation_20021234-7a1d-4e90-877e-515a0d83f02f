<template>
    <Veeform
        ref="addBankForm"
        class="space-y-7"
        @submit="createUserBank"
        :validation-schema="addBankSchema($t)"
    >
        <CommonDropdownBank
            @change="onChangeBank"
            :data="updatedBankList"
            :placeholder="{
                title: $t('user.withdraw.bank.select_bank_placeholder'),
                icon: '/assets/images/icons/withdraw/ic-bank.svg',
            }"
            :alwaysOpen="true"
        />
        <CommonTextInput
            type="text"
            name="bank_account_no"
            :label="$t('user.bank.add_bank.bank_account_no')"
            id="bank_account_no"
            :placeholder="$t('user.bank.add_bank.bank_account_no_placeholder')"
            required
            :maxlength="MAX_LENGTH.BANK_ACCOUNT_NO"
            :regex="/\d+/g"
        />
        <CommonTextInput
            :value="bankAccountName?.toUpperCase().trim()"
            type="text"
            name="bank_account_name"
            :label="$t('user.bank.add_bank.bank_account_name')"
            :placeholder="$t('user.bank.add_bank.bank_account_name_placeholder')"
            :maxlength="MAX_LENGTH.BANK_ACCOUNT_NAME"
            :is-uppercase="true"
            :isFormatUserName="true"
            required
            :id="!bankAccountName ? 'fromBankNameInput' : ''"
            :disabled="!!bankAccountName"
            :regex="/^[a-zA-Z\s]+$/"
            @input="onUserInput"
        />
        <div class="flex justify-center">
            <CommonButton
                classes="btn-submit min-w-[210px]"
                :loading="isLoading"
                :isDisabled="isLoading || isFormAddBankInvalid || !userInputValue.trim()"
                @click="onConfirmDiscardTransaction"
            >
                {{ $t('user.bank.add_bank.add_bank_account') }}
            </CommonButton>
        </div>
    </Veeform>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { Form as Veeform } from 'vee-validate'
import { useNotify } from '~/composables/use-notify'
import { MAX_LENGTH } from '~/constants/user'
import { bankCodeMapping } from '~/constants/withdraw.ts'
import { addBankSchema } from '~/forms/addBank.schema'
import { useBankService } from '~/services'
import { useAlertStore, useBankStore, useWithdrawStore } from '~/stores'

defineProps(['show'])
const alertStore = useAlertStore()

const { showAddBankSuccessModal } = useNotify()

const bankService = useBankService()

const isLoading = ref(false)
const addBankForm = ref(null)

const useBankStoreInstance = useBankStore()
const { getUserBankAccounts } = useBankStoreInstance
const { userBankAccounts } = storeToRefs(useBankStoreInstance)

const useWithdrawStoreInstnce = useWithdrawStore()
const { withdrawBanksList } = storeToRefs(useWithdrawStoreInstnce)

const bankAccountName = ref(userBankAccounts.value[0]?.bank_account_name)
const differenceBy = (array, values, iteratee) => {
    const excludeSet = new Set(values.map(iteratee))
    return array.filter(item => !excludeSet.has(iteratee(item)))
}
watch(
    () => userBankAccounts.value,
    value => {
        bankAccountName.value = value[0]?.bank_account_name
        addBankForm?.value?.setFieldValue('bank_account_name', value[0]?.bank_account_name)
    }
)

const onChangeBank = value => {
    addBankForm?.value?.setFieldValue('bank_code', value?.bank_code)
}

const withdrawBanksListComputed = computed(() => {
    return withdrawBanksList.value.map(item => ({
        ...item,
        value: item.bank_code,
        label: bankCodeMapping[item.bank_code?.toLowerCase()] || item.bank_code,
        icon: `/assets/images/v2/banks/icon-logo/${bankCodeToName(item.bank_code)}.webp?v=1`,
    }))
})

const updatedBankList = computed(() => {
    return differenceBy(withdrawBanksListComputed.value, userBankAccounts.value, bank =>
        bank.bank_code.toLowerCase()
    )
})

const isFormAddBankInvalid = computed(() => {
    const values = addBankForm.value?.values || {}
    return !values?.bank_code || !values?.bank_account_name || !values?.bank_account_no
})

const userInputValue = ref('')

const onUserInput = event => {
    userInputValue.value = event.target.value
}

const createUserBank = async payload => {
    try {
        isLoading.value = true
        const { data } = await bankService.createBank(payload)
        if (data?.value?.status === 'OK') {
            addBankForm?.value?.setFieldValue('bank_account_name', '')
            showAddBankSuccessModal()
            await getUserBankAccounts()
        } else if (data.value.message) {
            alertStore.showMessageError(data)
        } else {
            throw data
        }
    } catch (error) {
        let messageError = ''
        messageError = error?.value?.data?.message || error?.value?.message || error?.value || error

        alertStore.showMessageError(messageError || 'Đã xảy ra lỗi. Vui lòng kiểm tra lại!')
    } finally {
        isLoading.value = false
    }
}
</script>
