<template>
    <div class="flex h-full flex-col gap-1.5">
        <p class="text-left font-[500]">{{ props.cardProvider }}</p>
        <Swiper
            :pagination="pagination"
            :modules="[Navigation, Pagination]"
            :slides-per-view="1"
            :loop="true"
            :navigation="{
                prevEl: '.phonecard-details__button-prev',
                nextEl: '.phonecard-details__button-next',
            }"
        >
            <SwiperSlide v-for="item in items" :key="item.serial">
                <div>
                    <div class="flex items-baseline justify-between">
                        <span class="text-slate-300">{{
                            $t('user.deposit.phonecard.card_serial.title')
                        }}</span>
                        <p class="flex items-center gap-1">
                            <span class="text-white">{{ item.serial }}</span>
                            <CommonWidgetCopy :value="item.serial" />
                        </p>
                    </div>
                    <div class="flex items-baseline justify-between">
                        <span class="text-slate-300">{{
                            $t('user.deposit.phonecard.card_code.title')
                        }}</span>
                        <p class="flex items-center gap-1">
                            <span class="text-white">{{ item.pincode }}</span>
                            <CommonWidgetCopy :value="item.pincode" />
                        </p>
                    </div>
                </div>
            </SwiperSlide>
            <div class="swiper__direction phonecard-details__direction mt-3">
                <div class="swiper__button-prev phonecard-details__button-prev" />
                <div class="swiper__pagination phonecard-details__pagination text-center" />
                <div class="swiper__button-next phonecard-details__button-next" />
            </div>
        </Swiper>
    </div>
</template>
<script setup>
import { Navigation, Pagination } from 'swiper/modules'

const props = defineProps({
    cardProvider: {
        type: String,
        default: '',
    },
    cardSerial: {
        type: String,
        default: '',
    },
})

const items = computed(() => {
    return JSON.parse(props.cardSerial)
})

const pagination = {
    el: '.phonecard-details__pagination',
    clickable: true,
    renderBullet: function (index, className) {
        return '<span class="' + className + '">' + (index + 1) + '</span>'
    },
}
</script>
<style lang="scss" scoped>
.swiper {
    width: 100%;
    @apply w-full rounded-[10px];
    :deep(.swiper-pagination-bullet) {
        @apply flex h-6 w-6 items-center justify-center rounded border-none bg-slate-950 text-center text-xs text-slate-600 opacity-100;
    }
    :deep(.swiper-pagination-bullet-active) {
        @apply text-white;
    }
}
</style>
