<template>
    <div class="codepay">
        <div class="mb-6 space-y-6 px-3 max-xl:rounded-2xl max-xl:bg-slate-900 max-xl:p-3 xl:px-0">
            <Veeform
                id="create_codepay"
                ref="codepayForm"
                v-slot="{ values }"
                :initialValues="initialValues"
                :validation-schema="depositCodepaySchema"
                @submit="handleDepositCodepay"
                class="flex flex-col max-lg:gap-5 lg:gap-6"
            >
                <div>
                    <SpecificAccountDepositCodepayStepsProgress
                        :step-list="stepList"
                        :active-step="activeStep"
                    />
                </div>
                <template v-if="activeStep.value === stepList.step1.value">
                    <CommonTextInput
                        name="amount_deposit"
                        type="tel"
                        :value="defaultAmount"
                        :label="$t('user.deposit.codepay.input_amount_label')"
                        classes="text-sm font-medium h-14"
                        labelClass="bg-slate-900"
                        money
                        :isFormatNumber="true"
                        :exchangingRate="1"
                        maxlength="11"
                        :regex="/\d+/g"
                    />

                    <SpecificAccountDepositCodepayAmounts
                        :amount-list="amountList"
                        :selected-amount="selectedAmount"
                        :handle-number-input="handleNumberInput"
                    />

                    <SpecificAccountDepositCodepaySelectPromotion
                        v-if="values?.amount_deposit"
                        :package-selected="packageSelected"
                        :amount-deposit="
                            Number(values?.amount_deposit?.toString().replace(/\,/g, ''))
                        "
                        :on-change-package="onChangePackage"
                    />

                    <div class="form__submit flex justify-center">
                        <CommonButton
                            :loading="isLoadingForm"
                            :isDisabled="
                                isInitial || Object.keys(codepayForm?.errors || {})?.length > 0
                            "
                            classes="btn-submit min-w-[210px] max-lg:!max-w-none"
                            >{{ $t('user.deposit.codepay.button_create_qr') }}</CommonButton
                        >
                    </div>
                </template>
                <template v-else>
                    <div
                        class="codepay--data flex flex-col gap-3 rounded-[10px] p-3 lg:px-8 lg:py-7"
                    >
                        <div
                            class="codepay--bottom flex flex-col items-center lg:flex-row lg:gap-16"
                            :class="nicepayStatus"
                        >
                            <div
                                v-if="showMobile"
                                class="countdown mb-2 flex w-full gap-[9px] pl-[10px] text-[14px] font-semibold leading-[18px]"
                            >
                                <div class="nicepay__form--status">
                                    <div :class="nicepayStatus" class="flex items-center gap-1">
                                        <span
                                            class="bg-slate-900 max-lg:h-4 max-lg:w-4 max-lg:rounded-[2px] max-lg:p-[2px] lg:h-6 lg:w-6 lg:rounded-[10px] lg:p-1"
                                        >
                                            <IconsClock2
                                                classWrapper="max-lg:w-3 max-lg:h-3 lg:w-4 lg:h-4"
                                            />
                                        </span>
                                        <span
                                            v-if="activeStep.value === stepList.step2.value"
                                            class="countdown float-right text-xs font-semibold text-cyan-400"
                                            >{{ remainTime }}</span
                                        >
                                        <span
                                            v-if="
                                                activeStep.value === stepList.step3.value &&
                                                remainTimeSucess > 0
                                            "
                                            class="countdown float-right text-xs font-semibold text-green-700"
                                        >
                                            00:0{{ remainTimeSucess }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="codepay--bottom-left flex w-full items-center justify-center gap-3 xl:w-[160px] xl:flex-col xl:gap-1"
                            >
                                <!-- <p
                                    class="hidden text-xs font-medium leading-[18px] xl:block"
                                >
                                    {{ $t('user.deposit.codepay.qr.id') }}
                                    <span class="text-rose-600"
                                        >#{{ nicepayInfo?.id || '' }}</span
                                    >
                                </p> -->

                                <div
                                    class="relative w-[132px] min-w-[132px] rounded-xl bg-white p-3 xl:h-[160px] xl:w-full xl:min-w-[130px]"
                                >
                                    <img
                                        v-if="nicepayInfo?.bank_code"
                                        :src="`/assets/images/v2/banks/icon-logo/${nicepayInfo.bank_code.toLowerCase()}.webp?v=1`"
                                        :alt="nicepayInfo.bank_name"
                                        :data-bankcode="nicepayInfo.bank_code.toLowerCase()"
                                        class="qr-code__icon-bank absolute left-1/2 top-1/2 h-[24px] w-[24px] -translate-x-1/2 -translate-y-1/2"
                                        @error="handleImageError"
                                    />

                                    <img alt="qrcode" :src="qrcode" class="h-full w-full" />
                                    <span
                                        class="btn-download absolute left-full top-3 flex h-7 w-7 cursor-pointer items-center justify-center rounded-r-lg bg-slate-900"
                                        @click="downloadImg(qrcode)"
                                    >
                                        <IconsDownload />
                                    </span>
                                </div>
                            </div>
                            <div class="codepay--bottom-right w-full lg:pt-2.5 xl:flex-1 xl:pt-0">
                                <div v-if="showMobile" class="info mb-7">
                                    <div class="info__value flex items-center gap-1 !text-white">
                                        <img
                                            v-if="nicepayInfo?.bank_code"
                                            :src="`/assets/images/v2/banks/icon-logo/${nicepayInfo.bank_code.toLowerCase()}.webp?v=1`"
                                            :alt="nicepayInfo.bank_name"
                                            :data-bankcode="nicepayInfo.bank_code.toLowerCase()"
                                            class="qr-code__icon-bank h-[20px] w-[20px]"
                                            @error="handleImageError"
                                        />
                                        {{ nicepayInfo?.bank_name }}
                                    </div>
                                </div>
                                <div
                                    v-if="!showMobile"
                                    class="countdown mb-[6px] flex gap-[9px] pl-[10px] text-[14px] font-semibold leading-[18px]"
                                >
                                    <div class="nicepay__form--status">
                                        <div :class="nicepayStatus" class="flex items-center gap-1">
                                            <span
                                                class="bg-slate-900 max-lg:h-4 max-lg:w-4 max-lg:rounded-[2px] max-lg:p-[2px] lg:h-6 lg:w-6 lg:rounded-[10px] lg:p-1"
                                            >
                                                <IconsClock2
                                                    classWrapper="max-lg:w-3 max-lg:h-3 lg:w-4 lg:h-4"
                                                />
                                            </span>
                                            <span
                                                v-if="activeStep.value === stepList.step2.value"
                                                class="countdown float-right text-xs font-semibold text-cyan-400"
                                                >{{ remainTime }}</span
                                            >
                                            <span
                                                v-if="
                                                    activeStep.value === stepList.step3.value &&
                                                    remainTimeSucess > 0
                                                "
                                                class="countdown float-right text-xs font-semibold text-green-700"
                                            >
                                                00:0{{ remainTimeSucess }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="info">
                                    <div class="info__label text-slate-300">
                                        {{ $t('user.deposit.ewallet.label.amount') }}
                                    </div>
                                    <div class="info__value !font-bold !text-white">
                                        {{ NumberUtils.formatNumberWithDots(nicepayInfo?.amount) }}
                                        VND
                                    </div>
                                    <CommonWidgetCopy :value="nicepayInfo?.amount" />
                                </div>
                                <div v-if="!showMobile" class="info">
                                    <div class="info__label text-slate-300">
                                        {{ $t('user.deposit.codepay.qr.bank_name') }}
                                    </div>
                                    <div class="info__value flex items-center gap-1 !text-white">
                                        {{ nicepayInfo?.bank_name }}
                                        <img
                                            v-if="nicepayInfo?.bank_code"
                                            :src="`/assets/images/v2/banks/icon-logo/${nicepayInfo.bank_code.toLowerCase()}.webp?v=1`"
                                            :alt="nicepayInfo.bank_name"
                                            :data-bankcode="nicepayInfo.bank_code.toLowerCase()"
                                            class="qr-code__icon-bank h-[20px] w-[20px]"
                                            @error="handleImageError"
                                        />
                                    </div>
                                </div>
                                <div class="info">
                                    <div class="info__label text-slate-300">
                                        {{ $t('user.deposit.codepay.qr.bank_account_name') }}
                                    </div>
                                    <div class="info__value !text-white">
                                        {{ nicepayInfo?.bank_account_name }}
                                    </div>
                                    <CommonWidgetCopy :value="nicepayInfo?.bank_account_name" />
                                </div>
                                <div class="info">
                                    <div class="info__label text-slate-300">
                                        {{ $t('user.deposit.codepay.qr.bank_account_no') }}
                                    </div>
                                    <div class="info__value !text-white">
                                        {{ nicepayInfo?.bank_account_no }}
                                    </div>
                                    <CommonWidgetCopy :value="nicepayInfo?.bank_account_no" />
                                </div>
                                <div class="info">
                                    <div class="info__label text-slate-300">
                                        {{ $t('user.deposit.codepay.qr.code') }}
                                    </div>
                                    <div class="info__value !font-semibold !text-green-400">
                                        {{ nicepayInfo?.code }}
                                    </div>
                                    <CommonWidgetCopy :value="nicepayInfo?.code" />
                                </div>
                            </div>
                        </div>
                        <div class="line">
                            <span v-for="index in lines" :key="index"></span>
                        </div>
                    </div>

                    <div class="flex justify-center">
                        <CommonButton
                            classes="btn-submit min-w-[210px] max-lg:!max-w-none"
                            :loading="isLoading"
                            :isDisabled="
                                (activeStep.value === stepList.step2.value &&
                                    enableBtnCountdown > 0) ||
                                isInitial
                            "
                            @click="onConfirmDiscardTransaction"
                        >
                            {{ $t(getButtonLabel) }}
                            <span
                                class="pl-1"
                                v-if="
                                    activeStep.value === stepList.step2.value &&
                                    enableBtnCountdown > 0
                                "
                                >({{ enableBtnCountdown }}s)</span
                            >
                        </CommonButton>
                    </div>
                </template>
            </Veeform>
        </div>

        <SpecificAccountWithdrawDepositGuideline />
    </div>
</template>
<script setup>
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { useDepositStore, useModalStore, useUserStore, useShowErrorStore } from '~/stores'
import { depositCodepaySchema } from '~/forms/depositCodepay.schema'
import { Form as Veeform } from 'vee-validate'
import downloadImg from '~/utils/image/download-img'
import getRemainTime from '~/utils/countdown/get-remain-time'
import useCustomFetch from '~/composables/useCustomFetch'
import { NumberUtils } from '~/utils'
import { PROMOTION_PLAN_TYPE } from '~/constants/user'
import { statusTransaction } from '~/constants'

const lines = Array.from({ length: 10 }, (_, i) => i + 1)

const { NICEPAY_NEW_QR_TIME, NICEPAY_SUCCESS_TIME } = useRuntimeConfig().public

const { showMobile } = useCommon()

const { t } = useI18n()

const fetcher = useCustomFetch()
const useModalStoreInstance = useModalStore()
const { showConfirmPromoModal } = storeToRefs(useModalStoreInstance)

const useShowErrorStoreInstance = useShowErrorStore()
const { onShowErrorModal } = useShowErrorStoreInstance

const useUserInstances = useUserStore()
const { user } = storeToRefs(useUserInstances)

const isLoading = ref(false)
const isLoadingForm = ref(false)
const codepayForm = ref(null)
const isInitial = ref(true)
const stepList = {
    step1: {
        value: 1,
        label: 'Nạp tiền',
    },
    step2: {
        value: 2,
        label: 'Thanh toán',
    },
    step3: {
        value: 3,
        label: 'Hoàn thành',
    },
}
const defaultAmount = 100
const defaultPackage = 1
const amountList = [
    { value: 100 },
    { value: 200 },
    { value: 500 },
    { value: 1000 },
    { value: 2000 },
    { value: 5000, status: statusTransaction.proposed },
    { value: 10000 },
    { value: 20000 },
    { value: 50000 },
    { value: 100000 },
    { value: 200000 },
    { value: 300000 },
]
const remainTime = ref('')
const nicepayStatus = ref('')
const NicePayStatus = {
    STATUS_PROCESSING: 'processing',
    STATUS_FAILED: 'failed',
    STATUS_SUCESS: 'success',
}

const stepTwoInterval = ref(null)

const useDepositInstance = useDepositStore()
const { packages, nicepayInfo, nicepayStep } = storeToRefs(useDepositInstance)
const {
    getNicepayQRcode,
    createCodepayDeposit,
    setNicepayQRcode,
    setNicepayStep,
    setDiscardingNicepayTime,
    getDiscardingNicepayTime,
} = useDepositInstance

const qrcode = ref(getNicepayQRcode())
const activeStep = ref(qrcode.value ? stepList.step2 : stepList.step1)

const initialValues = ref({
    amount_deposit: defaultAmount,
    package_id: defaultPackage,
})

const packageSelected = ref(defaultPackage)
const enableBtnCountdown = ref(0)
const countdownSuccessInterval = ref(null)
const remainTimeSucess = ref(0)
const btnCountdownInterval = ref(null)
const selectedAmount = ref('0_100')

const onChangePackage = value => {
    packageSelected.value = value
    codepayForm.value?.setValues({
        package_id: value,
    })
}

const handleNumberInput = (value, index) => {
    codepayForm.value?.setValues({
        amount_deposit: NumberUtils.formatNumberWithDots(value.toString()?.replace(/\,/g, '')),
    })
    selectedAmount.value = `${index}_${value}`
}

const handleDepositCodepay = async payload => {
    isLoadingForm.value = true
    if (user.value?.package_id && user.value?.package_id !== PROMOTION_PLAN_TYPE.COMMISSION) {
        showConfirmPromoModal.value = true
        isLoadingForm.value = false
        return
    }

    if (payload.amount_deposit && payload.package_id) {
        const data = await createCodepayDeposit({
            amount: NumberUtils.formatMarkToNumber(payload.amount_deposit, 1000),
            packageId: payload.package_id,
        })
        if (data) {
            activeStep.value = stepList.step2
            setNicepayStep(stepList.step2.value)
            qrcode.value = nicepayInfo.value.qrcode ?? ''

            setNicepayQRcode(nicepayInfo.value.qrcode ?? '')
            remainTime.value = getRemainTime(nicepayInfo.value.expired_at_utc, false)
            setDiscardingNicepayTime(Number(NICEPAY_NEW_QR_TIME || 0))
            countdownTime()
            countDownDiscardTransaction()
            refreshDataInfo()
        }
    }
    isLoadingForm.value = false
}

const onConfirmDiscardTransaction = () => {
    isLoading.value = true

    onShowErrorModal({
        title: t('user.deposit.codepay.modal.title'),
        message: t('user.deposit.codepay.modal.question_confirm', {
            ticketId: nicepayInfo.value?.id,
        }),
        cancelButton: t('user.deposit.codepay.modal.btn_cancel'),
        confirmButton: t('user.deposit.codepay.modal.btn_ok'),
        icon: '/assets/images/v2/icons/deposit-warning.webp',
        onCancel: () => {
            return
        },
        onConfirm: () => {
            resetData()
        },
    })

    if (enableBtnCountdown.value > 0) {
        isLoading.value = false
        return
    }
    isLoading.value = false
}

const countDownDiscardTransaction = () => {
    enableBtnCountdown.value = Number(getDiscardingNicepayTime() || 0) || 0
    btnCountdownInterval.value = setInterval(() => {
        if (enableBtnCountdown.value <= 0) {
            enableBtnCountdown.value = 0
            setDiscardingNicepayTime(0)
            if (btnCountdownInterval.value) {
                clearInterval(btnCountdownInterval.value)
            }
        } else {
            enableBtnCountdown.value -= 1
            setDiscardingNicepayTime(enableBtnCountdown.value)
        }
    }, 1000)
}
const resetData = () => {
    nicepayInfo.value = null
    activeStep.value = stepList.step1
    setNicepayStep(stepList.step1.value)
    remainTime.value = ''
    nicepayStatus.value = ''
    setNicepayQRcode('')
    qrcode.value = ''
    enableBtnCountdown.value = 0
    packageSelected.value = packages.value?.[0]?.id
    codepayForm.value?.resetForm({
        values: {
            amount_deposit: defaultAmount,
            package_id: packageSelected.value,
        },
    })
    if (btnCountdownInterval.value) {
        clearInterval(btnCountdownInterval.value)
    }

    if (stepTwoInterval.value) {
        clearInterval(stepTwoInterval.value)
    }

    if (refreshDataInterval.value) {
        clearInterval(refreshDataInterval.value)
    }

    if (countdownSuccessInterval.value) {
        clearInterval(countdownSuccessInterval.value)
    }
}

const countdownTime = () => {
    if (nicepayInfo.value) {
        stepTwoInterval.value = setInterval(() => {
            remainTime.value = getRemainTime(nicepayInfo.value?.expired_at_utc ?? '', false)
            if (remainTime.value === '00:00') {
                remainTime.value = ''
                nicepayStatus.value = NicePayStatus.STATUS_FAILED
                activeStep.value = stepList.step3
                setNicepayStep(stepList.step3.value)
                if (stepTwoInterval.value) {
                    clearInterval(stepTwoInterval.value)
                }
                if (refreshDataInterval.value) {
                    clearInterval(refreshDataInterval.value)
                }
                setTimeout(() => {
                    resetData()
                }, 1000)
            }
        }, 1000)
    }
}

const refreshDataInterval = ref(null)
const refreshDataInfo = () => {
    refreshDataInterval.value = setInterval(() => {
        void getNicePayInfo(false)
    }, 5000)
}

const countDownDepositSuccess = () => {
    remainTimeSucess.value = Number(NICEPAY_SUCCESS_TIME || 5)
    countdownSuccessInterval.value = setInterval(() => {
        if (remainTimeSucess.value <= 0) {
            remainTimeSucess.value = 0
            if (countdownSuccessInterval.value) {
                clearInterval(countdownSuccessInterval.value)
            }
            resetData()
        } else {
            remainTimeSucess.value -= 1
        }
    }, 1000)
}
const getNicePayInfo = async (isInit = false) => {
    if (nicepayStep.value <= stepList.step1.value) {
        return
    }

    try {
        const { data } = await fetcher.get('/payment/nicepayInfo')
        if (!getNicepayQRcode()) {
            nicepayInfo.value = null
        } else {
            nicepayInfo.value = data.value?.data?.[0] || null
        }
        if (nicepayInfo.value) {
            const expiredTime = dayjs(nicepayInfo.value?.expired_at_utc ?? '')
            activeStep.value = stepList.step2
            setNicepayStep(stepList.step2.value)
            if (expiredTime.diff(dayjs(), 'seconds') > 0) {
                if (nicepayInfo.value.deposited) {
                    activeStep.value = stepList.step3
                    setNicepayStep(stepList.step3.value)
                    nicepayStatus.value = NicePayStatus.STATUS_SUCESS
                    remainTime.value = ''
                    countDownDepositSuccess()
                    if (stepTwoInterval.value) {
                        clearInterval(stepTwoInterval.value)
                    }
                    if (refreshDataInterval.value) {
                        clearInterval(refreshDataInterval.value)
                    }
                    return
                }
                nicepayStatus.value = NicePayStatus.STATUS_PROCESSING
                activeStep.value = stepList.step2
                setNicepayStep(stepList.step2.value)
                return
            }
        }
        if (isInit) {
            if (nicepayInfo.value) {
                activeStep.value = stepList.step3
                setNicepayStep(stepList.step3.value)
                nicepayStatus.value = NicePayStatus.STATUS_FAILED
                setTimeout(() => {
                    resetData()
                }, 1000)
            } else {
                setNicepayQRcode('')
                activeStep.value = stepList.step1
                setNicepayStep(stepList.step1.value)
            }
        } else {
            activeStep.value = stepList.step3
            setNicepayStep(stepList.step3.value)
            nicepayStatus.value = NicePayStatus.STATUS_FAILED
            setTimeout(() => {
                resetData()
            }, 1000)
        }
    } catch (e) {
        console.log(e)
        nicepayInfo.value = null
    } finally {
        isLoading.value = false
    }
}

const getButtonLabel = computed(() => {
    if (activeStep.value.value === stepList.step2.value) {
        return 'user.deposit.codepay.button.new'
    }
    return nicepayStatus.value === NicePayStatus.STATUS_SUCESS
        ? 'user.deposit.codepay.button.continue'
        : 'user.deposit.codepay.button.retry'
})

const handleImageError = event => {
    const bankName = event.target.alt.toLowerCase()
    const bankNameIcon = `/assets/images/v2/banks/icon-logo/${bankName}.webp?v=1`
    const defaultImage = '/assets/images/v2/banks/icon-logo/default.webp?v=1'

    const currentSrc = new URL(event.target.src).pathname
    if (currentSrc === bankNameIcon) {
        event.target.src = defaultImage
    } else {
        event.target.src = bankNameIcon
    }
}

onMounted(() => {
    if (packages.value?.length) {
        packageSelected.value = packages.value?.[0]?.id
    }
})
onMounted(async () => {
    await nextTick(async () => {
        await getNicePayInfo(true)
        if (activeStep.value.value === stepList.step2.value) {
            remainTime.value = getRemainTime(nicepayInfo?.value?.expired_at_utc || '', false)
            countdownTime()
            countDownDiscardTransaction()
            refreshDataInfo()
        } else {
            resetData()
        }
        isLoading.value = false
    })

    setTimeout(() => {
        isInitial.value = false
    }, 1000)
})

onUnmounted(() => {
    if (refreshDataInterval.value) {
        clearInterval(refreshDataInterval.value)
    }
})
watch(
    () => codepayForm.value?.values?.amount_deposit,
    value => {
        const amount = Number(value?.toString()?.replace(/\,/g, ''))

        const index = amountList.findIndex(item => item.value === amount)
        if (index !== -1) {
            selectedAmount.value = `${index}_${value}`
        } else {
            selectedAmount.value = ''
        }
    }
)

watch(
    () => packages.value?.[0]?.id,
    value => {
        if (value) {
            packageSelected.value = value
        }
    }
)
</script>
<style lang="scss" scoped>
.info {
    @apply flex h-8 flex-nowrap items-center gap-1 px-[10px];

    &__label {
        @apply w-[95px] shrink-0 text-[12px] font-normal leading-4 xl:leading-[14.63px];
    }

    &__value {
        @apply w-[calc(100%_-_116px)] text-[12px] font-medium leading-4 text-grey-900 xl:w-[calc(100%_-_144px)] xl:leading-[14.63px];
    }

    &__copy {
        @apply w-6 cursor-pointer text-[24px] text-[#717589];
        &.copied {
            @apply text-rose-600;
        }
    }

    &:last-child {
        background: rgba($color: #fff, $alpha: 0.1);
        @apply rounded-[6px];
    }
}

.codepay--data {
    background: linear-gradient(247.57deg, #08336d 3.75%, #103f7e 46.99%, #06254f 100.85%);
    position: relative;
    @media (max-width: 1023px) {
        width: 342px;
        margin: 0 auto;
    }
    .line {
        @apply absolute top-0 flex items-center justify-between gap-1 overflow-hidden max-lg:left-0 max-lg:top-[200px] max-lg:h-7 max-lg:w-full lg:left-[210px] lg:h-full lg:w-9 lg:flex-col;
        &::before {
            @apply absolute left-0 top-0 block transform rounded-full bg-slate-900 content-[''] max-lg:h-7 max-lg:w-7 max-lg:-translate-x-1/2 lg:h-9 lg:w-9 lg:-translate-y-1/2;
        }
        &::after {
            @apply absolute bottom-0 block transform rounded-full bg-slate-900 content-[''] max-lg:right-0 max-lg:h-7 max-lg:w-7 max-lg:translate-x-1/2 lg:left-0 lg:h-9 lg:w-9 lg:translate-y-1/2;
        }
        span {
            @apply rounded-sm bg-slate-900 max-lg:h-1 max-lg:w-4 lg:h-4 lg:w-1;
        }
    }
}
.codepay--bottom {
    &.failed {
        .nicepay__form--status {
            @apply text-[#F03535];
        }
    }
    &.success {
        .nicepay__form--status {
            @apply text-[#1FAB13];
        }
    }
}

.peer:checked + .method-item {
    @apply border-rose-600 text-rose-600;
    background: linear-gradient(123.38deg, #ffffff 11.43%, #ffebeb 81.75%);
}

.method-item {
    @apply flex h-[42px] cursor-pointer items-center justify-center rounded-lg border border-solid border-grey-200 font-medium text-grey-900;
    background: linear-gradient(114.48deg, #fdfefe 9.88%, #f8fafb 101.25%);
}
</style>
