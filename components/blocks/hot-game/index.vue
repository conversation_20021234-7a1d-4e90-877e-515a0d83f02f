<template>
    <div class="hot-games">
        <BlocksHeader title="home.popular_game" icon="/assets/images/v2/home/<USER>">
            <CommonButtonViewmore link="/cong-game/all?sort=hot" title="common.show_more" />
        </BlocksHeader>

        <CommonGameSkeleton v-if="gamesByFilter[storeFilterName]?.isLoading" />
        <div
            v-else-if="gamesByFilter[storeFilterName]?.data?.items?.length"
            class="grid grid-cols-2 max-lg:gap-x-[6px] max-lg:gap-y-3 lg:grid-cols-4 lg:gap-x-4 lg:gap-y-5"
        >
            <CommonGameCard
                v-for="hotGame in gamesByFilter[storeFilterName]?.data?.items"
                :key="hotGame.partner_game_id"
                :data="hotGame"
                :jackpotvalue="findJackpot(hotGame)"
                :show-favorite="true"
                :favoriteType="storeFilterName"
                favoriteColor="fill-white"
                titleClass="text-sm font-normal capitalize text-white xl:text-base"
                providerClass="text-[10px] text-slate-300 max-xl:font-medium md:text-sm"
                buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                @click="handleShowGame(hotGame)"
            />
        </div>
    </div>
</template>
<script setup>
import { useGamesStore } from '~/stores'
import { storeToRefs } from 'pinia'

const storeFilterName = 'hotGames'

const gameStoreInstance = useGamesStore()

const { getGamesByFilter } = gameStoreInstance
const { gamesByFilter } = storeToRefs(gameStoreInstance)

// const { download } = useImageDownloader()

const { handleShowGame } = useGame()

const { findJackpot } = useJackpot()

await useAsyncData(storeFilterName, async () => {
    await getGamesByFilter(storeFilterName, {
        type: 'hot',
        limit: 8,
    })
    return {
        hotGames: gamesByFilter[storeFilterName],
    }
})

// onMounted(() => {
//     if (gamesByFilter.value[storeFilterName]?.data?.items?.length) {
//         const files = gamesByFilter.value[storeFilterName].data.items.map(game => game.image);
//         const filesName = files.map(file => file.substring(file.lastIndexOf('/') + 1).split('.')[0].toLowerCase());
//         console.log('Files to download:', files);
//         download(files);
//     }
// })
</script>
