<template>
    <div
        class="hero-banner-slider relative overflow-hidden max-lg:aspect-[306/94] max-lg:rounded-lg lg:aspect-[1420/300] lg:rounded-[20px]"
    >
        <Swiper
            :modules="[SwiperNavigation, SwiperAutoplay, SwiperScrollbar]"
            :loop="true"
            :slides-per-view="1"
            :autoplay="{
                delay: 5000,
                disableOnInteraction: false,
            }"
            :scrollbar="{
                clickable: true,
                el: '.hero-banner-swiper-scrollbar',
            }"
            :navigation="{
                nextEl: '#next-hero-banner',
                prevEl: '#prev-hero-banner',
            }"
            class="h-full"
        >
            <SwiperSlide v-for="(card, index) in bannersList" :key="index" class="relative h-auto">
                <BlocksHeroBannerItem :data-item="card" :gotoDetail="gotoDetail" />
            </SwiperSlide>
        </Swiper>
        <div class="hero-banner-swiper-scrollbar aspect-[1420/300]"></div>
        <button id="prev-hero-banner" class="btn-slider-nav rotate-180 transform">
            <IconsArrow fillColor="fill-white" classWrapper="w-5 h-5" />
        </button>
        <button id="next-hero-banner" class="btn-slider-nav">
            <IconsArrow fillColor="fill-white" classWrapper="w-5 h-5" />
        </button>
    </div>
</template>

<script setup>
const localePath = useLocalePath()

const bannersList = [
    {
        media: {
            bgPc1x: 'assets/images/v2/hero-banner/new/live-baccarat-bg-pc_1x.webp',
            bgPc2x: 'assets/images/v2/hero-banner/new/live-baccarat-bg-pc_2x.webp',
            bgMb1x: 'assets/images/v2/hero-banner/new/live-baccarat-bg-mb_1x.webp',
            bgMb2x: 'assets/images/v2/hero-banner/new/live-baccarat-bg-mb_2x.webp',
            textPc1x: '/assets/images/v2/hero-banner/new/live-baccarat-text-pc_1x.webp',
            textPc2x: '/assets/images/v2/hero-banner/new/live-baccarat-text-pc_2x.webp',
            textMb1x: '/assets/images/v2/hero-banner/new/live-baccarat-text-mb_1x.webp',
            textMb2x: '/assets/images/v2/hero-banner/new/live-baccarat-text-mb_2x.webp',
        },
        url: '/casino/all?sort=hot&partner=vingame',
        classes: 'banner-live-baccarat ',
        classesText: 'lg:aspect-[468/157] max-lg:aspect-[139/55]',
        title: 'Baccarat LIVE',
        text: 'CƠ HỘI THẮNG x13',
    },
    {
        media: {
            bgPc1x: 'assets/images/v2/hero-banner/new/live-casino-bg-pc_1x.webp',
            bgPc2x: 'assets/images/v2/hero-banner/new/live-casino-bg-pc_2x.webp',
            bgMb1x: 'assets/images/v2/hero-banner/new/live-casino-bg-mb_1x.webp',
            bgMb2x: 'assets/images/v2/hero-banner/new/live-casino-bg-mb_2x.webp',
            textPc1x: '/assets/images/v2/hero-banner/new/live-casino-text-pc_1x.webp',
            textPc2x: '/assets/images/v2/hero-banner/new/live-casino-text-pc_2x.webp',
            textMb1x: '/assets/images/v2/hero-banner/new/live-casino-text-mb_1x.webp',
            textMb2x: '/assets/images/v2/hero-banner/new/live-casino-text-mb_2x.webp',
        },
        url: '/promo/sieu-hoan-tra-hang-tuan',
        classes: 'banner-live-casino ',
        classesText: 'lg:aspect-[489/128] max-lg:aspect-[150/39]',
        title: 'Siêu Hoàn Trả Hằng Tuần Cho Người Chơi Live Casino',
        text: 'Siêu Hoàn Trả Hằng Tuần Live Casino 20%',
    },
    {
        media: {
            bgPc1x: 'assets/images/v2/hero-banner/new/promotion-100-bg-pc_1x.webp',
            bgPc2x: 'assets/images/v2/hero-banner/new/promotion-100-bg-pc_2x.webp',
            bgMb1x: 'assets/images/v2/hero-banner/new/promotion-100-bg-mb_1x.webp',
            bgMb2x: 'assets/images/v2/hero-banner/new/promotion-100-bg-mb_2x.webp',
            textPc1x: '/assets/images/v2/hero-banner/new/promotion-100-text-pc_1x.webp',
            textPc2x: '/assets/images/v2/hero-banner/new/promotion-100-text-pc_2x.webp',
            textMb1x: '/assets/images/v2/hero-banner/new/promotion-100-text-mb_1x.webp',
            textMb2x: '/assets/images/v2/hero-banner/new/promotion-100-text-mb_2x.webp',
        },
        url: '/promo/khuyen-mai-100-lan-nap-dau-tien',
        classes: 'banner-promotion-100',
        classesText: 'lg:aspect-[561/208] max-lg:aspect-[176/67]',
        title: 'Khuyến Mãi 100% Lần Nạp Đầu Tiên',
        text: 'Khuyến Mãi Nạp 100% Lên Đến 20,000,000 VNĐ',
    },
    {
        media: {
            bgPc1x: 'assets/images/v2/hero-banner/new/slot-bg-pc_1x.webp',
            bgPc2x: 'assets/images/v2/hero-banner/new/slot-bg-pc_2x.webp',
            bgMb1x: 'assets/images/v2/hero-banner/new/slot-bg-mb_1x.webp',
            bgMb2x: 'assets/images/v2/hero-banner/new/slot-bg-mb_2x.webp',
            textPc1x: '/assets/images/v2/hero-banner/new/slot-text-pc_1x.webp',
            textPc2x: '/assets/images/v2/hero-banner/new/slot-text-pc_2x.webp',
            textMb1x: '/assets/images/v2/hero-banner/new/slot-text-mb_1x.webp',
            textMb2x: '/assets/images/v2/hero-banner/new/slot-text-mb_2x.webp',
        },
        url: '/promo/hoan-tra-slots',
        classes: 'banner-slot',
        classesText: 'lg:aspect-[558/138] max-lg:aspect-[175/68]',
        title: 'Hoàn Trả Slots',
        text: 'Hoàn Trả Slots Hoàn Trả Lên Đến 10%',
    },
    {
        media: {
            bgPc1x: 'assets/images/v2/hero-banner/new/sport-bg-pc_1x.webp',
            bgPc2x: 'assets/images/v2/hero-banner/new/sport-bg-pc_2x.webp',
            bgMb1x: 'assets/images/v2/hero-banner/new/sport-bg-mb_1x.webp',
            bgMb2x: 'assets/images/v2/hero-banner/new/sport-bg-mb_2x.webp',
            textPc1x: '/assets/images/v2/hero-banner/new/sport-text-pc_1x.webp',
            textPc2x: '/assets/images/v2/hero-banner/new/sport-text-pc_2x.webp',
            textMb1x: '/assets/images/v2/hero-banner/new/sport-text-mb_1x.webp',
            textMb2x: '/assets/images/v2/hero-banner/new/sport-text-mb_2x.webp',
        },
        url: '/promo/hoan-tra-the-thao',
        classes: 'banner-sport',
        classesText: 'lg:aspect-[558/138] max-lg:aspect-[141/45]',
        title: 'Hoàn Trả Thể Thao Siêu Khủng Không Giới Hạn Lên Đến 1.6%',
        text: 'Cuồng Nhiệt Sports Hoàn Trả Lên Đến 1.6%',
    },
]

const gotoDetail = url => navigateTo(localePath(url))
</script>
<style lang="scss" scoped>
:deep() {
    .swiper-scrollbar-horizontal {
        @apply absolute !left-1/2 bottom-3 z-10 h-[6px] w-[100px] -translate-x-1/2 rounded-full bg-slate-100/50;
    }

    .swiper-scrollbar-drag {
        @apply bg-white;
    }
}

.btn-slider-nav {
    @apply absolute top-1/2 z-1 flex h-10 w-10 -translate-y-1/2 items-center justify-center rounded-xl bg-slate-100/20;
    @media screen and (max-width: 767px) {
        @apply hidden;
    }
}

#prev-hero-banner {
    left: 14px;
}

#next-hero-banner {
    right: 14px;
}
</style>
