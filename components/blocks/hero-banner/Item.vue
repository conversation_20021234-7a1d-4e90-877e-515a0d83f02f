<template>
    <div
        :class="[
            'banner-item relative cursor-pointer max-lg:aspect-[306/94] lg:aspect-[1420/300]',
            dataItem.classes,
        ]"
        @click="gotoDetail(dataItem.url)"
    >
        <CommonResponsiveImage
            :src="dataItem?.media?.bgPc1x"
            :src-large-1x="dataItem?.media?.bgPc1x"
            :src-large-2x="dataItem?.media?.bgPc2x"
            :src-small-1x="dataItem?.media?.bgMb1x"
            :src-small-2x="dataItem?.media?.bgMb2x"
            :alt="dataItem.title"
            pictureClass="banner-item-img lg:aspect-[1420/300] max-lg:aspect-[306/94]"
        />
        <div class="banner-item-left-content">
            <CommonResponsiveImage
                :src="dataItem?.media?.textPc1x"
                :src-large-1x="dataItem?.media?.textPc1x"
                :src-large-2x="dataItem?.media?.textPc2x"
                :src-small-1x="dataItem?.media?.textMb1x"
                :src-small-2x="dataItem?.media?.textMb2x"
                :alt="dataItem.text"
                :pictureClass="dataItem?.classesText"
            />
        </div>
    </div>
</template>
<script setup>
defineProps({
    dataItem: {
        type: Object,
        default: () => {},
    },
    gotoDetail: {
        type: Function,
        default: () => {},
    },
})
</script>
<style scoped lang="scss">
.banner-item {
    position: relative;
    overflow: hidden;
    &-left-content {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        padding-left: 80px;
        z-index: 2;
        @media screen and (max-width: 767px) {
            padding-left: 24px;
        }
    }
}
</style>
