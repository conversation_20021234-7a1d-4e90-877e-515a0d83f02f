<template>
    <div class="flex items-center gap-2">
        <img v-if="icon" :src="icon" :alt="`icon ${title}`" class="h-[30px] w-[30px]" />
        <h2 class="text-sm font-medium capitalize text-white lg:text-lg">
            {{ $t(title) }}
        </h2>
    </div>
</template>

<script setup>
defineProps({
    title: {
        type: String,
        default: 'title',
    },
    icon: {
        type: String,
        default: '',
    },
})
</script>
