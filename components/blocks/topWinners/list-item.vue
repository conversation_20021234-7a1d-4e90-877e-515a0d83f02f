<template>
    <div class="top-win-list flex flex-col gap-1.5">
        <div
            class="flex h-[30px] items-center justify-around gap-2 rounded bg-slate-800 pl-[10px] text-xs font-semibold uppercase text-white xl:gap-1.5"
        >
            <div class="w-[36px] shrink-0">
                {{ $t('home.top_winners.top') }}
            </div>
            <div class="grow">
                {{ $t('home.top_winners.game') }}
            </div>
            <div class="w-[90px] shrink-0">
                {{ $t('home.top_winners.bonus') }}
            </div>
        </div>
        <div
            role="button"
            class="top-item cursor-pointer overflow-hidden rounded py-[6px] pl-3 pr-[14px] text-xs"
            v-for="(item, index) in data.slice(0, 7)"
            :key="item.id"
            @click="handleGetGameUrl(item)"
        >
            <div class="flex items-center gap-2">
                <div
                    class="top-item__index w-[36px] shrink-0 text-sm font-extrabold italic text-white"
                >
                    {{ index + 4 }}
                </div>
                <div class="top-item__info flex grow items-center gap-1">
                    <div class="top-item__title line-clamp-1 font-medium capitalize text-white">
                        {{ item.username }}
                    </div>
                </div>

                <div
                    class="top-item__amount flex min-w-[100px] shrink-0 items-center gap-1 rounded-full bg-[rgba(255,255,255,0.1)] px-[5px] py-[3px] font-medium text-white"
                >
                    <img
                        :src="'/assets/images/game/currency.svg'"
                        alt="icon star"
                        class="size-3.5 object-contain"
                        loading="lazy"
                    />
                    {{ NumberUtils.formatAmount(Math.round(item.winlost / 1000)) }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useGamesStore } from '~/stores'

const { data } = defineProps(['data'])
const useGamesStoreInstance = useGamesStore()
const { handleGetGameUrl } = useGamesStoreInstance
</script>
<style lang="scss" scoped>
.top-win-list > div:first-child {
    border-top: 0;
}
.top-item {
    background: url(/assets/images/v2/home/<USER>/ 100% 100%;
}
</style>
