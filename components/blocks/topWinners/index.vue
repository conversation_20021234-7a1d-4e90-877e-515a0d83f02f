<template>
    <div class="flex h-full w-full flex-col">
        <BlocksHeader
            v-if="!showMobile"
            title="home.top_winners.title"
            icon="/assets/images/v2/home/<USER>"
        />
        <div>
            <ul
                class="top__tab mb-1 grid w-full grid-cols-3 rounded-md bg-slate-800 p-1"
                id="myTab"
                data-tabs-toggle="#myTabContent"
                role="tablist"
            >
                <li v-for="(item, index) in tabItems" :key="index" role="presentation">
                    <button
                        class="top__tab-item flex w-full items-stretch justify-center rounded px-1 py-2 text-center text-xs font-medium capitalize text-white"
                        :id="`${item.id}-tab`"
                        :data-tabs-target="`#${item.id}`"
                        type="button"
                        role="tab"
                        :aria-controls="item.id"
                        :aria-selected="index === 0 ? true : false"
                    >
                        {{ $t(item.title) }}
                    </button>
                </li>
            </ul>

            <div
                ref="topWinnerRef"
                id="myTabContent"
                class="tab-content rounded-lg px-2.5 py-3 pt-2"
            >
                <div
                    v-for="(item, index) in tabItems"
                    :key="index"
                    :id="item.id"
                    :class="[
                        'pt-3',
                        {
                            hidden: index !== 0,
                        },
                    ]"
                    role="tabpanel"
                    :aria-labelledby="`${item.id}-tab`"
                >
                    <BlocksTopWinnersTopItem
                        v-if="topWinners[item.id]?.slice(0, 3)?.length > 2"
                        :data="topWinners[item.id]?.slice(0, 3)"
                    />
                    <BlocksTopWinnersListItem
                        v-if="topWinners[item.id]?.slice(3)?.length > 0"
                        :data="topWinners[item.id]?.slice(3)"
                    />
                    <BlocksTopWinnersDataUpdating v-else />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useTopWinnersStore } from '~/stores'
import { storeToRefs } from 'pinia'
import { nextTick } from 'vue'
import { initTabs } from 'flowbite'

const { showMobile } = useCommon()

const topWinnersStoreInstance = useTopWinnersStore()
const { topWinners } = storeToRefs(topWinnersStoreInstance)
const { getTopWinners } = topWinnersStoreInstance
const topWinnerRef = ref(null)

const tabItems = [
    {
        id: 'nearWin',
        title: 'home.daily',
    },
    {
        id: 'weekWin',
        title: 'home.weekly',
    },
    {
        id: 'monthWin',
        title: 'home.monthly',
    },
]

onMounted(async () => {
    await nextTick(async () => {
        initTabs()
    })
})
await useAsyncData('get-top-winners', async () => {
    await getTopWinners()
    return {
        topWinners: topWinners,
    }
})
</script>
<style lang="scss" scoped>
.top__tab-item:hover,
[aria-selected='true'] {
    background: linear-gradient(89.51deg, #4881d0 -5.71%, #1d3b64 130.24%);
    color: #fff;
}

.tab-content {
    background: linear-gradient(180deg, #142e52 -13.87%, #385e93 100%),
        radial-gradient(
            31.72% 13.71% at 93.75% 31.84%,
            rgba(69, 96, 133, 0.3) 0%,
            rgba(71, 98, 134, 0) 100%
        );
}
</style>
