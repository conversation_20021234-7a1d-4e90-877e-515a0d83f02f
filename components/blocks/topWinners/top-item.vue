<template>
    <div class="home-topwinner-item mb-3 flex justify-center gap-3 px-1 text-center">
        <div
            v-for="(item, index) in data"
            :key="index"
            role="button"
            :class="`top-item top-item--${index} cursor-pointer`"
            @click="handleGetGameUrl(item)"
        >
            <span class="top-item__title mx-auto line-clamp-1 max-w-[60px] pt-[64px]">
                {{ item.username }}
            </span>
            <div class="top-item__amount">
                <img :src="'/assets/images/game/currency.svg'" alt="icon star" loading="lazy" />
                <div class="text-[#27272A]">
                    {{ NumberUtils.formatAmount(Math.round(item.winlost / 1000)) }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useGamesStore } from '~/stores'

const props = defineProps({
    data: {
        type: Array,
    },
})
const data = toRef(props, 'data')

const gameStoreInstance = useGamesStore()
const { handleGetGameUrl } = gameStoreInstance
</script>
