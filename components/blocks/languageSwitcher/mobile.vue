<template>
    <div
        class="inline-flex cursor-pointer items-center rounded-full border border-[#FFEEC2] bg-[#FFF9EA] p-0.5 text-[#616161]"
    >
        <NuxtLink
            role="button"
            v-for="item in locales"
            :key="item.code"
            class="flex h-7 w-7 items-center justify-center rounded-full text-[11px] font-bold"
            :class="activeLang === item.code ? 'bg-bigg-yellow text-black' : ''"
            @click="selectLanguage(item.code)"
        >
            <div class="inline-flex items-center uppercase">
                {{ item.code }}
            </div>
        </NuxtLink>
    </div>
</template>
<script setup>
import { onBeforeMount } from 'vue'
const { locale, locales } = useI18n()
const activeLang = useCookie('lang', {
    default: () => 'vi',
    maxAge: 60 * 60 * 24 * 365,
})

const selectLanguage = payload => {
    activeLang.value = payload
    locale.value = payload
}
onBeforeMount(() => {
    locale.value = activeLang.value
})
</script>
