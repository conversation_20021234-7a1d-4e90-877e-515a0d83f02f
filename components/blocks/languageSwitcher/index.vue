<template>
    <div class="flex cursor-pointer items-center">
        <button
            type="button"
            data-dropdown-toggle="language-dropdown-menu"
            class="inline-flex items-center justify-center rounded-[50%] bg-[#F7F7F9] p-2.5 uppercase text-primary-400 ring-0"
            aria-label="language dropdown menu button"
            id="language-dropdown-button"
        >
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
            >
                <g clip-path="url(#clip0_4543_26397)">
                    <mask
                        id="mask0_4543_26397"
                        style="mask-type: luminance"
                        maskUnits="userSpaceOnUse"
                        x="0"
                        y="0"
                        width="24"
                        height="24"
                    >
                        <path d="M0 1.90735e-06H24V24H0V1.90735e-06Z" fill="white" />
                    </mask>
                    <g mask="url(#mask0_4543_26397)">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M0 12C0 18.6174 5.3826 24 12 24C18.6174 24 24 18.6174 24 12C24 5.3826 18.6174 1.90735e-06 12 1.90735e-06C5.3826 1.90735e-06 0 5.3826 0 12ZM12 22.5938C6.15925 22.5938 1.40625 17.8408 1.40625 12C1.40625 6.15925 6.15925 1.40625 12 1.40625C17.8408 1.40625 22.5937 6.15925 22.5937 12C22.5937 17.8408 17.8408 22.5938 12 22.5938Z"
                            fill="currentColor"
                        />
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M13.6104 21.8719C13.0163 22.3769 12.4657 22.5938 12 22.5938V24C12.9062 24 13.7681 23.5834 14.5211 22.9435C15.2773 22.3008 15.9685 21.397 16.5532 20.3197C17.7231 18.1639 18.5156 15.2232 18.5156 12C18.5156 8.77684 17.7231 5.83605 16.5532 3.68027C15.9685 2.60296 15.2773 1.69921 14.5211 1.05653C13.7681 0.416605 12.9062 1.90735e-06 12 1.90735e-06V1.40625C12.4657 1.40625 13.0163 1.62312 13.6104 2.12807C14.2013 2.63026 14.7917 3.38278 15.3172 4.35104C16.3675 6.28643 17.1094 8.99409 17.1094 12C17.1094 15.0059 16.3675 17.7136 15.3172 19.649C14.7917 20.6172 14.2013 21.3697 13.6104 21.8719Z"
                            fill="currentColor"
                        />
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M9.47892 22.9435C10.2319 23.5834 11.0938 24 12 24V22.5938C11.5343 22.5938 10.9837 22.3769 10.3896 21.8719C9.79872 21.3697 9.2083 20.6172 8.68281 19.649C7.63246 17.7136 6.89062 15.0059 6.89062 12C6.89062 8.99409 7.63246 6.28643 8.68281 4.35104C9.2083 3.38278 9.79872 2.63026 10.3896 2.12807C10.9837 1.62312 11.5343 1.40625 12 1.40625V1.90735e-06C11.0938 1.90735e-06 10.2319 0.416605 9.47892 1.05653C8.72274 1.69921 8.03152 2.60296 7.44685 3.68027C6.27689 5.83605 5.48438 8.77684 5.48438 12C5.48438 15.2232 6.27689 18.1639 7.44685 20.3197C8.03152 21.397 8.72274 22.3008 9.47892 22.9435Z"
                            fill="currentColor"
                        />
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M23.2969 11.2969H0.703125V12.7031H23.2969V11.2969Z"
                            fill="currentColor"
                        />
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M11.9999 5.90625C8.84432 5.90625 6.01855 5.00628 4.07317 3.75968L3.31445 4.94369C5.50063 6.3446 8.59293 7.3125 11.9999 7.3125C15.4069 7.3125 18.4993 6.34455 20.6855 4.94359L19.9268 3.75959C17.9814 5.00623 15.1555 5.90625 11.9999 5.90625Z"
                            fill="currentColor"
                        />
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M4.07319 20.2412C6.01861 18.9945 8.84442 18.0946 12.0001 18.0946C15.1556 18.0946 17.9814 18.9945 19.9268 20.2411L20.6855 19.0571C18.4993 17.6562 15.407 16.6883 12.0001 16.6883C8.59304 16.6883 5.50069 17.6562 3.31445 19.0572L4.07319 20.2412Z"
                            fill="currentColor"
                        />
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M12.7031 23.2969V0.703127H11.2969V23.2969H12.7031Z"
                            fill="currentColor"
                        />
                    </g>
                </g>
                <defs>
                    <clipPath id="clip0_4543_26397">
                        <rect width="24" height="24" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </button>
        <!-- Dropdown -->
        <div
            class="z-50 my-4 hidden list-none divide-y divide-gray-100 rounded-lg bg-white text-base shadow"
            id="language-dropdown-menu"
        >
            <ul class="py-2 font-medium">
                <li v-for="item in locales" :key="item.code">
                    <NuxtLink
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        role="menuitem"
                        @click="selectLanguage(item.code)"
                    >
                        <div class="inline-flex items-center">
                            {{ item.text }}
                        </div>
                    </NuxtLink>
                </li>
            </ul>
        </div>
    </div>
</template>
<script setup>
import { onBeforeMount, onMounted } from 'vue'
const { locale, locales } = useI18n()
import { Dropdown, initDropdowns } from 'flowbite'
const activeLang = useCookie('lang', {
    default: () => 'vi',
    maxAge: 60 * 60 * 24 * 365,
})
let dropdown

const selectLanguage = locale_code => {
    if (locale_code !== activeLang.value) {
        activeLang.value = locale_code
        locale.value = locale_code
    }

    if (dropdown) dropdown.hide()
}
onBeforeMount(() => {
    locale.value = activeLang.value
})

onMounted(() => {
    initDropdowns()

    const _targetEl = document.getElementById('language-dropdown-menu')
    const _triggerEl = document.getElementById('language-dropdown-button')

    dropdown = new Dropdown(_targetEl, _triggerEl)
})
</script>
