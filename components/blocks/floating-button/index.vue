<template>
    <div class="floating-button" :class="isShow ? 'right-0 z-[50]' : '-right-[179px] z-[30]'">
        <div ref="floatingButtonRef" @click="isShow = !isShow" class="floating-button__header">
            <div class="floating-button__title">
                {{ $t('floating_button.title') }}
            </div>
            <i class="icon-contact"></i>
        </div>
        <div class="floating-button__content">
            <ul class="floating-button__list">
                <li
                    v-for="(item, index) in socials"
                    :key="index"
                    class="floating-button__item"
                    @click="item.action"
                >
                    <img :src="item.icon" class="h-5 w-5" alt="" />
                    {{ $t(item.title) }}
                </li>
            </ul>
        </div>
    </div>
</template>
<script setup>
import { onClickOutside } from '@vueuse/core'

const router = useRouter()
const localePath = useLocalePath()

const isShow = ref(false)
const floatingButtonRef = ref(null)

const { MESSENEGER_LINK, TELEGRAM_OFFICIAL, TELEGRAM_CSKH } = useRuntimeConfig().public

const openNewTab = link => {
    const openNewTab = window.open('about:blank', '_blank')
    if (openNewTab) {
        openNewTab.location.href = link
    }
}

const socials = shallowRef([
    {
        title: 'footer.title.livechat',
        icon: '/assets/images/v2/home/<USER>',
        action: () => window.LiveChatWidget.call('maximize'),
    },
    {
        title: 'footer.title.telegram',
        icon: '/assets/images/v2/home/<USER>',
        action: () => openNewTab(TELEGRAM_CSKH),
    },
    {
        title: 'footer.title.telegram_official',
        icon: '/assets/images/v2/home/<USER>',
        action: () => openNewTab(TELEGRAM_OFFICIAL),
    },
    {
        title: 'footer.title.promotion',
        icon: '/assets/images/v2/home/<USER>',
        action: () => router.push(localePath('/khuyen-mai')),
    },
    {
        title: 'footer.title.messenger',
        icon: '/assets/images/v2/home/<USER>',
        action: () => openNewTab(MESSENEGER_LINK),
    },
])

onClickOutside(floatingButtonRef, () => {
    isShow.value = false
})
</script>
<style lang="scss" scoped>
.floating-button {
    @apply fixed transition-all ease-linear max-xl:bottom-[calc(50%_-_150px)] xl:bottom-[calc(50%_-_65px)];
}
.floating-button__header {
    writing-mode: vertical-lr;
    background: linear-gradient(18.46deg, #3467ad -36.26%, #142e52 102.29%);
    @apply absolute right-full top-0 flex h-[68%] -rotate-180 transform cursor-pointer items-center justify-center rounded-r-[6px] transition-all ease-linear max-xl:flex max-xl:h-[32px] max-xl:w-[32px] max-xl:items-center max-xl:justify-center xl:p-2;
}

.floating-button__title {
    @apply text-xs font-medium text-[#FFFFFF] max-xl:hidden;
}

.floating-button__content {
    @apply w-[179px] rounded-bl-[6px] bg-slate-900 p-3;
}

.floating-button__list {
    @apply flex flex-col gap-[2px];
}

.floating-button__item {
    @apply flex cursor-pointer items-center gap-2 rounded bg-slate-800 px-[6px] py-2 text-xs font-semibold text-white;
}

.icon-contact {
    @apply -rotate-180 text-[20px] text-[#fff] max-xl:pb-[2px] xl:mt-2;
}

body {
    &:has(.float-icon) {
        .floating-button {
            @apply max-xl:bottom-[140px];
        }
    }
}
</style>
