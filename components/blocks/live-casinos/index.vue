<template>
    <CommonBoxWrapperHomePage class="live-casinos container">
        <template #header>
            <BlocksHeader v-if="!showMobile" title="header.nav_menu.live_casino">
                <CommonButtonViewMore :link="`/casino`" />
            </BlocksHeader>
        </template>
        <div class="overflow-hidden">
            <div class="flex flex-col gap-2 lg:h-[312px] lg:flex-row lg:gap-4">
                <BlocksLiveCasinosItemVideo
                    v-for="(item, index) in filteredCasinoByVideo"
                    :key="`item-video-${index}`"
                    :item-data="item"
                    :handle-get-casino-url="handleShowGame"
                />
                <div v-if="filteredCasinoByImage.length" class="swiper-swapper">
                    <Swiper class="z-0 lg:!ml-0 lg:!p-0 lg:!py-0 lg:!pl-0" :slidesPerView="3.2">
                        <SwiperSlide
                            v-for="(item, index) in filteredCasinoByImage"
                            :key="`item-image-${index}`"
                            class="swiper-slide !w-[86px] cursor-pointer lg:!w-[210px]"
                        >
                            <BlocksLiveCasinosItemImage
                                :item-data="item"
                                :handle-get-casino-url="handleShowGame"
                            />
                        </SwiperSlide>
                    </Swiper>
                </div>
            </div>
        </div>
    </CommonBoxWrapperHomePage>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { useJackpotStore, useCasinoStore } from '~/stores'

const { showMobile } = useCommon()
const staticUrl = useRuntimeConfig().public.staticUrl

const jackpotStore = useJackpotStore()
const { getJackpots } = jackpotStore

const casinoStore = useCasinoStore()
const { getTopCasinos } = casinoStore
const { topCasinos } = storeToRefs(casinoStore)

const listCasino = computed(() => {
    return [
        {
            partner_provider: 'b52',
            partner_game_id: 'vgmn_108',
            name: 'Bầu Cua Sexy',
            type: 'video',
            link: '/casino/xocdia?partner=b52',
            title: 'casino.bau_cua',
            media: {
                bg: `${staticUrl}/assets/images/v2/live-casinos/girl-1.webp`,
                icon: `${staticUrl}/assets/images/v2/provider/b52.webp`,
            },
        },
        {
            partner_provider: 'go',
            partner_game_id: 'vgmn_110',
            type: 'image',
            link: '/casino/sicbo?partner=go',
            title: 'casino.tai_xiu',
            media: {
                bg: `${staticUrl}/assets/images/v2/live-casinos/girl-2.webp`,
                icon: `${staticUrl}/assets/images/v2/provider/go88.webp`,
            },
        },
        {
            partner_provider: 'rik',
            partner_game_id: 'vgmn_108',
            type: 'image',
            link: '/casino/sexy?partner=rik',
            title: 'casino.bau_cua',
            media: {
                bg: `${staticUrl}/assets/images/v2/live-casinos/girl-3.webp`,
                icon: `${staticUrl}/assets/images/v2/provider/rik.webp`,
            },
        },
        {
            partner_provider: 'b52',
            partner_game_id: 'vgmn_110',
            type: 'image',
            link: '/casino/sicbo?partner=b52',
            title: 'casino.tai_xiu-sexy',
            media: {
                bg: `${staticUrl}/assets/images/v2/live-casinos/girl-4.webp`,
                icon: `${staticUrl}/assets/images/v2/provider/b52.webp`,
            },
        },
        {
            partner_provider: 'rik',
            partner_game_id: 'vgmn_110',
            type: 'image',
            link: '/casino/poker',
            title: 'casino.xanh-chin',
            media: {
                bg: `${staticUrl}/assets/images/v2/live-casinos/girl-5.webp`,
                icon: `${staticUrl}/assets/images/v2/provider/rik.webp`,
            },
        },
    ]
})

const filteredCasinoByVideo = computed(() => {
    try {
        const matchedItems = listCasino.value.filter(item => item.type === 'video')
        return matchedItems.map(item => {
            const matchedCasino = topCasinos.value.find(
                itemCasino =>
                    item.partner_game_id === itemCasino.partner_game_id &&
                    item.partner_provider === itemCasino.partner_provider
            )
            if (matchedCasino) {
                item.is_favorite = matchedCasino.is_favorite
            }

            return item
        })
    } catch (error) {
        console.error(error)
    }
})

const filteredCasinoByImage = computed(() => {
    return listCasino.value.filter(item => item.type === 'image')
})

const { handleShowGame } = useCasino()

onMounted(() => {
    nextTick(async () => {
        await getTopCasinos()
        getJackpots()
    })
})

onUnmounted(() => {
    topCasinos.value = []
})
</script>
<style lang="scss" scoped>
.is-desktop .live-casinos {
    .swiper-swapper {
        width: calc(100% - 548px - 16px);
    }
}
.swiper-slide {
    @apply max-lg:ml-2 lg:ml-4;
    &:first-child {
        @apply ml-0;
    }
}
</style>
