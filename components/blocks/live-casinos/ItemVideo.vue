<template>
    <CommonErrorWrapper>
        <div
            :id="elementId"
            class="item-image relative h-full flex-shrink-0 cursor-pointer overflow-hidden rounded-2xl bg-cover lg:w-[548px]"
        >
            <img
                :src="`${staticUrl}${itemData?.media?.bg}`"
                :alt="itemData?.title"
                class="aspect-[auto_548/312]"
                width="548"
                height="312"
            />
            <div
                class="absolute left-0 top-0 z-10 h-full w-full"
                @click="handleGetCasinoUrl(itemData)"
            >
                <div class="absolute top-3 z-10 flex w-full justify-between px-2 lg:top-4 lg:px-4">
                    <div class="flex gap-2">
                        <span
                            v-if="supportVideo"
                            class="flex gap-[2px] rounded bg-[#FF0044E5] px-1 py-[2px] lg:gap-1 lg:px-2"
                        >
                            <IconsLive class-wrapper="w-3 h-3 lg:w-5 lg:h-5" />
                            <span class="text-[10px] uppercase leading-[14px] text-white lg:text-sm"
                                >LIVE</span
                            >
                        </span>
                        <span
                            v-if="usersLiveGame"
                            class="flex gap-[2px] rounded bg-[#9E9E9E4D] px-1 py-[2px] lg:gap-1 lg:px-2"
                        >
                            <IconsUsers class-wrapper=" w-3 h-3 lg:w-5 lg:h-5" />
                            <span
                                class="text-[10px] uppercase leading-[14px] text-white lg:text-sm"
                                >{{ usersLiveGame }}</span
                            >
                        </span>
                    </div>

                    <div class="flex items-center gap-2">
                        <span
                            v-if="jackpots[elementId]"
                            class="flex items-center gap-[2px] rounded-full bg-[#9E9E9E4D] px-1 py-[2px]"
                        >
                            <IconsCoin class-wrapper="w-3 h-3 lg:w-4 lg:h-4" />
                            <CommonAnimatedNumber
                                :animationDuration="5000"
                                :number="findJackpot({ partner_game_id: elementId })"
                                :previousNumber="
                                    Math.round(
                                        findJackpot({ partner_game_id: elementId }) * (3 / 5)
                                    )
                                "
                                class="text-[10px] font-medium leading-[14px] text-[#FFB631] lg:text-sm"
                            />
                        </span>
                        <ModulesGameFavoriteIcon :game="{ ...itemData }" type="hot_casinos" />
                    </div>
                </div>
                <div
                    class="absolute bottom-2 left-2 z-10 flex transform flex-col items-start gap-1 lg:bottom-4 lg:left-4 lg:gap-2"
                >
                    <img
                        :src="itemData?.media?.icon"
                        :alt="$t(itemData?.title)"
                        class="h-4 w-auto lg:h-8"
                    />
                    <h3
                        class="w-full text-sm font-semibold uppercase text-white lg:text-[28px] lg:font-bold lg:leading-[46px]"
                    >
                        {{ $t(itemData?.title) }}
                    </h3>
                </div>
                <div v-if="supportVideo" class="absolute bottom-4 right-4 z-10">
                    <span
                        :id="`soundon-${elementId}`"
                        class="button-sound icon-mute"
                        @click.stop="$setSoundOnEl(elementId)"
                    >
                        <img
                            :src="`${
                                useRuntimeConfig().public.staticUrl
                            }/assets/images/icons/icon-sound-on.svg`"
                            alt="sound-on"
                        />
                    </span>
                    <span
                        :id="`soundoff-${elementId}`"
                        class="button-sound"
                        style="display: none"
                        @click.stop="$setSoundOffEl(elementId)"
                    >
                        <img
                            :src="`${
                                useRuntimeConfig().public.staticUrl
                            }/assets/images/icons/icon-sound-on.svg`"
                            alt="sound-on"
                        />
                    </span>
                </div>
            </div>
        </div>
    </CommonErrorWrapper>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { useJackpotStore } from '~/stores'
import { tokenMapVideo } from '~/constants/video'

const staticUrl = useRuntimeConfig().public.staticUrl

const useJackpotStoreInstance = useJackpotStore()
const { jackpots, liveB52UserActive } = storeToRefs(useJackpotStoreInstance)

const { $verifyToken, $loadNanoPlayer, $setSoundOffEl, $setSoundOnEl } = useNuxtApp()
const { itemData } = defineProps({
    itemData: {
        type: Object,
        default: () => {},
    },
    handleGetCasinoUrl: {
        type: Function,
        default: () => {},
    },
})

const usersLiveGame = computed(() => {
    const game = liveB52UserActive.value.find(item => item.gameId === itemData?.partner_game_id)

    if (game) {
        return game.activeUsers
    }

    return 0
})

const elementId = ref(`${itemData?.partner_provider}_${itemData?.partner_game_id}`)

const { findJackpot } = useJackpot()

const supportVideo = ref(
    tokenMapVideo.find(
        item =>
            item.provider === itemData?.partner_provider &&
            item.gameId === itemData?.partner_game_id
    )
)

onMounted(async () => {
    try {
        if (typeof $loadNanoPlayer === 'function') {
            await $loadNanoPlayer()
        }
        if (
            window.NanoPlayer &&
            supportVideo.value?.tokenId &&
            elementId.value &&
            typeof $verifyToken === 'function'
        ) {
            const domain = window.location.hostname
            $verifyToken(
                elementId.value,
                supportVideo.value?.tokenId,
                supportVideo.value?.tokenkey,
                domain
            )
        }
    } catch (error) {
        console.log('watchEffect supportVideo', error)
    }
})
</script>
<style lang="scss" scoped>
.button-sound.icon-mute {
    position: relative;
    &::after {
        content: '';
        position: absolute;
        left: 0;
        top: 7px;
        width: 20px;
        height: 1px;
        background-color: #fff;
        transform: rotate(45deg);
    }
}
</style>
