<template>
    <CommonErrorWrapper>
        <div
            :id="elementId"
            class="item-image relative cursor-pointer"
            @click="handleGetCasinoUrl(itemData)"
        >
            <img
                :src="`${staticUrl}${itemData?.media?.bg}`"
                :alt="itemData?.title"
                class="aspect-[auto_210/312]"
                width="210"
                height="312"
            />
            <div
                class="absolute bottom-1 flex w-full -translate-y-1 transform flex-col items-center gap-1"
            >
                <img
                    :src="itemData?.media?.icon"
                    :alt="$t(itemData?.title)"
                    class="h-[14px] w-auto lg:h-8"
                />
                <h3
                    class="w-full text-center text-xs font-semibold uppercase leading-[18px] text-white lg:text-[28px] lg:font-bold lg:leading-[46px]"
                >
                    {{ $t(itemData?.title) }}
                </h3>
            </div>
        </div>
    </CommonErrorWrapper>
</template>

<script setup>
const { itemData } = defineProps({
    itemData: {
        type: Object,
        default: () => {},
    },
    handleGetCasinoUrl: {
        type: Function,
        default: () => {},
    },
})
const staticUrl = useRuntimeConfig().public.staticUrl
const elementId = ref(`${itemData?.partner_provider}_${itemData?.partner_game_id}`)
</script>
