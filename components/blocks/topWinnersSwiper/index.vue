<template>
    <div v-if="nearWinWithId.length" class="top-winner-swiper">
        <Swiper
            :modules="[SwiperNavigation]"
            slides-per-view="auto"
            :space-between="8"
            :navigation="{
                nextEl: '#next-nav-top-winner',
                prevEl: '#prev-nav-top-winner',
            }"
        >
            <SwiperSlide
                v-for="item in nearWinWithId"
                :key="item.partner_game_id"
                class="top-winner-item"
            >
                <BlocksTopWinnersSwiperCard :data="item" />
            </SwiperSlide>
            <button
                v-show="isSupportNav"
                id="prev-nav-top-winner"
                class="absolute left-0 top-0 z-1 flex h-full w-[50px] items-center justify-start pl-2"
                aria-label="Previous match"
            >
                <IconsArrow
                    fill-color="fill-slate-300"
                    class-wrapper="w-5 h-5 rotate-180 transform"
                />
            </button>
            <button
                v-show="isSupportNav"
                id="next-nav-top-winner"
                class="absolute right-0 top-0 z-1 flex h-full w-[50px] items-center justify-end pr-2"
                aria-label="Next match"
            >
                <IconsArrow fill-color="fill-slate-300" class-wrapper="w-5 h-5" />
            </button>
        </Swiper>
    </div>
</template>

<script setup>
import { useTopWinnersStore } from '~/stores'
import { storeToRefs } from 'pinia'

defineProps({
    isSupportNav: {
        type: Boolean,
        default: false,
    },
})

const topWinnersStoreInstance = useTopWinnersStore()

const { nearWinWithId } = storeToRefs(topWinnersStoreInstance)
const { getTopWinners } = topWinnersStoreInstance

await useAsyncData('get-top-winners', async () => {
    await getTopWinners()
    return {
        nearWinWithId: nearWinWithId,
    }
})
</script>

<style lang="scss" scoped>
.swiper-slide {
    width: 160px;
    margin-right: 8px;
}
#prev-nav-top-winner {
    background: linear-gradient(90deg, #142639 0%, rgba(20, 38, 57, 0) 96.2%);
}
#next-nav-top-winner {
    background: linear-gradient(90deg, rgba(20, 38, 57, 0) 0%, #142639 96.2%);
}
</style>
