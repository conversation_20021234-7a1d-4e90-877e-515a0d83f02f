<template>
    <NuxtLink
        role="button"
        class="item-card flex items-center gap-[10px] rounded-[10px] bg-slate-700 p-1"
        @click="handleShowGame(data)"
    >
        <nuxt-img
            :src="data.image"
            class="aspect-[150/102] h-[30px] flex-shrink-0 rounded-[6px] object-cover xl:aspect-[253/175] xl:h-[50px]"
            :alt="data.name"
            loading="lazy"
            :placeholder="'/assets/images/img-defaut.png'"
            onerror='this.onerror = null; this.src="/assets/images/img-defaut.png"; this.classList.add("object-cover");'
            quality="50"
            format="webp"
            sizes="(max-width: 600px) 100vw, 50vw"
        />

        <div class="w-full">
            <div class="text-xs font-medium capitalize text-white">
                {{ data.username }}
            </div>

            <div class="item-card__price text-sm font-bold text-green-400">
                {{ NumberUtils.formatNumberWithCommas(Math.round(data.winlost / 1000)) }}
                K
            </div>
        </div>
    </NuxtLink>
</template>
<script setup>
import { NumberUtils } from '~/utils'

defineProps({
    data: {
        type: Object,
    },
})

const { handleShowGame } = useGame()
</script>
