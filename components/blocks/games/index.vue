<template>
    <div :id="boxId">
        <BlocksHeader v-if="isShowHeader" :title="title" :icon="icon">
            <CommonButtonViewmore
                v-if="linkViewMore"
                :link="linkViewMore"
                title="common.show_more"
            />
            <div v-if="layoutType === 'slider'" class="items-center gap-2 lg:flex">
                <button
                    :id="`prev-nav-${boxId}`"
                    :class="[
                        'flex h-9 w-9 rotate-180 transform items-center justify-center rounded-[50%] bg-slate-800',
                    ]"
                    aria-label="Previous match"
                >
                    <IconsArrow fill-color="fill-slate-300" class-wrapper="w-5 h-5" />
                </button>
                <button
                    :id="`next-nav-${boxId}`"
                    :class="['flex h-9 w-9 items-center justify-center rounded-[50%] bg-slate-800']"
                    aria-label="Next match"
                >
                    <IconsArrow fill-color="fill-slate-300" class-wrapper="w-5 h-5" />
                </button>
            </div>
        </BlocksHeader>

        <CommonGameSkeleton v-if="gamesByFilter[boxId]?.isLoading" />
        <template v-else-if="gamesByFilter[boxId]?.data?.items?.length">
            <div
                v-if="layoutType === 'grid'"
                class="grid grid-cols-2 gap-x-[6px] gap-y-3 md:grid-cols-4 md:gap-x-4 md:gap-y-4"
            >
                <CommonGameCard
                    v-for="game in gamesByFilter[boxId]?.data?.items"
                    :key="game.partner_game_id"
                    :data="game"
                    :jackpotvalue="findJackpot(game)"
                    :show-favorite="true"
                    favoriteColor="fill-white"
                    titleClass="text-sm font-normal capitalize text-white xl:text-base"
                    providerClass="text-[10px] text-slate-300 max-xl:font-medium md:text-sm"
                    buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                    @click="handleShowGame(game)"
                />
            </div>
            <template v-if="layoutType === 'slider'">
                <Swiper
                    :modules="[SwiperNavigation]"
                    class="z-0 lg:!ml-0 lg:!p-0 lg:!py-0 lg:!pl-0"
                    auto-height
                    slides-per-view="auto"
                    :spaceBetween="16"
                    :breakpoints="{
                        1024: {
                            slidesPerView: 4,
                        },
                    }"
                    :navigation="{
                        nextEl: `#next-nav-${boxId}`,
                        prevEl: `#prev-nav-${boxId}`,
                    }"
                >
                    <SwiperSlide
                        v-for="game in gamesByFilter[boxId]?.data?.items"
                        :key="game.partner_game_id"
                    >
                        <CommonGameCard
                            :data="game"
                            :jackpotvalue="findJackpot(game)"
                            :show-favorite="true"
                            favoriteColor="fill-white"
                            :favoriteType="boxId"
                            titleClass="text-sm font-normal capitalize text-white xl:text-base"
                            providerClass="text-[10px] text-slate-300 max-xl:font-medium md:text-sm"
                            buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                            @click="handleShowGame(game)"
                        />
                    </SwiperSlide>
                </Swiper>
            </template>
        </template>
    </div>
</template>
<script setup>
import { useGamesStore } from '~/stores'
import { storeToRefs } from 'pinia'

const { boxId, gameType, limit, sort } = defineProps({
    boxId: {
        type: String,
        default: 'games',
    },
    title: {
        type: String,
        default: 'home.cardgame',
    },
    icon: {
        type: String,
        default: '',
    },
    linkViewMore: {
        type: String,
        default: '',
    },
    gameType: {
        type: String,
        default: 'game_cards',
    },
    limit: {
        type: Number,
        default: 4,
    },
    sort: {
        type: String,
        default: 'hot',
    },
    layoutType: {
        type: String,
        default: 'grid', // grid, slider
    },
    isShowHeader: {
        type: Boolean,
        default: true,
    },
})

const gameStoreInstance = useGamesStore()

const { getGamesByFilter } = gameStoreInstance
const { gamesByFilter } = storeToRefs(gameStoreInstance)

const { handleShowGame } = useGame()

const { findJackpot } = useJackpot()

await useAsyncData(boxId, async () => {
    await getGamesByFilter(boxId, {
        type: gameType,
        limit: limit,
        page: 1,
        sort: sort,
    })

    return {
        [boxId]: gamesByFilter[boxId],
    }
})
</script>
<style lang="scss" scoped>
.swiper-slide {
    @apply mr-4 w-[253px];
}
.swiper-button-disabled,
.swiper-button-disabled {
    cursor: not-allowed;
    opacity: 0.5;
}
</style>
