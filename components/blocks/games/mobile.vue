<template>
    <div class="relative flex h-full w-full flex-col xl:mb-[35px]">
        <div>
            <div class="grid w-full grid-cols-2 gap-2.5">
                <template
                    v-for="(hotGame, index) in hotGamesMobile.items"
                    :key="hotGame.partner_game_id"
                >
                    <CommonGameCard
                        v-if="index < 4"
                        class="hot-label"
                        :data="hotGame"
                        @click="handleGetGameUrl(hotGame)"
                    />
                </template>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useGamesStore } from '~/stores'
import { storeToRefs } from 'pinia'

const gameStoreInstance = useGamesStore()
const { hotGamesMobile } = storeToRefs(gameStoreInstance)
const { handleGetGameUrl } = gameStoreInstance
</script>
<style scoped>
.hot-label:before {
    @apply pointer-events-none absolute right-0 top-0 h-[17px] w-[47px] rounded-tr-md bg-[url(/assets/images/icons/ic-hot.svg)] bg-no-repeat content-[''];
}
</style>
