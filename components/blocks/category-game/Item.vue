<template>
    <div
        class="cagegory-game-item relative h-auto overflow-hidden rounded-md duration-200 hover:scale-105"
    >
        <NuxtLink :to="localePath(game?.link)" class="relative block">
            <img
                :src="`${staticUrl}${game?.thumb}`"
                class="aspect-[auto_271/160] object-cover"
                width="271"
                height="160"
                :alt="`thumb ${game?.title}`"
            />
            <span class="item-content block">
                <span class="item-desc">{{ $t(game?.desc) }}</span>
                <span class="item-title">
                    {{ $t(game?.title) }}
                </span>
            </span>
        </NuxtLink>
    </div>
</template>
<script setup>
const localePath = useLocalePath()
const staticUrl = useRuntimeConfig().public.staticUrl

defineProps({
    game: {
        type: Object,
        default: () => {},
    },
})
</script>
<style lang="scss" scoped>
.item-content {
    @apply absolute w-full left-0 bottom-0 px-4 pb-7;
}
.item-title {
    @apply block text-lg font-bold uppercase text-green-400;
}
.item-desc {
    @apply text-xs text-white;
}
</style>
