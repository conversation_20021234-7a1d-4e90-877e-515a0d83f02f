<template>
    <div class="item-style-2-mb flex h-full flex-col rounded">
        <div class="flex items-center justify-between">
            <div class="league-name">
                <span class="line-clamp-1">
                    {{ data?.league_name_text }}
                </span>
            </div>
            <div class="team-time flex items-center gap-1">
                <span class="team-time-t">{{ formatTime(data?.text_time) }}</span>
                <span class="team-time-d">{{ formatDate(data?.text_time) }}</span>
            </div>
        </div>
        <div class="team-wrapper my-2">
            <div class="team flex items-center justify-center gap-4">
                <div :class="CSS.teamName">
                    <p class="team__title">{{ hTeam.name }}</p>
                    <div class="team__logo">
                        <img :src="hTeam.flag_thumbnail" :alt="hTeam.name" loading="lazy" />
                    </div>
                </div>
                <span class="text-sm font-semibold text-rose-600"> VS </span>
                <div :class="CSS.teamName">
                    <div class="team__logo">
                        <img :src="aTeam.flag_thumbnail" :alt="aTeam.name" loading="lazy" />
                    </div>
                    <p class="team__title">{{ aTeam.name }}</p>
                </div>
            </div>
        </div>
        <div
            class="grid flex-1 grid-cols-4 grid-rows-3 items-center gap-1 px-2 text-xs font-medium"
        >
            <p class="rate_text !text-xs font-medium text-slate-300">1 x 2</p>
            <div :class="CSS.rateItem">
                <p :class="CSS.rateText">{{ $t('home.home') }}</p>
                <p class="text-right" :class="CSS.rateText">
                    {{ euroHTeam.odds }}
                </p>
            </div>
            <div :class="CSS.rateItem">
                <p :class="CSS.rateText">{{ $t('home.away') }}</p>
                <p class="text-right" :class="CSS.rateText">
                    {{ euroATeam.odds }}
                </p>
            </div>
            <div :class="CSS.rateItem">
                <p :class="CSS.rateText">{{ $t('home.draw') }}</p>
                <p class="text-right" :class="CSS.rateText">
                    {{ euroDraw.odds }}
                </p>
            </div>
            <p class="rate_text !text-xs font-medium text-slate-300">Handicap</p>
            <div :class="CSS.rateItem">
                <p :class="['text-red-700', CSS.rateText]">{{ hpdHTeam.rate }}</p>
                <p :class="['text-right text-green-400', CSS.rateText]">
                    {{ hpdHTeam.odds }}
                </p>
            </div>
            <div :class="CSS.rateItem">
                <p :class="CSS.rateText">{{ hpdATeam.rate }}</p>
                <p :class="['text-right text-green-400', CSS.rateText]">
                    {{ ouHTeam.odds }}
                </p>
            </div>
            <button
                class="row-span-2 h-full justify-center rounded-lg !bg-rose-600 text-center text-xs font-medium text-white"
                :class="CSS.rateItem"
                @click="loadSportLink(data)"
            >
                {{ $t('common.bet_now') }}
            </button>
            <p class="rate_text !text-xs font-medium text-slate-300">O / U</p>
            <div :class="CSS.rateItem">
                <p :class="CSS.rateText">{{ ouHTeam.rate }}</p>
                <p class="text-right" :class="CSS.rateText">
                    {{ hpdATeam.odds }}
                </p>
            </div>
            <div :class="CSS.rateItem">
                <p :class="['text-red-700', CSS.rateText]">{{ ouATeam.rate }}</p>
                <p class="text-right" :class="CSS.rateText">
                    {{ ouATeam.odds }}
                </p>
            </div>
        </div>
    </div>
</template>

<script setup>
const { data } = defineProps(['data'])
const { loadSportLink } = useHotMatch()

const CSS = {
    teamName: 'flex items-center gap-1',
    rateRow: 'grid w-full grid-cols-3 gap-1 text-center',
    rateItem: 'flex items-center rounded bg-[rgba(0,0,0,.1)] py-[4px] px-[5px]',
    rateText: 'w-1/2 rate_text font-medium',
}

const DEFAULT_ODDS = '-'

const hTeam = computed(() => {
    return data && data.teams ? data.teams[0] : {}
})

const aTeam = computed(() => {
    return data && data.teams ? data.teams[1] : {}
})

const hpdATeam = computed(() => {
    return data && data.hdp && data.hdp.aTeam
        ? data.hdp.aTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const hpdHTeam = computed(() => {
    return data && data.hdp && data.hdp.hTeam
        ? data.hdp.hTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const ouATeam = computed(() => {
    return data && data.ou && data.ou.aTeam
        ? data.ou.aTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const ouHTeam = computed(() => {
    return data && data.ou && data.ou.hTeam
        ? data.ou.hTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const euroATeam = computed(() => {
    return data && data.euro && data.euro.aTeam
        ? data.euro.aTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const euroHTeam = computed(() => {
    return data && data.euro && data.euro.hTeam
        ? data.euro.hTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const euroDraw = computed(() => {
    return data && data.euro && data.euro.draw
        ? data.euro.draw
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const formatTime = date => {
    return convertTimeUTC(date).format('HH:mm')
}

const formatDate = date => {
    return convertTimeUTC(date).format('DD/MM')
}
</script>
