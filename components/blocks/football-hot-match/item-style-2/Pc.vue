<template>
    <div class="item-style-2-pc h-full overflow-hidden rounded-lg">
        <div class="item-header flex items-center">
            <span class="league-name">
                {{ data?.league_name_text }}
            </span>
            <div class="team-time flex items-center gap-1">
                <span class="team-time-t">{{ formatTime(data?.text_time) }}</span>
                <span class="team-time-d">{{ formatDate(data?.text_time) }}</span>
            </div>
        </div>
        <div class="flex items-center gap-2">
            <div class="w-[120px]">
                <div class="mb-[23px] ml-[10px] mt-2">
                    <div class="team">
                        <div class="mb-1 flex items-center gap-1">
                            <div class="team__logo">
                                <img :src="hTeam.flag_thumbnail" :alt="hTeam.name" loading="lazy" />
                            </div>
                            <p class="team__title">{{ hTeam.name }}</p>
                        </div>
                        <div class="flex items-center gap-1">
                            <div class="team__logo">
                                <img :src="aTeam.flag_thumbnail" :alt="aTeam.name" loading="lazy" />
                            </div>
                            <p class="team__title">{{ aTeam.name }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex-1 py-[13px] pr-2 text-xs font-medium text-neutral-400">
                <div :class="CSS.rateRow">
                    <div>Handicap</div>
                    <div>O / U</div>
                    <div>1 x 2</div>
                </div>

                <div class="my-1" :class="CSS.rateRow">
                    <div :class="CSS.rateItem">
                        <p :class="['text-red-700', CSS.rateText]">{{ hpdHTeam.rate }}</p>
                        <p :class="['text-green-400', CSS.rateText]">
                            {{ hpdHTeam.odds }}
                        </p>
                    </div>
                    <div :class="CSS.rateItem">
                        <p :class="CSS.rateText">{{ ouHTeam.rate }}</p>
                        <p :class="CSS.rateText">
                            {{ ouHTeam.odds }}
                        </p>
                    </div>
                    <div :class="CSS.rateItem">
                        <p :class="[CSS.rateText, 'text-left']">{{ $t('home.home') }}</p>
                        <p :class="CSS.rateText">
                            {{ euroHTeam.odds }}
                        </p>
                    </div>
                </div>

                <div class="my-1" :class="CSS.rateRow">
                    <div :class="CSS.rateItem">
                        <p :class="CSS.rateText">{{ hpdATeam.rate }}</p>
                        <p class="text-green-400" :class="CSS.rateText">
                            {{ hpdATeam.odds }}
                        </p>
                    </div>
                    <div :class="CSS.rateItem">
                        <p :class="['text-red-700', CSS.rateText]">{{ ouATeam.rate }}</p>
                        <p :class="CSS.rateText">
                            {{ ouATeam.odds }}
                        </p>
                    </div>
                    <div :class="CSS.rateItem">
                        <p :class="[CSS.rateText, 'text-left']">{{ $t('home.away') }}</p>
                        <p :class="CSS.rateText">
                            {{ euroATeam.odds }}
                        </p>
                    </div>
                </div>

                <div class="my-1" :class="CSS.rateRow">
                    <button
                        class="btn btn-primary btn-size-sm col-span-2 justify-center text-center"
                        :class="CSS.rateItem"
                        @click="loadSportLink(data)"
                    >
                        {{ $t('common.bet_now') }}
                    </button>
                    <div :class="CSS.rateItem">
                        <p :class="[CSS.rateText, 'text-left']">{{ $t('home.draw') }}</p>
                        <p :class="CSS.rateText">
                            {{ euroDraw.odds }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const { data } = defineProps(['data'])
const { loadSportLink } = useHotMatch()

const CSS = {
    rateRow: 'grid w-full grid-cols-3 gap-1 text-center text-[#B7BEC6]',
    rateItem:
        'rate-item flex items-center rounded bg-[rgba(0,0,0,.1)] py-[3px] px-[5px] text-white',
    rateText: 'w-1/2 text-[10px] font-medium',
}

const DEFAULT_ODDS = '-'

const hTeam = computed(() => {
    return data && data.teams ? data.teams[0] : {}
})

const aTeam = computed(() => {
    return data && data.teams ? data.teams[1] : {}
})

const hpdATeam = computed(() => {
    return data && data.hdp && data.hdp.aTeam
        ? data.hdp.aTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const hpdHTeam = computed(() => {
    return data && data.hdp && data.hdp.hTeam
        ? data.hdp.hTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const ouATeam = computed(() => {
    return data && data.ou && data.ou.aTeam
        ? data.ou.aTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const ouHTeam = computed(() => {
    return data && data.ou && data.ou.hTeam
        ? data.ou.hTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const euroATeam = computed(() => {
    return data && data.euro && data.euro.aTeam
        ? data.euro.aTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const euroHTeam = computed(() => {
    return data && data.euro && data.euro.hTeam
        ? data.euro.hTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const euroDraw = computed(() => {
    return data && data.euro && data.euro.draw
        ? data.euro.draw
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const formatTime = date => {
    return convertTimeUTC(date).format('HH:mm')
}

const formatDate = date => {
    return convertTimeUTC(date).format('DD/MM')
}
</script>
