<template>
    <div class="hidden items-center gap-2 lg:flex" :data-home="isHomePage">
        <button
            id="prev-hot-match"
            :class="[
                'flex rotate-180 transform items-center justify-center rounded-[50%] bg-slate-800',
                isHomePage
                    ? 'h-9 w-9'
                    : 'absolute left-0 top-1/2 z-1 h-7 w-7 -translate-x-1/2 -translate-y-1/2 transform',
            ]"
            aria-label="Previous match"
        >
            <IconsArrow
                fill-color="fill-slate-300"
                :class-wrapper="isHomePage ? 'w-5 h-5' : 'w-4 h-4'"
            />
        </button>
        <button
            id="next-hot-match"
            :class="[
                'flex items-center justify-center rounded-[50%] bg-slate-800',
                isHomePage
                    ? 'h-9 w-9'
                    : 'absolute right-0 top-1/2 z-1 h-7 w-7 -translate-y-1/2 translate-x-1/2 transform',
            ]"
            aria-label="Next match"
        >
            <IconsArrow
                fill-color="fill-slate-300"
                :class-wrapper="isHomePage ? 'w-5 h-5' : 'w-4 h-4'"
            />
        </button>
    </div>
</template>
<script setup>
const route = useRoute()
const isHomePage = route.name === 'index'
</script>
<style lang="scss" scoped>
#prev-hot-match.swiper-button-disabled,
#next-hot-match.swiper-button-disabled {
    cursor: not-allowed;
    opacity: 0.5;
}
</style>
