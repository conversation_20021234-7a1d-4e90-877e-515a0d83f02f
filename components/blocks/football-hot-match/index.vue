<template>
    <div class="hot-matches relative w-full">
        <div>
            <BlocksFootballHotMatchHeader v-if="isHomePage" />

            <div class="relative">
                <BlocksFootballHotMatchWrapperLoader v-show="hotMatches.length == 0" />
                <Swiper
                    v-if="hotMatches.length > 0"
                    :class="[
                        'z-0 lg:!ml-0 lg:!p-0 lg:!py-0 lg:!pl-0',
                        isHomePage ? 'is-home-page' : '',
                    ]"
                    :modules="[SwiperScrollbar, SwiperNavigation]"
                    v-bind="configSwiper"
                >
                    <SwiperSlide v-for="(item, index) in hotMatches" :key="index">
                        <BlocksFootballHotMatchItemStyle1 v-if="isHomePage" :data="item" />
                        <BlocksFootballHotMatchItemStyle2 v-else :data="item" />
                    </SwiperSlide>
                </Swiper>
                <BlocksFootballHotMatchGroupNavBtn v-if="!isHomePage" />
                <div class="hot-matches-swiper-scrollbar lg:hidden"></div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useHotMatchesStore } from '~/stores'
import { storeToRefs } from 'pinia'

const route = useRoute()

const hotMatchesStoreInstance = useHotMatchesStore()
const { getHotMatches } = hotMatchesStoreInstance

const { hotMatches } = storeToRefs(hotMatchesStoreInstance)

const isHomePage = computed(() => {
    return route.name === 'index'
})

await useAsyncData('hot-matches', async () => {
    await getHotMatches()

    return {
        hotMatches,
    }
})

const configSwiper = computed(() => {
    return isHomePage.value
        ? {
              autoHeight: true,
              slidesPerView: 1,
              navigation: {
                  nextEl: `#next-hot-match`,
                  prevEl: `#prev-hot-match`,
              },
          }
        : {
              autoHeight: true,
              slidesPerView: 1,
              spaceBetween: 0,
              breakpoints: {
                  640: {
                      slidesPerView: 2.3,
                  },
                  1024: {
                      slidesPerView: 4,
                      spaceBetween: 16,
                  },
              },
              navigation: {
                  nextEl: `#next-hot-match`,
                  prevEl: `#prev-hot-match`,
              },
              scrollbar: {
                  clickable: true,
                  el: '.hot-matches-swiper-scrollbar',
              },
          }
})
</script>
<style lang="scss">
.hot-matches {
    .swiper-slide {
        width: 320px;
        margin-right: 16px;
        @media screen and (max-width: 640px) {
            margin-right: 0;
        }
    }

    .is-home-page {
        .swiper-slide {
            margin-right: 0;
        }
    }

    .team-time-t {
        background: linear-gradient(
            252.66deg,
            rgba(27, 104, 255, 0.6) 8.18%,
            rgba(48, 185, 255, 0.6) 63.48%,
            rgba(27, 104, 255, 0.6) 89.03%
        );
        @apply mb-0.5 rounded-full px-2 py-0.5 text-[10px] font-medium leading-3;
    }

    .item-style-1 {
        background: linear-gradient(124deg, #102747 45.35%, #1f4b87 93.83%);
        @apply cursor-pointer rounded-lg px-2.5 py-2;
        .item-header {
            @apply relative mb-2.5 pb-2.5 text-center text-xs text-white;
            &::before {
                @apply absolute bottom-0 left-0 h-[1px] w-full bg-[linear-gradient(90deg,rgba(223,227,236,0)_0%,#DFE3EC_50.5%,rgba(223,227,236,0)_99.5%)] content-[''];
            }
        }

        .league-name {
            @apply line-clamp-1 break-all capitalize;
        }

        .team-time {
            @apply order-2 flex shrink-0 flex-col px-3 text-center;
        }

        .team-time-d {
            @apply text-xs;
        }

        .team-name {
            @apply line-clamp-1 break-all text-xs font-semibold text-white;
        }

        .team-logo {
            @apply shrink-0;
        }
    }

    .item-style-2-pc {
        background: linear-gradient(124deg, #102747 45.35%, #1f4b87 93.83%);
        .item-header {
            background: linear-gradient(83.33deg, #385e93 1.02%, #1f395d 98.98%),
                radial-gradient(
                    69.94% 224.04% at 74.79% 36.54%,
                    rgba(0, 108, 255, 0.2) 0%,
                    rgba(0, 108, 255, 0) 100%
                );

            @apply px-[10px] py-1 text-xs text-white;
        }

        .league-name {
            @apply line-clamp-1 w-[250px];
        }

        .team__title {
            @apply line-clamp-1 flex-1 text-xs font-bold text-white;
        }

        .team__logo {
            @apply mb-1 size-[24px] rounded-full border border-solid border-[#EAEBEB] bg-white p-1;

            img {
                @apply size-full object-contain;
            }
        }
        .rate-item {
            @apply text-right;
        }
    }

    .item-style-2-mb {
        background: linear-gradient(124deg, #102747 45.35%, #1f4b87 93.83%);
        @apply px-[10px] pt-3 text-xs text-white max-lg:pb-[14px] lg:pb-[18px];
        .league-name {
            @apply line-clamp-1 w-[200px] text-xs;
        }

        .team-wrapper {
            @apply relative pb-2;
            &::before {
                @apply absolute bottom-0 left-0 h-[1px] w-full bg-[linear-gradient(90deg,rgba(223,227,236,0)_0%,#DFE3EC_50.5%,rgba(223,227,236,0)_99.5%)] opacity-60 content-[''];
            }
        }

        .team__title {
            @apply line-clamp-1 flex-1 font-bold;
        }

        .team__logo {
            @apply size-[24px] rounded-full border border-solid border-[#EAEBEB] bg-white p-1;

            img {
                @apply size-full object-contain;
            }
        }

        .rate_text {
            @apply text-[10px] leading-[16px];
        }
    }

    .hot-matches-swiper-scrollbar {
        @apply absolute -bottom-2 left-1/2 z-10 !h-1 w-[110px] -translate-x-1/2 rounded-full bg-white/20;
        .swiper-scrollbar-drag {
            @apply bg-rose-600;
        }
    }
}
</style>
