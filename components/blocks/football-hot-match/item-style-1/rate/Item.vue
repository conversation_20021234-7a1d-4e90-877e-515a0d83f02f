<template>
    <div
        :class="`flex h-6 items-center justify-between rounded bg-[#0000000D] px-3 text-xs ${data?.title?.toLowerCase()}`"
    >
        <span class="uppercase text-[#545454]">
            {{ data?.title }}
        </span>
        <span class="rate font-bold">
            {{ data?.value?.rate }}
        </span>
        <span class="odds font-bold">
            {{ data?.value?.odds }}
        </span>
    </div>
</template>

<script setup>
defineProps({
    data: {
        type: Object,
        default: () => {},
    },
})
</script>
<style lang="scss" scoped>
.h,
.o {
    .rate {
        color: #108429;
    }
}

.a,
.u {
    .odds {
        color: #ff6668;
    }
}
</style>
