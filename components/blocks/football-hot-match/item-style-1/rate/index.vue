<template>
    <div class="grid grid-cols-2 gap-2.5">
        <BlocksFootballHotMatchItemRateItem
            v-for="rate in gameRates"
            :key="rate.title"
            :data="rate"
        />
    </div>
</template>

<script setup>
const { itemData } = defineProps({
    itemData: {
        type: Object,
        default: () => {},
    },
})

const hdpHomeTeam = item => {
    return item
        ? {
              odds: item?.hdp?.hTeam?.odds ?? 0,
              rate: item?.hdp?.hTeam?.rate ?? 0,
          }
        : {
              odds: 0,
              rate: 0,
          }
}

const hdpAwayTeam = item => {
    return item
        ? {
              odds: item?.hdp?.aTeam?.odds ?? 0,
              rate: item?.hdp?.aTeam?.rate ?? 0,
          }
        : {
              odds: 0,
              rate: 0,
          }
}

const ouHomeTeam = item => {
    return item
        ? {
              odds: item?.ou?.hTeam?.odds ?? 0,
              rate: item?.ou?.hTeam?.rate ?? 0,
          }
        : {
              odds: 0,
              rate: 0,
          }
}

const ouAwayTeam = item => {
    return item
        ? {
              odds: item?.ou?.aTeam?.odds ?? 0,
              rate: item?.ou?.aTeam?.rate ?? 0,
          }
        : {
              odds: 0,
              rate: 0,
          }
}

const gameRates = computed(() => {
    return [
        {
            title: 'H',
            value: hdpHomeTeam(itemData),
        },
        {
            title: 'O',
            value: hdpAwayTeam(itemData),
        },
        {
            title: 'A',
            value: ouHomeTeam(itemData),
        },
        {
            title: 'U',
            value: ouAwayTeam(itemData),
        },
    ]
})
</script>
