<template>
    <div class="flex flex-col gap-1">
        <div v-for="(team, index) in data" :key="index" class="relative flex items-center gap-1">
            <span class="h-5 w-5 overflow-hidden rounded-full border border-[#eee]">
                <img
                    :src="team?.flag_thumbnail"
                    :alt="team?.name"
                    class="aspect-[auto_1/1]"
                    width="20"
                    height="20"
                    onerror='this.onerror = null; this.src="/assets/images/v2/hot-match/fc_sample.jpg";'
                    loading="lazy"
                />
            </span>

            <span class="text-xs font-semibold text-grey-900">{{ team?.name }}</span>
        </div>
    </div>
</template>

<script setup>
defineProps({
    data: {
        type: Array,
        default: () => [],
    },
})
</script>
