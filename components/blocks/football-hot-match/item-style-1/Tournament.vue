<template>
    <div class="flex items-start justify-between text-xxs text-grey-900">
        <div class="h-7 flex-1 pr-2 text-xxs text-grey-900">
            {{ data?.name }}
        </div>
        <div class="flex-shrink-0 whitespace-nowrap">
            {{ formatDate(data?.time) }}
        </div>
    </div>
</template>

<script setup>
import dayjs from 'dayjs'

const formatDate = date => {
    return dayjs(date).format('HH:mm - DD/MM/YYYY')
}

defineProps({
    data: {
        type: Object,
        default: () => {},
    },
})
</script>
