<template>
    <div class="item-style-1" @click="loadSportLink(data)">
        <div class="item-header">
            <span class="league-name">{{ data?.league_name_text }}</span>
        </div>

        <div class="item__teams mb-[10px] flex items-center text-xs text-[#FBFDFF]">
            <div
                v-for="(team, index) in data.teams"
                :key="`team-${index}`"
                class="team flex w-1/2 items-center gap-1"
                :class="index === 0 ? 'home-team order-1 justify-end' : 'away-team order-3'"
            >
                <div class="team-logo" :class="index === 0 ? 'order-2' : ''">
                    <img :src="team.flag_thumbnail" :alt="team.name" class="h-6 w-6 rounded-full" />
                </div>
                <div class="team-name">
                    {{ team.name }}
                </div>
            </div>
            <div class="team-time">
                <span class="team-time-t">{{ formatTime(data?.text_time) }}</span>
                <span class="team-time-d">{{ formatDate(data?.text_time) }}</span>
            </div>
        </div>

        <div class="grid grid-cols-2 gap-3 text-xs font-medium text-white">
            <div>
                <div class="mb-[2px] text-center text-slate-300">HDP</div>
                <div class="mb-1 flex justify-between rounded bg-[rgba(0,0,0,0.06)] px-[10px] py-1">
                    <div class="w-[20px] font-bold">H</div>
                    <div class="w-[30px] text-center">{{ hpdHTeam.rate }}</div>
                    <div class="w-[30px] text-right text-[#00CAB0]">
                        {{ hpdHTeam.odds }}
                    </div>
                </div>
                <div class="flex justify-between rounded bg-[rgba(0,0,0,0.06)] px-[10px] py-1">
                    <div class="w-[20px] font-bold">A</div>
                    <div class="w-[30px] text-center">
                        {{ hpdATeam.rate }}
                    </div>
                    <div class="w-[30px] text-right text-[#60BFFF]">
                        {{ hpdATeam.odds }}
                    </div>
                </div>
            </div>
            <div>
                <div class="mb-[2px] text-center text-slate-300">O / U</div>
                <div class="mb-1 flex justify-between rounded bg-[rgba(0,0,0,0.06)] px-[10px] py-1">
                    <div class="w-[20px] font-bold">O</div>
                    <div class="w-[30px] text-center">
                        {{ ouHTeam.rate }}
                    </div>
                    <div class="w-[30px] text-right text-[#60BFFF]">
                        {{ ouHTeam.odds }}
                    </div>
                </div>
                <div class="flex justify-between rounded bg-[rgba(0,0,0,0.06)] px-[10px] py-1">
                    <div class="w-[20px] font-bold">U</div>
                    <div class="w-[30px] text-center">
                        {{ ouATeam.rate }}
                    </div>
                    <div class="w-[30px] text-right text-[#00CAB0]">
                        {{ ouATeam.odds }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { computed } from 'vue'
const { data } = defineProps(['data'])

const { loadSportLink } = useHotMatch()

// TODO: update sports
const DEFAULT_ODDS = '-'

const hpdATeam = computed(() => {
    return data && data.hdp && data.hdp.aTeam
        ? data.hdp.aTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const hpdHTeam = computed(() => {
    return data && data.hdp && data.hdp.hTeam
        ? data.hdp.hTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const ouATeam = computed(() => {
    return data && data.ou && data.ou.aTeam
        ? data.ou.aTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const ouHTeam = computed(() => {
    return data && data.ou && data.ou.hTeam
        ? data.ou.hTeam
        : { rate: DEFAULT_ODDS, odds: DEFAULT_ODDS }
})

const formatTime = date => {
    return convertTimeUTC(date).format('HH:mm')
}

const formatDate = date => {
    return convertTimeUTC(date).format('DD/MM')
}
</script>
<style lang="scss" scoped></style>
