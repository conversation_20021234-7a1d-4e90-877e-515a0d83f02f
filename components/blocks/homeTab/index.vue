<template>
    <div class="home-tab w-full xl:hidden">
        <div class="home-tab__header mb-4 grid grid-cols-3 gap-2">
            <div
                class="home-tab__item"
                v-for="item in tab"
                :key="item.id"
                @click="currentTab = item.id"
                :class="{ active: currentTab === item.id }"
            >
                <img
                    :src="item.thumb"
                    :alt="item.name"
                    width="32"
                    height="32"
                    class="relative mx-auto -mt-4 mb-[2px]"
                />
                <span class="capitalize">{{ item.name }}</span>
            </div>
        </div>
        <div class="home-tab__content" :data-content="currentTabContent?.name">
            <keep-alive>
                <component :is="currentTabContent?.component" />
            </keep-alive>
        </div>
    </div>
</template>
<script setup>
const currentTab = ref(1)
const tab = [
    {
        id: 1,
        component: resolveComponent('BlocksHomeTabGameHot'),
        name: 'Game Hot',
        thumb: '/assets/images/v2/home/<USER>',
    },
    {
        id: 2,
        component: resolveComponent('BlocksHomeTabPlayMore'),
        name: 'Chơi Nhiều',
        thumb: '/assets/images/v2/home/<USER>',
    },
    {
        id: 3,
        component: resolveComponent('BlocksHomeTabTopWinner'),
        name: 'Top Winner',
        thumb: '/assets/images/v2/home/<USER>',
    },
]

const currentTabContent = computed(() => {
    return tab.find(item => item.id === currentTab.value)
})
</script>
