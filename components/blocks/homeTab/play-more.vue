<template>
    <div class="hotgame-tab-mb relative flex h-full w-full flex-col xl:mb-[35px]">
        <div>
            <div
                class="hotgame__content flex flex-wrap gap-x-[6px] gap-y-[10px]"
                v-if="hotGames?.length"
            >
                <CommonGameCard
                    v-for="(hotGame, index) in hotGames"
                    :key="hotGame.partner_game_id"
                    :data="hotGame"
                    :jackpotvalue="findJackpot(hotGame)"
                    @click="handleShowGame(hotGame)"
                    :show-favorite="true"
                    itemClass="game-card"
                    :imageClass="`game-card__thumb min-w-full${
                        index === 0 ? ' aspect-[16/9]' : ''
                    }`"
                    favoriteColor="fill-white"
                    titleClass="text-sm font-normal capitalize text-white xl:text-base"
                    providerClass="text-[10px] text-slate-300 max-xl:font-medium lg:text-sm"
                    buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                />
                <NuxtLink to="/user/deposit">
                    <img
                        :src="'/assets/images/v2/home/<USER>'"
                        alt="Banner"
                        class="rounded-lg"
                    />
                </NuxtLink>
            </div>
        </div>
    </div>
</template>
<script setup>
import { STATUS_OK } from '~/constants/apiStatus'
import { useGameService } from '~/services'

const gameService = useGameService()

const hotGames = ref([])

const { handleShowGame } = useGame()
const { findJackpot } = useJackpot()

onMounted(async () => {
    await nextTick(async () => {
        try {
            const { data } = await gameService.getGames({
                sort: 'hot',
                limit: 19,
            })

            if (data.value?.status === STATUS_OK) {
                hotGames.value = data.value.data.items
            }
        } catch (error) {
            console.log('fet get hot games error', error)
        }
    })
})
</script>
