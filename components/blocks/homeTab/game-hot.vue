<template>
    <div class="hotgame-tab-mb relative flex h-full w-full flex-col xl:mb-[35px]">
        <div>
            <div class="hotgame__content flex flex-wrap gap-x-[6px] gap-y-[10px]">
                <div
                    v-for="(hotGame, index) in gamesByFilter[storeFilterName]?.data?.items"
                    :key="hotGame.partner_game_id"
                    ref="hotgameRef"
                    class="game-card first:max-lg:aspect-[306/164]"
                >
                    <CommonCasinoCard
                        v-if="index === 0 && hotGame.api_url.includes('casinoUrl')"
                        :data="{
                            ...hotGame,
                            label: 'live',
                        }"
                        :showVoiceButton="true"
                        :ratio="'first:max-lg:aspect-[346/194.63]'"
                        @click="handleShowGameCasino(hotGame)"
                        :show-favorite="true"
                        isIgnoreProvider
                        :imageClass="`game-card__thumb aspect-[306/164] min-w-full`"
                        isIgnoreLazy
                        :isInlineInfo="true"
                        :userActive="
                            userActive.find(item => item.gameId === hotGame.partner_game_id)
                                ?.activeUsers
                        "
                        :jackpotvalue="
                            jackpots[hotGame.partner_provider + '_' + hotGame.partner_game_id]
                        "
                        videoClass="aspect-[306/164]"
                        favoriteColor="fill-white"
                        titleClass="text-sm font-normal capitalize text-white xl:text-base"
                        providerClass="text-[10px] text-slate-300 max-xl:font-medium lg:text-sm"
                        buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                    />

                    <CommonCasinoCard
                        v-else-if="hotGame.api_url.includes('casinoUrl')"
                        :data="{
                            ...hotGame,
                        }"
                        @click="handleShowGameCasino(hotGame)"
                        :show-favorite="true"
                        :showVoiceButton="true"
                        :imageClass="`game-card__thumb aspect-[150/102] min-w-full`"
                        :jackpotvalue="
                            jackpots[hotGame.partner_provider + '_' + hotGame.partner_game_id]
                        "
                        :userActive="
                            userActive.find(item => item.gameId === hotGame.partner_game_id)
                                ?.activeUsers
                        "
                        favoriteColor="fill-white"
                        titleClass="text-sm font-normal capitalize text-white xl:text-base"
                        providerClass="text-[10px] text-slate-300 max-xl:font-medium lg:text-sm"
                        buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                    />

                    <CommonGameCard
                        v-else
                        :data="{
                            ...hotGame,
                        }"
                        :jackpotvalue="findJackpot(hotGame)"
                        isIgnoreProvider
                        @click="handleShowGame(hotGame)"
                        :show-favorite="true"
                        :imageClass="`game-card__thumb aspect-[150/102] min-w-full`"
                        favoriteColor="fill-white"
                        titleClass="text-sm font-normal capitalize text-white xl:text-base"
                        providerClass="text-[10px] text-slate-300 max-xl:font-medium lg:text-sm"
                        buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                    />
                </div>
                <NuxtLink to="/user/deposit">
                    <img
                        :src="'/assets/images/v2/home/<USER>'"
                        alt="Banner"
                        class="rounded-lg"
                    />
                </NuxtLink>
            </div>
        </div>
    </div>
</template>
<script setup>
import { useJackpotStore, useGamesStore } from '~/stores'
import { storeToRefs } from 'pinia'

const storeFilterName = 'featureGames'

const gameStoreInstance = useGamesStore()

const { getGamesByFilter } = gameStoreInstance
const { gamesByFilter } = storeToRefs(gameStoreInstance)

const hotgameRef = ref([])

await useAsyncData(storeFilterName, async () => {
    await getGamesByFilter(
        storeFilterName,
        {
            sort: 'feature',
            limit: 21,
        },
        true
    )
    return {
        featureGames: gamesByFilter[storeFilterName],
    }
})

const useJackpotStoreInstance = useJackpotStore()
const { jackpots, liveB52UserActive, liveGoUserActive, liveTechplayUserActive } =
    storeToRefs(useJackpotStoreInstance)

const userActive = computed(() => {
    return liveB52UserActive.value
        .concat(liveGoUserActive.value)
        .concat(liveTechplayUserActive.value)
})

const { handleShowGame } = useGame()
const { handleShowGame: handleShowGameCasino } = useCasino()
const { findJackpot } = useJackpot()
</script>
