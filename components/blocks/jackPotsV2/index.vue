<template>
    <div class="jackpot-v2 xl:pt-6">
        <BlocksHeader title="home.jackpot" icon="/assets/images/v2/home/<USER>">
            <CommonButtonViewmore link="/cong-game/no-hu" title="common.show_more" />
        </BlocksHeader>
        <div v-if="gamesByFilter[storeFilterName]?.data?.items?.length" class="relative pb-6">
            <Swiper
                :modules="[SwiperAutoplay, SwiperScrollbar]"
                :slides-per-view="1.92"
                :space-between="8"
                :loop="true"
                :autoplay="{
                    delay: 4000,
                    disableOnInteraction: false,
                }"
                :breakpoints="{
                    768: {
                        slidesPerView: 3,
                    },
                    1280: {
                        slidesPerView: 4,
                        spaceBetween: 12,
                    },
                }"
                :scrollbar="{
                    clickable: true,
                    el: '.jackpot-swiper-scrollbar',
                }"
                @slideChange="onChangeSlide"
            >
                <SwiperSlide
                    v-for="(hotGame, index) in gamesByFilter[storeFilterName]?.data?.items"
                    :key="hotGame.partner_game_id"
                >
                    <CommonGameCard
                        v-if="currentSlideActive + 3 >= index"
                        :data="hotGame"
                        :jackpotvalue="findJackpot(hotGame)"
                        @click="handleShowGame(hotGame)"
                        buttonPlayClass="rounded-xl bg-rose-600 px-4 xl:h-9"
                        image-class="game-card xl:w-[9.14vw] 2xl:w-[171px] 2xl:h-[118px] min-w-full"
                    />
                    <div v-else class="flex h-[6.3385vw] w-full items-center justify-center">
                        <CommonSpinner />
                    </div>
                </SwiperSlide>
            </Swiper>
            <div class="jackpot-swiper-scrollbar"></div>
        </div>
    </div>
</template>
<script setup>
import { useGamesStore } from '~/stores'
import { storeToRefs } from 'pinia'

const gameStoreInstance = useGamesStore()

const { getGamesByFilter } = gameStoreInstance
const { gamesByFilter } = storeToRefs(gameStoreInstance)
const currentSlideActive = ref(0)

const onChangeSlide = event => {
    currentSlideActive.value = event.realIndex
}
const { handleShowGame } = useGame()
const { findJackpot } = useJackpot()

const storeFilterName = 'gameJackpot'

await useAsyncData(storeFilterName, async () => {
    await getGamesByFilter(storeFilterName, {
        type: 'nohu',
        sort: 'hot',
    })

    return {
        gameJackpot: gamesByFilter[storeFilterName],
    }
})
</script>
<style lang="scss" scoped>
$viewPort: 1440;

@function pxToVW($size) {
    $vw: calc($viewPort * 0.01);
    @return calc($size / $vw) * 1vw;
}

@media (max-width: 1443px) {
    .game-card {
        height: pxToVW(122);
    }
}

.swiper-slide {
    margin-right: 12px;
    width: 171px;
}
:deep() {
    .swiper-scrollbar-horizontal {
        @apply absolute !left-1/2 bottom-0 z-10 h-[6px] w-[100px] -translate-x-1/2 rounded-full bg-[#F7F7F980]/50;
    }

    .swiper-scrollbar-drag {
        @apply bg-white;
    }
}
</style>
