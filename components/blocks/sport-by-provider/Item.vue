<template>
    <div class="relative cursor-pointer overflow-hidden rounded-[8px]">
        <div
            class="relative rounded-xl lg:transition-all lg:duration-500 lg:hover:-translate-y-[1.5%] lg:hover:scale-[1.01]"
            @click="handleRedireact(item)"
        >
            <img
                :src="showMobile ? item?.imgSrcMobile : item?.imgSrc"
                :alt="`${item?.key} thumb`"
            />
            <div
                class="absolute w-full max-lg:bottom-9 max-lg:left-3 lg:bottom-3 lg:right-7 lg:text-right"
            >
                <p
                    class="mb-1.5 hidden whitespace-nowrap rounded-full bg-green-400 px-[10px] py-[2px] text-xs text-gray-950 lg:inline-block"
                >
                    {{ item?.subTitle }}
                </p>
                <p class="whitespace-nowrap text-xl font-extrabold uppercase text-white">
                    {{ item?.title }}
                </p>
            </div>
            <span
                class="bg-position-center absolute bottom-0 left-0 flex h-[22px] w-[96px] items-center justify-start gap-[3px] bg-[url('/assets/images/v2/sports/bg-nav.webp')] bg-cover bg-no-repeat py-[3px] pl-3 lg:hidden"
            >
                <span class="text-[8px] font-semibold uppercase leading-[12px] text-white">{{
                    $t('common.bet_now')
                }}</span>
                <IconsArrowRight class-wrapper="w-[14px] h-[14px]" fill-color="fill-white" />
            </span>
        </div>
    </div>
</template>
<script setup>
defineProps({
    item: {
        type: Object,
        default: () => {},
    },
})
const localePath = useLocalePath()
const { showMobile } = useCommon()
const router = useRouter()
const useUserStoreInstance = useUserStore()
const { user, isLoggedIn } = storeToRefs(useUserStoreInstance)
const useModalStoreInstance = useModalStore()
const { showUpdateFullnameModal, showLoginModal } = storeToRefs(useModalStoreInstance)
const handleRedireact = item => {
    if (item?.isRequireLogin && !isLoggedIn.value) {
        showLoginModal.value = true
        return
    }

    if (user.value && user?.value?.is_updated_fullname === 0) {
        showUpdateFullnameModal.value = true
        return
    }

    router.push(localePath(item?.to))
}
</script>
