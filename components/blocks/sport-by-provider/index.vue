<template>
    <CommonBoxWrapperHomePage class="sport-by-provider">
        <template #header>
            <BlocksHeader v-if="!showMobile" :title="title" />
        </template>
        <div
            :class="{
                'flex flex-col gap-3': showMobile,
                'grid grid-cols-4 gap-4': !showMobile,
            }"
        >
            <div v-for="item in items" :key="item?.id">
                <BlocksSportByProviderItem :item="item" />
            </div>
        </div>
    </CommonBoxWrapperHomePage>
</template>
<script setup>
defineProps({
    title: {
        type: String,
        default: '',
    },
    items: {
        type: Array,
        default: () => [],
    },
})

const { showMobile } = useCommon()
</script>
