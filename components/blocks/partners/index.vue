<template>
    <CommonErrorWrapper>
        <div class="partners">
            <BlocksHeader title="home.partners" icon="/assets/images/v2/home/<USER>">
            </BlocksHeader>
            <div class="grid grid-cols-6 gap-5">
                <div
                    v-for="(partner, index) in partnersSorted"
                    :key="index"
                    class="partner-item flex h-[60px] items-center justify-center"
                    @click="clickProvider(partner)"
                    :data-order="partner.order"
                >
                    <img
                        :src="`${staticUrl}/assets/images/v2/providers/${partner.logo}?v=1`"
                        class="h-[60px] cursor-pointer"
                        :alt="partner.alt"
                    />
                </div>
            </div>
        </div>
    </CommonErrorWrapper>
</template>
<script setup>
import { useCommon } from '~/composables/use-common'
import { storeToRefs } from 'pinia'
import { useUserStore, useModalStore, useSportsStore } from '~/stores'
import { useNotify } from '~/composables/use-notify'
import { SPORT_API } from '~/constants/sport'

const staticUrl = useRuntimeConfig().public.staticUrl
const partners = ref([
    {
        order: 1,
        url: '/ksports',
        logo: 'k-sports.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'sport',
        api_url: SPORT_API.ksport,
    },
    {
        order: 34,
        url: '/casino/all?sort=all&partner=yeebet',
        logo: 'yeebet.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 13,
        url: '/casino/all?sort=all&partner=DreamGaming',
        logo: 'dreamgaming.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 12,
        url: '/casino/all?sort=all&partner=hogaming',
        logo: 'ho-gaming.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 29,
        url: '/casino/all?sort=all&partner=hogaming',
        logo: 'spinomenal.webp',
        alt: 'partner',
        loginRequired: false,
        key: 'spinomenal',
    },
    {
        order: 21,
        url: '/cong-game/all?sort=all&partner=netent',
        logo: 'netent.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 33,
        url: '/casino/all?sort=all&partner=rik',
        logo: 'rik.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    // {
    //     order: 35,
    //     url: '/cong-game/all?sort=all&partner=DragonSoft',
    //     logo: 'iwin.webp',
    //     alt: 'partner',
    //     loginRequired: false,
    //     key: 'dragon-soft',
    // },
    {
        order: 36,
        url: '/casino/all?sort=all&partner=viacasino',
        logo: 'via.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 37,
        url: '/cockfight/ga28',
        logo: 'ga28.webp',
        alt: 'partner',
        loginRequired: true,
        type: 'sport',
        api_url: SPORT_API.ga28,
    },
    {
        order: 2,
        url: '/ssports',
        logo: 'btisports.webp',
        alt: 'partner',
        loginRequired: true,
        type: 'sport',
        api_url: SPORT_API.btisport,
    },
    // {
    //     order: 11,
    //     url: '/ssports',
    //     logo: 'betradar.webp',
    //     alt: 'partner',
    //     loginRequired: true,
    //     type: 'sport',
    //     api_url: '/gameUrl?partnerProvider=bti',
    // },
    {
        order: 37,
        url: '/cockfight/ws168',
        logo: 'ws168.webp',
        alt: 'partner',
        loginRequired: true,
        type: 'sport',
        api_url: SPORT_API.ws168,
    },
    // {
    //     order: 16,
    //     url: '/ssports',
    //     logo: 'ibbet.webp',
    //     alt: 'partner',
    //     loginRequired: true,
    //     type: 'sport',
    //     api_url: '/gameUrl?partnerProvider=bti',
    // },
    {
        order: 17,
        url: '/im-play',
        logo: 'inplay.webp',
        alt: 'partner',
        loginRequired: true,
        type: 'sport',
        api_url: SPORT_API.implay,
    },
    {
        order: 3,
        url: '/casino/all?sort=all&partner=evo',
        logo: 'evolution.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 4,
        url: '/casino/all?sort=all&partner=pragmatic',
        logo: 'pragmatic.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 5,
        url: '/cong-game/all?sort=all&partner=evoplay',
        logo: 'evoplay.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 6,
        url: '/casino/all?sort=all&partner=vivo',
        logo: 'vivo.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 7,
        url: '/cong-game/all?sort=all&partner=playngo',
        logo: 'playngo.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 8,
        url: '/cong-game/all?sort=all&partner=microgaming',
        logo: 'mg.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 9,
        url: '/cong-game/all?sort=all&partner=vingame',
        logo: 'techplay.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 10,
        url: '/cong-game/all?sort=all&partner=jdb',
        logo: 'jdb.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 14,
        url: '/casino/all?sort=all&partner=ezugi',
        logo: 'ezugi.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 15,
        url: '/cong-game/all?sort=all&partner=fachai',
        logo: 'fachai.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    // {
    //     order: 39,
    //     url: '/im-sports',
    //     logo: 'db-gaming.webp',
    //     alt: 'partner',
    //     loginRequired: true,
    //     type: 'sport',
    //     api_url: '/gameUrl?partnerProvider=im&partnerGameId=esport',
    // },
    {
        order: 18,
        url: '/cong-game/all?sort=all&partner=jili',
        logo: 'jili.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 19,
        url: '/cong-game/all?sort=all&partner=KingMaker',
        logo: 'kingmaker.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 26,
        url: '/casino/all?sort=all&partner=sagaming',
        logo: 'sa-gaming.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    // {
    //     order: 20,
    //     url: '/cong-game/all?sort=all&partner=qtech',
    //     logo: 'qtech.webp',
    //     alt: 'partner',
    //     loginRequired: false,
    //     type: 'link'
    // },
    {
        order: 22,
        url: '/cong-game/all?sort=all&partner=redtiger',
        logo: 'redtiger.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 23,
        url: '/saba-sports',
        logo: 'saba-sports.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'sport',
        api_url: SPORT_API.sabasport,
    },
    {
        order: 25,
        url: '/cong-game/all?sort=all&partner=AskMeBet',
        logo: 'askmelotto.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 24,
        url: '/cong-game/all?sort=all&partner=spribe',
        logo: 'spribe.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 27,
        url: '/casino/all?sort=all&partner=ebet',
        logo: 'ebet.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 28,
        url: '/cong-game/all?sort=all&partner=habanero',
        logo: 'habanero.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 30,
        url: '/cong-game/all?sort=all&partner=tomhorn',
        logo: 'tomhorn.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 31,
        url: '/casino/all?sort=all&partner=b52',
        logo: 'b52.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
    {
        order: 32,
        url: '/casino/all?sort=all&partner=go',
        logo: 'go88.webp',
        alt: 'partner',
        loginRequired: false,
        type: 'link',
    },
])

const partnersSorted = computed(() => partners.value.sort((a, b) => a.order - b.order))

const useUserStoreInstance = useUserStore()

const { isLoggedIn } = storeToRefs(useUserStoreInstance)

const { openNewTab } = useCommon()
const useSportsStoreInstance = useSportsStore()
const { getSportUrl } = useSportsStoreInstance

const useModalStoreInstance = useModalStore()
const { showLoginModal } = storeToRefs(useModalStoreInstance)
const { showGameMaintenanceModal } = useNotify()

const clickProvider = item => {
    if (item.loginRequired && !isLoggedIn.value) {
        showLoginModal.value = true

        return
    }
    if (item.type === 'link') {
        if (item.isNewTab) {
            openNewTab(item.url)
        } else {
            navigateTo(item.url)
        }
        return
    }

    if (item.type === 'sport') {
        openNewTab('', async () => {
            try {
                const { data } = await getSportUrl(item.api_url)

                if (data?.status === 'OK' && data?.data?.url) {
                    if (import.meta.client) {
                        return data?.data?.url
                    }
                    return ''
                }
                showGameMaintenanceModal()
            } catch (error) {
                console.log(error)
                return ''
            }
        })
    }
}
</script>
<style lang="scss" scoped>
.partner-item {
    @apply rounded-lg bg-slate-900;
}
</style>
