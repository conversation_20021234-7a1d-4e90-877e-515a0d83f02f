<template>
    <div v-if="homePromotion.length" class="home-promotions">
        <BlocksHeader title="header.nav_menu.events_home" icon="/assets/images/v2/home/<USER>">
            <CommonButtonViewmore :link="`/khuyen-mai`" title="common.view_all" />
        </BlocksHeader>

        <Swiper
            class="z-0 lg:!ml-0 lg:!p-0 lg:!py-0 lg:!pl-0"
            auto-height
            :slides-per-view="1.2"
            :spaceBetween="8"
            :breakpoints="{
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 20,
                },
            }"
        >
            <SwiperSlide
                v-for="promotion in homePromotion"
                :key="promotion.id"
                class="swiper-slide cursor-pointe"
            >
                <BlocksPromotionItem :promotion="promotion" />
            </SwiperSlide>
        </Swiper>
    </div>
</template>

<script setup>
import { homePromotion } from '~/resources/promotion'
</script>
<style lang="scss" scoped>
.swiper-slide {
    @apply w-[340px] max-lg:mr-2 lg:mr-5;
}
</style>
