<template>
    <div class="item relative cursor-pointer overflow-hidden rounded-lg">
        <img
            :src="`${staticUrl}${promotion?.image}`"
            class="aspect-[auto_420/180] object-cover"
            alt="image"
            @click="$router.push(localePath(promotion?.link))"
        />
        <div
            class="absolute transform max-lg:left-2 max-lg:top-5 lg:left-3 lg:top-1/2 lg:-translate-y-1/2"
            @click="$router.push(localePath(promotion?.link))"
        >
            <h3 class="item-title">
                {{ $t(promotion?.title) }}
            </h3>
            <p class="item-text">
                {{ $t(promotion?.description) }}
            </p>
            <NuxtLink
                class="btn btn-primary btn-size-sm flex max-w-fit items-center gap-1 pl-3 pr-3"
                :to="promotion?.link"
            >
                {{ $t('common.show_more') }}
                <IconsChevronRight fill-color="fill-white" class-wrapper="w-4 h-4" />
            </NuxtLink>
        </div>
    </div>
</template>

<script setup>
defineProps({
    promotion: {
        type: Object,
        default: () => {},
    },
})
const localePath = useLocalePath()

const staticUrl = useRuntimeConfig().public.staticUrl
</script>

<style lang="scss" scoped>
.item-title {
    @apply mb-1 font-bold capitalize text-white max-lg:text-xs lg:text-base;
}
.item-text {
    @apply text-white max-lg:mb-1 max-lg:text-xxs lg:mb-3 lg:text-xs;
}
</style>
