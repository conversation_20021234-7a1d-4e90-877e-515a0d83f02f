<template>
    <div class="section-header">
        <div
            class="relative mb-6 flex h-[52px] items-center justify-items-stretch border-l-0 border-r-0 border-[#F7F7F9] px-3 md:border md:border-l md:border-r xl:mb-5 xl:hidden xl:border xl:px-2 xl:py-4"
        >
            <div class="justify-self-start">
                <slot name="back">
                    <button
                        class="absolute left-2 top-1/2 flex -translate-y-1/2 items-center justify-center text-[24px] text-[#fff]"
                        @click="navigateTo('/user')"
                    >
                        <i class="icon-left-chevron" />
                    </button>
                </slot>
            </div>
            <div
                class="section-header__title flex-auto text-center text-base font-bold leading-5 text-black"
            >
                <slot name="title" />
            </div>
            <div class="action absolute right-2 top-1/2 -translate-y-1/2 justify-self-end">
                <slot name="action" />
            </div>
        </div>
        <div class="hidden justify-between xl:flex">
            <div class="mb-2 text-[22px] font-bold capitalize leading-[27px] text-grey-900">
                <slot name="title" />
            </div>
            <div>
                <slot name="action" />
            </div>
        </div>
    </div>
</template>
<script></script>
<style lang="scss" scoped>
@media (max-width: 1023px) {
    .section-header {
        @apply sticky top-0 z-50 bg-rose-600;
        &__title {
            @apply text-white;
        }
        :deep(#filter-dropdown-button) {
            @apply text-[#fff];

            path {
                stroke: #fff;
            }
        }
    }
}
</style>
