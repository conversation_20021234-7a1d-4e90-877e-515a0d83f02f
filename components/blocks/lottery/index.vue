<template>
    <div class="game-lottery">
        <BlocksHeader title="home.lottery" icon="/assets/images/v2/home/<USER>">
            <CommonButtonViewmore :link="PAGE_URL.GAMES.LODE_ALL" title="common.show_more" />
        </BlocksHeader>
        <div class="grid grid-cols-4 gap-4">
            <nuxt-link
                v-for="item in items"
                :key="item.id"
                :to="item.link"
                class="hover-shine cursor-pointer overflow-hidden rounded-lg"
            >
                <div class="hover-shine__target">
                    <CommonImage
                        :src="item.image"
                        alt="Banner"
                        class="mx-auto h-[9.151vw] w-[13.177vw] min-w-full 2xl:h-[175px] 2xl:w-[253px]"
                    />
                </div>
            </nuxt-link>
        </div>
    </div>
</template>
<script setup lang="ts">
import { PAGE_URL } from '~/constants/page-urls'
const items = [
    {
        id: 2,
        image: '/assets/images/v2/lottery/lode-sieutoc.webp',
        link: PAGE_URL.LODE_SIEUTOC,
    },
    {
        id: 1,
        image: '/assets/images/v2/lottery/lode-3mien.webp',
        link: PAGE_URL.LODE_3MIEN,
    },
    {
        id: 4,
        image: '/assets/images/v2/lottery/keno-vietlott.webp',
        link: PAGE_URL.KENO.KENOVL,
    },
    {
        id: 3,
        image: '/assets/images/v2/lottery/keno-sieutoc.webp',
        link: PAGE_URL.KENO.KENOST,
    },
]
</script>
<style lang="scss" scoped></style>
