<template>
    <CommonNoData :title="title">
        <template #icon>
            <IconsLoadingSymbol v-if="didStartLoading" />
            <IconsEmptySymbol v-else-if="isNoDataSearch" />
            <IconsEmptyGame v-else-if="isFavorite" />
        </template>

        <i18n-t
            v-if="isFavorite && !isNoDataSearch && !didStartLoading"
            tag="p"
            keypath="favorite.favorite_empty_2"
            scope="global"
            class="-mt-5 inline-block text-center text-sm font-normal text-slate-300"
        >
            <template #icon>
                <IconsHeart classWrapper="w-6 h-6 ml-1 mr-1 !inline-block -mb-2" />
            </template>
        </i18n-t>
        <button
            class="btn btn-primary"
            @click="handleClick"
            v-if="!isNoDataSearch && !didStartLoading"
        >
            {{ $t(buttonText) }}
        </button>
    </CommonNoData>
</template>

<script setup>
import { useCasinoStore, useGamesStore } from '~/stores'

const route = useRoute()

const gamesStoreInstance = useGamesStore()
const {
    games,
    sort: gameSort,
    keyword: gameKeyword,
    isLoading: isLoadingGames,
    didStartLoading: didStartLoadingGames,
} = storeToRefs(gamesStoreInstance)

const casinoStoreInstance = useCasinoStore()
const {
    casinos,
    sort: casinoSort,
    keyword: casinoKeyword,
    isLoading: isLoadingCasino,
    didStartLoading: didStartLoadingCasino,
} = storeToRefs(casinoStoreInstance)

const isCasino = route.name.includes('casino')
const sort = computed(() => {
    if (isCasino) return casinoSort.value
    return gameSort.value
})
const isFavorite = computed(() => sort.value === 'favorite')
const isRecent = computed(() => sort.value === 'recent')
const didStartLoading = computed(() => didStartLoadingGames.value || didStartLoadingCasino.value)

const isNoDataSearch = computed(() => {
    if (isCasino) {
        return casinoKeyword.value && casinos.value.length == 0
    }
    return gameKeyword.value && games.value.length == 0
})

const title = computed(() => {
    if (didStartLoading.value) return 'favorite.data-loading'
    if (isNoDataSearch.value) return 'favorite.search-no-data'
    if (isFavorite.value) return 'favorite.favorite_empty'
    if (isRecent.value) return 'favorite.recently_played'
    return 'common.no_data'
})

const buttonText = computed(() => {
    if (isFavorite.value) return 'favorite.add_now'
    if (isRecent.value) return 'common.play'
    return 'common.play'
})

const handleClick = () => {
    if (isCasino) {
        if (isLoadingCasino.value) return
        casinoStoreInstance.$patch({
            type: 'all',
            sort: 'all',
            page: 1,
            partner: '',
            keyword: '',
        })
    } else {
        if (isLoadingGames.value) return
        gamesStoreInstance.$patch({
            type: 'all',
            sort: 'all',
            page: 1,
            partner: '',
            keyword: '',
        })
    }
}
</script>
