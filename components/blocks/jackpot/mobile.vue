<template>
    <div class="jackpot-mb relative overflow-hidden rounded-lg">
        <div class="mb-3 flex items-center gap-1">
            <img
                :src="`/assets/images/img/home/<USER>/icon-title.png`"
                class="h-[30px] w-[30px]"
                alt="icon"
                loading="lazy"
            />
            <div class="text-sm font-semibold text-[#17181A]">
                {{ $t('home.jackpot') }}
            </div>

            <NuxtLink class="ms-auto flex cursor-pointer items-center" to="/cong-game/slots">
                <div class="text-xs font-medium text-[#3E4049]">
                    {{ $t('home.seemore') }}
                </div>
                <img :src="'/assets/images/home/<USER>'" alt="seemore" class="h-4 w-4" />
            </NuxtLink>
        </div>
        <div
            class="jackpot__header flex items-center gap-2 rounded-t-lg bg-[#0A7655] px-5 pb-2 pt-3.5"
        >
            <span class="shrink-0 text-xs font-semibold text-[#FFFFFF]">
                {{ $t('home.jackpot_coming') }}
            </span>
            <div class="jackpot-number w-full text-center text-[18px] font-bold leading-[22px]">
                <CommonAnimatedNumber
                    v-if="jackpotNumber > 0"
                    :number="jackpotNumber"
                    :previousNumber="Math.round(jackpotNumber * (3 / 5))"
                />
                K
            </div>
        </div>
        <div class="jackpot__content h-[31.2vw] p-3 pr-0 xl:h-auto">
            <ClientOnly>
                <BlocksJackPotsV2 class="jackpot__slider" />
            </ClientOnly>
        </div>
        <div class="block-bottom h-[85px] rounded-b-lg p-4 pe-0 xl:h-auto">
            <ClientOnly>
                <BlocksTopWinnersSwiper />
            </ClientOnly>
        </div>
    </div>
</template>
<script setup>
import { useJackpotStore } from '~/stores'
import { storeToRefs } from 'pinia'

const useJackpotStoreInstance = useJackpotStore()
const { jackpots } = storeToRefs(useJackpotStoreInstance)
const jackpotNumber = computed(() => {
    return Object.values(jackpots.value || {}).reduce((acc, item) => acc + item, 0)
})
</script>
