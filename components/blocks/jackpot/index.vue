<template>
    <div class="jackpot-pc overflow-hidden rounded-xl">
        <div
            class="jackpot__header flex gap-6 bg-[url(/assets/images/v2/jackpot/bg.webp)] bg-cover bg-no-repeat pl-8 pr-6"
        >
            <nuxt-link
                to="/cong-game/no-hu"
                class="jackpot__banner relative flex w-[263px] shrink-0 items-end pt-5"
            >
                <CommonImage
                    :src="`/assets/images/v2/jackpot/image.webp`"
                    alt="jackpot"
                    class="h-[212px] w-[263px]"
                />
                <div
                    v-if="jackpotNumber > 0"
                    class="jackpot-number absolute bottom-[10px] left-0 flex w-full items-center justify-center text-center text-[25px] font-extrabold max-2xl:text-[22px]"
                >
                    <ClientOnly>
                        <CommonAnimatedNumber
                            :number="jackpotNumber"
                            :previousNumber="Math.round(jackpotNumber * (3 / 5))"
                            suffix="K"
                        />
                    </ClientOnly>
                </div>
            </nuxt-link>
            <BlocksJackPotsV2 class="jackpot__slider w-[calc(100%_-_263px_-_20px)]" />
        </div>
        <div class="block-top p-6">
            <BlocksTopWinnersSwiper />
        </div>
    </div>
</template>
<script setup>
import { useJackpotStore } from '~/stores'
import { storeToRefs } from 'pinia'

const useJackpotStoreInstance = useJackpotStore()
const { jackpots } = storeToRefs(useJackpotStoreInstance)

const jackpotNumber = computed(() => {
    return Object.values(jackpots.value || {}).reduce((acc, item) => acc + item, 0)
})
</script>
<style lang="scss" scoped>
.jackpot-pc {
    background: linear-gradient(
        89.2deg,
        #0e2470 6.64%,
        #1962e3 15.42%,
        #1448ab 33.36%,
        #0c1741 97.55%
    );
}
</style>
