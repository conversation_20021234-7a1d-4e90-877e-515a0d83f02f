<template>
    <div class="page-events__content">
        <div v-if="isLoading" class="text-center">
            <CommonSpinner />
        </div>
        <template v-else>
            <div
                v-if="data?.length"
                class="grid grid-cols-1 gap-x-2.5 gap-y-3 xl:grid-cols-4 xl:gap-x-5 xl:gap-y-8 xl:px-0"
            >
                <div v-for="(item, index) in data" :key="index">
                    <BlocksEventsCard :data="item" :isShowDescription="false" />
                </div>
            </div>
            <div v-else class="block-empty flex flex-col justify-center text-center lg:h-[446px]">
                <div class="mb-2">
                    <img
                        :src="'/assets/images/img/icons/events.svg'"
                        alt="empty-events"
                        class="mx-auto"
                    />
                </div>
                <p class="mb-1 text-sm text-white">
                    <template v-if="type === 'news'">
                        {{ $t('events.empty.news_title') }}
                    </template>
                    <template v-else>
                        {{ $t('events.empty.title') }}
                    </template>
                </p>
                <p class="text-xs text-slate-300">
                    <template v-if="type === 'news'">
                        {{ $t('events.empty.news_desc') }}
                    </template>
                    <template v-else>
                        {{ $t('events.empty.desc') }}
                    </template>
                </p>
            </div>
        </template>
    </div>
</template>
<script setup>
defineProps(['data', 'type', 'isLoading'])
</script>
<style lang="scss" scoped>
.page-events__content {
    @apply min-h-[270px];
    &:has(.block-empty) {
        @apply flex items-center justify-center;
    }
}
</style>
