<template>
    <div class="hover-shine">
        <div class="hover-shine__target">
            <nuxt-link :to="data.actionurl">
                <div class="absolute right-0 top-0 z-10">
                    <div class="tag-end" v-if="data.status === 'finish'"><PERSON><PERSON> kết thúc</div>
                </div>

                <CommonImage
                    :src="data.imgurl"
                    :src-mb="data.imgUrlMb"
                    :alt="data?.title"
                    :version="versionImage"
                    classWrapper="cursor-pointer rounded-lg "
                    class="aspect-[306/127] w-full rounded-lg object-cover xl:aspect-[165/110]"
                />
            </nuxt-link>
        </div>
        <nuxt-link
            :to="data.actionurl"
            class="my-1 block cursor-pointer text-base font-medium capitalize text-white max-xl:text-[14px] max-xl:font-semibold xl:my-3"
        >
            {{ data.title }}
        </nuxt-link>
        <div
            v-if="isShowDescription"
            class="line-clamp-2 text-sm font-normal text-slate-300 xl:line-clamp-3 xl:leading-5"
        >
            {{ data.description }}
        </div>
    </div>
</template>

<script setup>
defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    isShowDescription: {
        type: Boolean,
        default: true,
    },
})
const versionImage = '1.0.2'
</script>

<style lang="scss" scoped>
@font-face {
    font-family: 'SF-Pro';
    src: url('/assets/fonts/SF-Pro/bold.otf');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

.tag-end {
    font-family: 'SF-Pro';
    background: url('/assets/images/pages/events/tag-bg.png') no-repeat;
    @apply flex h-[24px] w-[122px] items-center justify-center pl-6 text-center text-sm font-bold uppercase text-white;

    @media (max-width: 1199px) {
        @apply h-[21px] w-[101px] pl-4 text-xs;
    }
}
</style>
