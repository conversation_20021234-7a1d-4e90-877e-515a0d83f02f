<template>
    <div class="amounts grid w-full gap-2 max-lg:grid-cols-3 lg:grid-cols-6">
        <div
            v-for="(item, index) in amountList"
            :key="`value${getItemValue(item)}`"
            :class="[
                'amount-item relative',
                {
                    'is-active':
                        `${index}_${getItemValue(item)}` === selectedAmount ||
                        getItemValue(item) === selectedAmount,
                },
            ]"
            @click="handleNumberInput(getItemValue(item), index)"
        >
            <Field
                :id="`value${getItemValue(item)}`"
                type="radio"
                :name="nameInput"
                :value="getItemValue(item)"
                class="peer hidden"
            />

            <label :for="`value${getItemValue(item)}`">
                {{ NumberUtils.formatNumberWithDots(getItemValue(item)) }} K
            </label>
            <CommonStatus
                v-if="typeof item === 'object' && item?.status"
                :status="item?.status"
                position="right-0 top-0"
                custom-style="!rounded-tl-none !rounded-tr-lg !rounded-bl-lg !rounded-br-none"
            />
        </div>
    </div>
</template>
<script setup>
import { Field } from 'vee-validate'
import { NumberUtils } from '~/utils'

defineProps({
    nameInput: {
        type: String,
        default: 'number_input',
    },
    amountList: {
        type: Array,
        required: true,
    },
    selectedAmount: {
        type: String,
        required: true,
    },
    handleNumberInput: {
        type: Function,
        required: true,
    },
})

const getItemValue = item => {
    if (typeof item === 'object') {
        return item.value
    }
    return item
}
</script>
<style scoped>
.amount-item {
    @apply w-full rounded-xl border border-slate-800 bg-slate-800;
}
.amount-item.is-active {
    @apply border-green-400;
}

.amount-item label {
    @apply flex w-full cursor-pointer justify-center py-[10px] font-medium text-white max-lg:text-xs lg:text-sm;
}

.amount-item.is-active label {
    @apply text-green-400;
}
</style>
