<template>
    <div class="relative">
        <label class="mb-2 block text-sm text-white">
            {{ $t('user.deposit.codepay.button_promo') }}
        </label>

        <div class="promotions grid grid-cols-2 gap-3">
            <div
                v-for="item in promotionPackagesFiltered"
                :key="item.id"
                :class="[
                    'promotion-item cursor-pointer text-center max-lg:py-2 lg:py-5',
                    {
                        'is-active': packageSelected === item?.id,
                    },
                ]"
                :data-id="item?.id"
                @click="onChangePackage(item.id)"
            >
                <p class="promotion-item-title">
                    {{ item.title }}
                </p>
            </div>
        </div>
        <div
            v-if="promotionExtraData && packageSelected === PROMOTION_PLAN_TYPE.WELCOME"
            class="promotion-metadata"
        >
            <div class="promotion-metadata-item">
                <p class="mb-1 text-[12px] leading-[18px] text-slate-300"><PERSON><PERSON><PERSON><PERSON><PERSON> mãi</p>
                <p class="text-[12px] font-medium leading-[18px] text-white">
                    {{ promotionAmount.toLocaleString() }} {{ $t('common.money_symbol') }}
                </p>
            </div>
            <div class="promotion-metadata-item">
                <p class="mb-1 text-[12px] leading-[18px] text-slate-300">Thực nhận</p>
                <p class="text-[12px] font-medium leading-[18px] text-white">
                    {{ receiveAmount.toLocaleString() }} {{ $t('common.money_symbol') }}
                </p>
            </div>
            <div class="promotion-metadata-item">
                <p class="mb-1 text-[12px] leading-[18px] text-slate-300">Số vòng cược</p>
                <p class="text-[12px] font-medium leading-[18px] text-white">
                    {{ promotionExtraData.multiplier }}
                </p>
            </div>
            <div class="promotion-metadata-item">
                <p class="mb-1 text-[12px] leading-[18px] text-slate-300">Tiền cược yêu cầu</p>
                <p class="text-[12px] font-medium leading-[18px] text-white">
                    {{ totalAmount.toLocaleString() }} {{ $t('common.money_symbol') }}
                </p>
            </div>
        </div>
    </div>
</template>
<script setup>
import { PROMOTION_PLAN_TYPE } from '~/constants/user'
import { promotionPackages } from '~/constants/promotion'
import { useUserStore } from '~/stores'

const { packageSelected, amountDeposit } = defineProps({
    packageSelected: {
        type: Number,
        required: true,
    },
    onChangePackage: {
        type: Function,
        required: true,
    },
    amountDeposit: {
        type: Number,
        required: true,
    },
})

const useUserStoreInstance = useUserStore()
const { user } = storeToRefs(useUserStoreInstance)

const promotionPackagesFiltered = computed(() => {
    if (!user.value?.package_id) {
        return promotionPackages
    }
    return promotionPackages.filter(item => item.id === PROMOTION_PLAN_TYPE.COMMISSION)
})

const promotionExtraData = computed(() => {
    return promotionPackagesFiltered.value?.find(item => item.id === packageSelected)
})

const promotionAmount = computed(() => {
    const amount = amountDeposit
    const esimateAmount = amount * promotionExtraData.value?.rate
    if (esimateAmount > promotionExtraData.value?.maxPromotionAmount) {
        return promotionExtraData.value?.maxPromotionAmount
    }
    return esimateAmount
})

const receiveAmount = computed(() => {
    return promotionAmount.value + amountDeposit
})

const totalAmount = computed(() => {
    const amount = amountDeposit
    const esimateAmount = amount * promotionExtraData.value?.rate
    if (esimateAmount > promotionExtraData.value?.maxPromotionAmount) {
        return (
            promotionExtraData.value?.maxPromotionAmount *
                2 *
                promotionExtraData.value?.multiplier +
            esimateAmount -
            promotionAmount.value
        )
    }
    return (promotionAmount.value + amount) * promotionExtraData.value?.multiplier
})

// watch(
//     () => packageSelected,
//     (value) => {
//         console.log('packageSelected in select', packageSelected)
//     },
//     {immediate: true}
// )
</script>
<style lang="scss" scoped>
.promotion-item {
    background: url('/assets/images/v2/promotions/promotion-unselect.png') 0 0 no-repeat;
    background-size: cover;
    @apply relative flex flex-col justify-center rounded-xl border border-transparent hover:border-green-400 max-lg:h-[56px] lg:h-[88px];
}

.promotion-item-title {
    @apply font-semibold uppercase text-white max-lg:mb-1 max-lg:text-[12px] max-lg:leading-[18px] lg:mb-[6px] lg:text-base;
}

.promotion-item-description {
    @apply text-slate-300 max-lg:text-[12px] max-lg:leading-[18px] lg:text-base;
}

.promotion-item.is-active {
    background-image: url('/assets/images/v2/promotions/promotion-selected.png');
    @apply hover:border-transparent max-lg:border-green-400;
    &::before {
        @apply absolute rounded-full border border-green-400 content-[''] max-lg:right-1 max-lg:top-1 max-lg:h-[10px] max-lg:w-[10px] lg:right-3 lg:top-3 lg:h-[14px] lg:w-[14px];
    }

    &::after {
        @apply absolute rounded-full bg-green-400 content-[''] max-lg:right-[6px] max-lg:top-[6px] max-lg:h-[6px] max-lg:w-[6px] lg:right-[14px] lg:top-[14px] lg:h-[10px] lg:w-[10px];
    }

    .promotion-item-title {
        @apply text-green-400;
    }
}

.promotion-metadata {
    @apply mt-2 rounded-lg bg-slate-800 py-[14px] max-lg:flex max-lg:flex-col max-lg:gap-2 lg:grid lg:grid-cols-4;
    &-item {
        @apply px-4 first-of-type:border-l-0 max-lg:flex max-lg:justify-between lg:border-l lg:border-l-slate-700;
    }
}
</style>
