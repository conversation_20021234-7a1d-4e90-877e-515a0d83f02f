<template>
    <HelpWrapper>
        <div class="term">
            <div class="section">
                <h1>Hướng Dẫn Đăng Ký Tài <PERSON>hoản Tại {{ brandName }}</h1>
                <p>
                    <strong>Bước 1</strong>: T<PERSON>y cập trang chủ {{ brandName }} → Nhấp chuột vào nút
                    đăng ký góc phải màn hình.
                </p>
                <img
                    class="hidden xl:block"
                    :src="`/assets/images/support/huong-dan-dang-ky-step1.webp?v=1`"
                />
                <img
                    class="mx-auto xl:hidden"
                    :src="`/assets/images/support/huong-dan-dang-ky-step1-1200.webp?v=1`"
                />
            </div>
            <div class="section">
                <p>
                    <strong>Bước 2</strong>: Quý Khách vui lòng điền đầy đủ thông tin theo yêu cầu
                    dưới đây
                </p>
                <ul>
                    <li class="mb-3">
                        <span>Tên đăng nhập:</span> <PERSON>h<PERSON>ng được ít hơn 6 ký tự, vi<PERSON><PERSON> li<PERSON>, không dấu,
                        không có khoảng trắng.
                    </li>
                    <li class="mb-3">
                        <span>Mật khẩu:</span> Không được ít hơn 6 ký tự, viết liền không dấu.
                    </li>
                    <li class="mb-3">
                        <span>Số điện thoại:</span> Vui lòng nhập số điện thoại Quý Khách đang sử
                        dụng.
                    </li>
                </ul>
                <img
                    class="hidden xl:block"
                    :src="`/assets/images/support/huong-dan-dang-ky-step2.webp?v=1`"
                />
                <img
                    class="mx-auto xl:hidden"
                    :src="`/assets/images/support/huong-dan-dang-ky-step2-1200.webp?v=1`"
                />
            </div>
            <div class="section">
                <p>
                    <strong>Bước 3</strong>: Quý Khách nhấn vào nút “Đăng ký” sau khi điền đầy đủ
                    thông tin, hệ thống sẽ cập nhật và đăng nhập tài khoản sau vài giây.
                </p>
            </div>
        </div>
    </HelpWrapper>
</template>

<script setup>
const brandName = useRuntimeConfig().public.BRAND_NAME
</script>
