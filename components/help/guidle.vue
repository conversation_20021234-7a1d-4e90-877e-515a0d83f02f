<template>
    <div class="term">
        <div class="custom-tabs">
            <!-- Tab Navigation -->
            <div
                :class="`nav-tabs w-full max-lg:flex max-lg:!justify-start max-lg:overflow-x-auto max-lg:overflow-y-hidden lg:grid lg:grid-cols-12`"
            >
                <div class="nav-tabs-inner col-span-12 col-start-1" :data-route="route.name">
                    <div
                        v-for="(tab, index) in tabs"
                        :key="index"
                        :class="[
                            'nav-tab-item !text-sm lg:w-1/4 lg:justify-center',
                            {
                                active: tab.key === route.name,
                            },
                        ]"
                        :data-title="tab.title"
                        @click="changeTab(tab)"
                    >
                        <span
                            class="nav-item-title whitespace-nowrap first-letter:uppercase"
                            :data-title="tab.title"
                        >
                            {{ $t(tab.title) }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Tab Content -->
            <div class="tab-content lg:grid lg:grid-cols-12">
                <div class="col-span-12 col-start-1">
                    <component :is="component[route.name]" />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
const route = useRoute()
const router = useRouter()
const tabs = [
    {
        title: 'supports.p2p',
        key: 'huong-dan-giao-dich',
        url: '/huong-dan-giao-dich',
    },
    {
        title: 'supports.registration',
        key: 'huong-dan-dang-ky',
        url: '/huong-dan-dang-ky',
    },
    {
        title: 'supports.deposit',
        key: 'huong-dan-nap-tien',
        url: '/huong-dan-nap-tien',
    },
    {
        title: 'supports.withdrawal',
        key: 'huong-dan-rut-tien',
        url: '/huong-dan-rut-tien',
    },
]

const component = shallowRef({
    'huong-dan-giao-dich': resolveComponent('HelpHuongDanGiaoDich'),
    'huong-dan-dang-ky': resolveComponent('HelpHuongDanDangKy'),
    'huong-dan-nap-tien': resolveComponent('HelpHuongDanNapTien'),
    'huong-dan-rut-tien': resolveComponent('HelpHuongDanRutTien'),
})

const changeTab = item => {
    router.replace(item.url)
}
</script>
<style lang="scss" scoped>
.nav-tabs-inner {
    @apply flex items-center gap-4 border-b border-b-slate-800 max-lg:justify-center lg:gap-6;
}

.nav-tab-item {
    @apply relative flex cursor-pointer items-center gap-1 py-[6px] text-sm font-medium text-white transition-all lg:py-[10px] lg:text-base;
    &:before {
        @apply absolute -bottom-[1px] left-0 h-[2px] w-full bg-transparent content-[''];
    }
}

.tab-content {
    @apply py-4 lg:py-6;
}

:deep() {
    .nav-tab-item:hover,
    .nav-tab-item.active {
        &::before {
            @apply bg-rose-600;
        }
    }
}
</style>
