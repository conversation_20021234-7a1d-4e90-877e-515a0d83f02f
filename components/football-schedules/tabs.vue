<template>
    <div ref="tabsWrapRef" class="tabs-wrapper">
        <div
            ref="tabsRef"
            class="tabs flex w-full shrink-0 gap-6 overflow-hidden max-[389px]:justify-between max-[389px]:gap-3 xl:gap-9"
            :class="{ 'fixed-menu': isFixed }"
        >
            <NuxtLink
                v-for="(item, index) in items"
                :key="index"
                class="flex cursor-pointer border-b-[3px] border-solid border-transparent pb-1 text-center text-sm font-medium leading-5 text-[#717589] hover:text-rose-600 max-lg:px-0"
                :class="currentTab === item.key ? 'active !border-rose-600 !text-rose-600' : ''"
                :to="item.link"
            >
                <span>{{ item.name }}</span>
            </NuxtLink>
        </div>
    </div>
</template>
<script setup lang="ts">
defineProps({
    items: {
        type: Object,
        default: () => {},
    },
})
const route = useRoute()
const currentTab = computed(() => route?.params?.tab)
</script>
<style lang="scss" scoped>
.tabs-wrapper {
    @apply sticky top-[0] z-[30] border-b border-solid border-[#DFE3EC] bg-[#ffffff] pt-2 xl:relative xl:pt-0;
}
</style>
