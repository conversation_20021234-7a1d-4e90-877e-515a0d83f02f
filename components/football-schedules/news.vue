<template>
    <div v-if="articles?.length" class="news">
        <h2 class="mb-2 text-base font-semibold leading-normal text-white">
            {{ $t('football_schedules.news_title') }}
        </h2>
        <div class="grid gap-4">
            <nuxt-link
                v-for="(post, index) in articles"
                :key="index"
                :to="`${PAGE_URL.NEWS}/post/${post.alias}`"
                class="relative flex flex-row overflow-hidden rounded-lg bg-slate-800 text-sm leading-[18px] transition-all duration-300 hover:shadow xl:flex-col"
            >
                <CommonImage
                    lazyload
                    :src="`${post?.thumbnail}`"
                    :alt="post?.title"
                    :imageDefault="`/${NEWS_IMAGE_DEFAULT}`"
                    classWrapper="aspect-[auto_220/122] max-lg:aspect-[auto_140/100] flex-shrink-0 object-cover max-lg:w-[140px]"
                    class="lazy-image-custom h-full w-full object-fill"
                />
                <div class="flex flex-col px-3 py-4 lg:py-2">
                    <h4
                        class="mb-3 line-clamp-2 text-sm font-medium text-white lg:mb-1 lg:text-xs lg:leading-[18px]"
                    >
                        {{ post.title }}
                    </h4>
                    <div class="flex items-center gap-0.5">
                        <i class="icon-clock size-4 text-base leading-4 text-slate-300"></i>
                        <span class="text-[10px] leading-[14px] text-slate-300">
                            {{ formatDate(post.created_time, 'dd/MM/yyyy') }}
                        </span>
                    </div>
                </div>
            </nuxt-link>
        </div>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { NEWS_IMAGE_DEFAULT } from '~/constants'
import { PAGE_URL } from '~/constants/page-urls'
import { useNewsStore } from '~/stores'
import { formatDate } from '~/utils/date'

const useNewsStoreInstance = useNewsStore()
const { postsByCategory } = storeToRefs(useNewsStoreInstance)
const { getPostByCategory } = useNewsStoreInstance

getPostByCategory('soi-keo-bong-da')

const articles = computed(() => postsByCategory.value['soi-keo-bong-da']?.slice(0, 3) || [])
</script>
<style lang="scss">
.lazy-image-custom {
    object-fit: fill !important;
}
</style>
