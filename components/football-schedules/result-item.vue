<template>
    <div class="schedule-item" v-if="schedule" :class="{ 'is-active': isShowSchedule }">
        <NuxtLink
            role="button"
            class="schedule-item__league flex cursor-pointer items-center gap-2 bg-slate-700 px-3 py-2"
            @click="toggleCollapse"
        >
            <img
                :src="schedule.season_info.logo"
                :alt="schedule.season_info.name"
                class="size-5 object-contain"
            />
            <span class="text-xs font-medium text-white">{{ schedule.season_info.name }}</span>
        </NuxtLink>
        <div class="schedule-item__content" v-if="isShowSchedule">
            <div v-for="(item, index) in schedule.events" :key="index" class="match-item px-3 py-4">
                <div class="match-item__battle flex">
                    <div
                        class="match-item__date flex w-[41px] flex-col text-xs font-semibold text-white xl:w-[60px]"
                    >
                        {{ formatDateUTC(item.sport_event.start_time) }}
                        <span class="text-sm">{{
                            formatTimeUTC(item.sport_event.start_time)
                        }}</span>
                    </div>

                    <div class="match-item__teams">
                        <div class="match-item__teams--box flex w-full flex-row items-center gap-2">
                            <img
                                class="size-5 min-w-5 object-contain"
                                :src="item.sport_event.competitors[0].logo"
                                :alt="item.sport_event.competitors[0].name"
                            />
                            <div class="match-item__teams--name flex flex-col flex-wrap gap-[2px]">
                                <div class="team__name w-full flex-1">
                                    {{ item.sport_event.competitors[0].name }}
                                </div>
                                <div v-if="result && getCard(item, 0)" class="flex">
                                    <span
                                        v-if="getCard(item, 0)?.yellow_cards"
                                        class="match-item__teams--card flex"
                                    >
                                        {{ getCard(item, 0)?.yellow_cards }}
                                        <img
                                            :src="`/assets/images/pages/football-schedules/yellow_cards.svg`"
                                            alt="yellow"
                                        />
                                    </span>
                                    <span
                                        v-if="getCard(item, 0)?.red_cards"
                                        class="match-item__teams--card flex"
                                    >
                                        {{ getCard(item, 0)?.red_cards }}
                                        <img
                                            :src="`/assets/images/pages/football-schedules/red_cards.svg`"
                                            alt="red"
                                        />
                                    </span>
                                </div>
                            </div>
                            <div
                                v-if="!isNaN(+item.sport_event_status?.home_score)"
                                class="match-item__teams--score ml-auto text-sm font-semibold text-white xl:text-xs"
                            >
                                <span class="ml-2">
                                    {{ item.sport_event_status?.home_score || 0 }}
                                </span>
                            </div>
                        </div>
                        <div class="match-item__teams--box flex w-full flex-row items-center gap-2">
                            <img
                                class="size-5 min-w-5 object-contain"
                                :src="item.sport_event.competitors[1].logo"
                                :alt="item.sport_event.competitors[1].name"
                            />
                            <div class="match-item__teams--name flex flex-col flex-wrap gap-[2px]">
                                <div class="team__name w-full flex-1">
                                    {{ item.sport_event.competitors[1].name }}
                                </div>
                                <div v-if="result && getCard(item, 1)" class="flex">
                                    <span
                                        v-if="getCard(item, 1)?.yellow_cards"
                                        class="match-item__teams--card flex"
                                    >
                                        {{ getCard(item, 1)?.yellow_cards }}
                                        <img
                                            :src="`/assets/images/pages/football-schedules/yellow_cards.svg`"
                                            alt="yellow"
                                        />
                                    </span>
                                    <span
                                        v-if="getCard(item, 1)?.red_cards"
                                        class="match-item__teams--card flex"
                                    >
                                        {{ getCard(item, 1)?.red_cards }}
                                        <img
                                            :src="`/assets/images/pages/football-schedules/red_cards.svg`"
                                            alt="red"
                                        />
                                    </span>
                                </div>
                            </div>
                            <div
                                v-if="!isNaN(+item.sport_event_status?.away_score)"
                                class="match-item__teams--score ml-auto text-sm font-semibold text-white xl:text-xs"
                            >
                                <span class="ml-2">
                                    {{ item.sport_event_status?.away_score || 0 }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div :class="['match-item__status ml-auto', item.sport_event_status?.status]">
                        {{ convertStatus(item.sport_event_status?.status) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { getCard, formatDateUTC, formatTimeUTC } from '~/utils/football-schedule-helper'

const props = defineProps({
    schedule: {
        type: Object,
        default: () => ({}),
    },
    odds: {
        type: Boolean,
        required: false,
    },
    match: {
        type: Boolean,
        required: false,
    },
    result: {
        type: Boolean,
        required: false,
    },
    isActive: {
        type: Boolean,
        required: false,
    },
})
const { t } = useI18n()
const isShowSchedule = ref(props.isActive)

const convertStatus = status => {
    switch (status) {
        case 'live':
            return t('football_schedules.match_status.live')
        case 'not_started':
            return t('football_schedules.match_status.not_started')
        case 'cancelled':
            return t('football_schedules.match_status.cancelled')
        case 'postponed':
            return t('football_schedules.match_status.postponed')
        default:
            return t('football_schedules.match_status.end')
    }
}

const toggleCollapse = () => {
    isShowSchedule.value = !isShowSchedule.value
}
</script>
<style scoped lang="scss">
.schedule-item {
    &.is-active {
        .schedule-item__league {
            &::before {
                @apply rotate-0 transform;
            }
        }
    }
    &__league {
        @apply relative border-b border-solid border-slate-700;
        &::before {
            @apply absolute right-3 top-1/2 grid size-6 -translate-y-1/2 rotate-180 transform place-content-center rounded bg-[#FFFFFF1A] font-['icomoon'] text-[12px] font-normal text-white transition-all content-['\e96c'] xl:right-2;
        }
    }
}
.match-item {
    @apply border-b border-solid border-slate-750;
    .team__name {
        @apply line-clamp-1 break-all text-sm font-normal text-white;
    }
    &__status {
        @apply whitespace-nowrap rounded-full px-2 py-0.5 text-xs text-gray-950;
        &.ended {
            @apply bg-slate-400;
        }
        &.live {
            @apply bg-cyan-400;
        }
        &.not_started {
            @apply bg-[#009B84];
        }
        &.cancelled {
            @apply bg-red-700;
        }
        &.postponed {
            @apply bg-orange-700;
        }
    }
    &__teams {
        @apply flex w-full max-w-[210px] flex-col items-start justify-between gap-[10px] xl:max-w-[261px];
    }
    &__teams--card {
        @apply mr-0.5 flex items-center py-0 text-xs font-medium text-white;
        img {
            @apply size-4;
        }
    }
    &__battle {
        @apply flex items-center gap-2;
    }
}
</style>
