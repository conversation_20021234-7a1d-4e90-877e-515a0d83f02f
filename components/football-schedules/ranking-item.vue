<template>
    <div class="schedule-item" :class="{ 'is-active': isShowSchedule }">
        <NuxtLink
            role="button"
            class="schedule-item__league flex cursor-pointer items-center gap-2 bg-slate-700 px-3 py-2"
            @click="toggleCollapse"
        >
            <img
                :src="league?.season.logo"
                :alt="league?.season.name"
                class="size-5 object-contain"
            />
            <span class="text-xs font-medium text-white">
                {{ league?.season.name }}
            </span>
        </NuxtLink>
        {{ league?.groupslag }}
        <div v-if="isShowSchedule">
            <div
                v-for="(item, idx) in league?.standings?.groups || league?.groups"
                :key="idx"
                class="ranking__group"
            >
                <div v-if="item.name" class="ranking__group--header">Bảng {{ item.name }}</div>
                <div class="ranking__group--info max-xl:!hidden">
                    <div class="ranking__group--stt">TT</div>
                    <div class="ranking__group--club">Đội</div>
                    <div class="ranking__group--title">
                        <div>Trận</div>
                        <div>Thắng</div>
                        <div>Hoà</div>
                        <div>Thua</div>
                        <div>BT</div>
                        <div>SBT</div>
                        <div>+/-</div>
                        <div>Điểm</div>
                    </div>
                </div>
                <div class="ranking__group--info xl:!hidden">
                    <div class="ranking__group--stt text-xs text-white">TT</div>
                    <div class="ranking__group--club text-xs text-white">Đội</div>
                    <div class="ranking__group--title ranking__group--title-mb text-xs text-white">
                        <div>Trận</div>
                        <div>Th</div>
                        <div>H</div>
                        <div>Thua</div>
                        <div>BT</div>
                        <div>SBT</div>
                        <div>+/-</div>
                        <div>Điểm</div>
                    </div>
                </div>

                <div v-if="!!item.standings.length" class="ranking__table">
                    <div
                        v-for="(standing, index) in item.standings"
                        :key="index"
                        :class="[
                            'ranking__table-item',
                            {
                                other: !!standing.current_outcome,
                            },
                            league?.colorRankList[
                                standing.current_outcome?.toLowerCase()?.replaceAll(' ', '-') || ''
                            ],
                            standing.current_outcome?.toLowerCase()?.replaceAll(' ', '-') || '',
                        ]"
                    >
                        <div class="ranking__table-item--left">
                            <div class="ranking__table-stt">
                                {{ standing.rank }}
                            </div>
                            <div class="ranking__table-country">
                                <img :src="standing.competitor.logo" alt="country" />
                                <div class="ranking__table-country--name">
                                    <div class="team__name">
                                        {{ standing.competitor.name }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="ranking__table-item--right">
                            <div>
                                {{ standing.played }}
                            </div>
                            <div>
                                {{ standing.win }}
                            </div>
                            <div>
                                {{ standing.draw }}
                            </div>
                            <div>
                                {{ standing.loss }}
                            </div>
                            <div>
                                {{ standing.goals_for }}
                            </div>
                            <div>
                                {{ standing.goals_against }}
                            </div>
                            <div>
                                {{ standing.goals_diff }}
                            </div>
                            <div>
                                {{ standing.points }}
                            </div>
                        </div>
                    </div>
                </div>

                <div v-else class="ranking__table1">
                    <EmptyData />
                </div>
            </div>

            <div
                v-if="
                    league?.outcome?.length ||
                    completedOutCome(league?.standings?.groups)?.length ||
                    league?.standings?.groups?.length
                "
                class="mx-4 my-3"
            >
                <div v-if="league?.standings?.groups?.length > 1" class="ranking__type">
                    <div class="ranking__type--title">Vòng loại</div>
                    <div class="ranking__type--content">
                        <div
                            v-for="(it, index) in completedOutCome(league?.standings?.groups)"
                            :key="index"
                            :class="[
                                { other: !!it },
                                classNamesList[index],
                                league?.colorRankList[
                                    it?.toLowerCase()?.replaceAll(' ', '-') || ''
                                ],

                                it.toLowerCase()?.replaceAll(' ', '-') || '',
                            ]"
                            class="ranking__type--item"
                        >
                            {{ outcomeTranslate[it.toLowerCase()?.replaceAll(' ', '-')] || it }}
                        </div>
                    </div>
                </div>
                <div v-else class="ranking__type">
                    <div class="ranking__type--title">Ghi chú</div>
                    <div class="ranking__type--content">
                        <div
                            v-for="(it, index) in league?.outcome"
                            :key="index"
                            class="ranking__type--item"
                            :class="[
                                { other: !!it },
                                classNamesList[index],
                                league?.colorRankList[
                                    it?.toLowerCase()?.replaceAll(' ', '-') || ''
                                ],

                                it.toLowerCase()?.replaceAll(' ', '-') || '',
                            ]"
                        >
                            {{ outcomeTranslate[it.toLowerCase()?.replaceAll(' ', '-')] || it }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
const props = defineProps({
    league: {
        type: Object,
        default: () => ({}),
    },
    colorRankList: {
        type: Object,
        default: () => ({}),
    },
    isActive: {
        type: Boolean,
        default: true,
    },
})
const isShowSchedule = ref(props.isActive)
const toggleCollapse = () => {
    isShowSchedule.value = !isShowSchedule.value
}

const outcomeTranslate = {
    qualified: 'Vòng tiếp theo',
    playoffs: 'Vòng đấu play-off',
    relegation: 'Xuống hạng',
    'relegation-playoffs': 'Trận quyết định đội xuống hạng',
    'champions-league': 'Vòng bảng Vô địch các CLB Châu Âu',
    'champions-league-league-stage': 'Vòng bảng Vô địch các CLB Châu Âu',
    'champions-league-qualification': 'Trận quyết định đội xuống hạng',
    'uefa-europa-league': 'Vòng bảng UEFA Europa',
    'europa-league-league-stage': 'Vòng bảng UEFA Europa',
    'conference-league-qualification': 'Vòng loại UEFA Europa Conference',
    'uefa-ecl-playoffs': 'Vòng loại UEFA Europa Conference',
    'knockout-phase': 'Vòng loại trực tiếp',
}
const classNamesList = [
    'bg-light-blue',
    'bg-light-yellow',
    'bg-light-green',
    'bg-light-pink',
    'bg-light-orange',
    'bg-light-cyan',
    'bg-light-lavender',
    'bg-light-salmon',
    'bg-light-gold',
    'bg-light-teal',
]

const completedOutCome = groups => {
    let result = []
    groups?.forEach(group => {
        if (group.current_outcome.length > 0) {
            result = result.concat(group.current_outcome)
        }
    })
    return [...new Set(result)]
}
</script>
<style lang="scss" scoped>
.ranking-title {
    @apply text-sm text-white;
}
.ranking__group {
    &--header {
        @apply px-3 py-2.5 text-sm font-bold text-white xl:px-0 xl:text-base;
    }
    &--info {
        @apply flex h-[40px] items-center justify-between bg-slate-800 px-4 font-normal text-white xl:text-xs;
    }
    &--stt {
        @apply w-5 shrink-0 text-center;
    }
    &--club {
        @apply pl-6 text-white xl:pl-8;
    }
    &--title {
        @apply ml-auto grid w-auto grid-cols-8 gap-0 text-center text-white xl:gap-1 2xl:gap-2;
        > div {
            @apply w-[29px] xl:w-[36px];
            &:last-child {
                @apply text-white;
            }
        }
    }
}

.ranking__table {
    @apply mb-4 flex flex-col;
    &-item {
        @apply relative flex items-center justify-between border-b border-solid border-slate-750 px-4 py-2.5;
        &:after {
            @apply absolute left-0 top-0 h-full w-1 content-[''];
        }
        &.blue,
        &.red {
            .ranking__table-country:before {
                background: transparent;
                @apply absolute left-2 top-1/2 size-0 -translate-y-1/2 rounded-none border-4 border-t-0 border-b-[#1abd8c] border-l-[transparent] border-r-[transparent] content-[''];
            }
        }
        &.red {
            &:after {
                @apply bg-[#FF274E];
            }
            .ranking__table-country {
                &:before {
                    @apply rotate-180 border-b-[#FF274E];
                }
            }
        }
        &.yellow:after {
            @apply bg-[#ffc659];
        }
        &.blue:after {
            @apply bg-[#4286f5];
        }
        &.green:after {
            @apply bg-[#31D75F];
        }
        &.blue2:after {
            @apply bg-[#1abd8c];
        }
        &.orange:after {
            @apply bg-[#D98556];
        }

        &.bg-light-blue::after {
            background: #5f9bd5;
        }

        &.bg-light-yellow::after,
        &.uefa--qualifying:after {
            background: #ffffb3;
        }

        &.bg-light-pink::after {
            background: #ff9a9e;
        }

        &.bg-light-orange::after {
            background: #ff9f00;
        }

        &.bg-light-cyan::after {
            background: #aeeeee;
        }

        &.first-group,
        &.qualified,
        &.champions-league,
        &.champions-league-league-stage {
            &:after {
                background: #31d75f;
            }
        }

        &.conference-league-qualification,
        &.uefa-ecl-playoffs {
            &:after {
                background: #0284e7;
            }
        }

        &.uefa-europa-league,
        &.europa-league-league-stage {
            &:after {
                background: #03c4fe;
            }
        }

        &.second-group,
        &.playoffs,
        &.uefa-ecl-playoffs {
            &:after {
                background: #0664de;
            }
        }

        &.relegation-playoffs,
        &.champions-league-qualification {
            &:after {
                background: #d98556;
            }
        }

        &.relegation {
            &:after {
                background: #ff274e;
            }
        }
        &.knockout-phase {
            &:after {
                background: #deda06;
            }
        }
        &.uefa-el-qualification:after {
            @apply bg-[#76c7c0];
        }
        &--left {
            @apply flex flex-1;
        }
        &--right {
            @apply grid w-auto grid-cols-8 gap-0 text-center text-xs font-normal xl:gap-1 xl:text-sm 2xl:gap-2;
            > div {
                @apply w-[29px] text-white xl:w-[36px];
                &:last-child {
                    @apply font-semibold;
                }
            }
        }
    }
    &-stt {
        @apply flex min-w-5 items-center justify-center text-sm text-white;
    }
    &-country {
        @apply relative flex items-center gap-1 text-xs text-white xl:pl-8 xl:text-sm;
        img {
            @apply size-5 object-contain;
        }
        .team__name {
            @apply line-clamp-1 break-all text-xs font-normal text-white xl:text-sm;
        }
    }
}

.schedule-item {
    &.is-active {
        .schedule-item__league {
            &::before {
                @apply rotate-0 transform;
            }
        }
    }
    &__league {
        @apply relative border-b border-solid border-slate-700;
        &::before {
            @apply absolute right-3 top-1/2 grid size-6 -translate-y-1/2 rotate-180 transform place-content-center rounded bg-[#FFFFFF1A] font-['icomoon'] text-[12px] font-normal text-white transition-all content-['\e96c'] xl:right-2;
        }
    }
}
.ranking__type {
    @apply text-xs leading-[18px] text-white;
    &--title {
        @apply mb-1 text-xs font-semibold;
    }
    &--content {
        @apply grid gap-1;
        > div {
            @apply relative flex items-center gap-2;
            &:before {
                @apply size-2.5 bg-[#31D75F] content-[''];
            }
            &.orange:before {
                @apply bg-[#D98556];
            }
            &.red:before {
                @apply bg-[#FF274E];
            }
            &.blue:before {
                @apply bg-[#4286f5];
            }
            &.yellow:before {
                @apply bg-[#ffc659];
            }
            &.bg-light-blue::before {
                background: #5f9bd5;
            }

            &.bg-light-yellow::before {
                background: #ffffb3;
            }

            &.bg-light-green::before {
                background: #76c7c0;
            }

            &.bg-light-pink::before {
                background: #ff9a9e;
            }

            &.bg-light-orange::before {
                background: #ff9f00;
            }

            &.bg-light-cyan::before {
                background: #aeeeee;
            }

            &.bg-light-lavender::before {
                background: #e6e6fa;
            }

            &.bg-light-salmon::before {
                background: #ff6f61;
            }

            &.bg-light-gold::before {
                background: #f5deb3;
            }

            &.bg-light-teal::before {
                background: #a0d6b4;
            }
            &.first-group,
            &.qualified,
            &.champions-league,
            &.champions-league-league-stage {
                &:before {
                    background: #31d75f;
                }
            }

            &.conference-league-qualification,
            &.uefa-ecl-playoffs {
                &:before {
                    background: #0284e7;
                }
            }

            &.uefa-europa-league,
            &.europa-league-league-stage {
                &:before {
                    background: #03c4fe;
                }
            }

            &.second-group,
            &.playoffs,
            &.uefa-ecl-playoffs {
                &:before {
                    background: #0664de;
                }
            }

            &.relegation-playoffs,
            &.champions-league-qualification {
                &:before {
                    background: #d98556;
                }
            }

            &.relegation {
                &:before {
                    background: #ff274e;
                }
            }
            &.knockout-phase {
                &:before {
                    background: #deda06;
                }
            }
        }
    }
}
</style>
