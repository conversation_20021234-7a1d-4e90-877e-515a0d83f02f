<template>
    <div class="flex h-full flex-col rounded bg-slate-900 max-lg:-mx-3 lg:p-6 lg:pt-2">
        <CommonTabs :tabs="tabs" class-nav-wrapper="max-lg:!justify-start max-lg:!px-3" />
    </div>
</template>
<script setup>
import dayjs from 'dayjs'
import weekday from 'dayjs/plugin/weekday'
import { PAGE_URL } from '~/constants/page-urls'
import { tabToTitleMap } from '~/resources/football-schedules'

dayjs.extend(weekday)

const tabs = ref([
    {
        title: tabToTitleMap['lich-thi-dau-bong-da'],
        content: resolveComponent('FootballSchedulesTabSchedule'),
        link: PAGE_URL.FOOTBALL_SCHEDULE.SCHEDULE,
        classNavInner: 'max-lg:w-full col-span-12 col-start-1',
    },
    {
        title: tabToTitleMap['ty-le-keo-bong-da'],
        content: resolveComponent('FootballSchedulesTabOdds'),
        link: PAGE_URL.FOOTBALL_SCHEDULE.ODDS,
        classNavInner: 'max-lg:w-full col-span-12 col-start-1',
    },
    {
        title: tabToTitleMap['bang-xep-hang-bong-da'],
        content: resolveComponent('FootballSchedulesTabRanking'),
        link: PAGE_URL.FOOTBALL_SCHEDULE.RANKING,
        classNavInner: 'max-lg:w-full col-span-12 col-start-1',
    },
    {
        title: tabToTitleMap['ket-qua-bong-da'],
        content: resolveComponent('FootballSchedulesTabResult'),
        link: PAGE_URL.FOOTBALL_SCHEDULE.RESULT,
        classNavInner: 'max-lg:w-full col-span-12 col-start-1',
    },
])

const route = useRoute()
const queryDate = ref(
    !route?.query?.date || route?.query?.date === 'week' || route?.query?.date === 'all'
        ? dayjs().format('YYYY-MM-DD')
        : route?.query?.date
)
const filterWeek = ref(
    !route?.query?.date
        ? 'all'
        : route?.query?.date !== 'week' && route?.query?.date !== 'all'
          ? 'date'
          : route?.query?.date
)

const querySeason = ref(route?.query?.season || 'all')
const schedule = computed(() => (route.params.tab === 'lich-thi-dau-bong-da' ? 'schedule' : ''))
const isStandingTab = computed(() => route.params.tab === 'bang-xep-hang-bong-da')
const queryAddOn = computed(() =>
    route?.params?.tab === 'ket-qua-bong-da'
        ? {
              match: 'results',
          }
        : {}
)

const useFootballSchedulesInstance = useFootballSchedules()
const { getSchedules } = useFootballSchedulesInstance

const currentDate = new Date()
const tomorrowDate = new Date()
tomorrowDate.setDate(currentDate.getDate() + 1)

watch(
    () => route.query,
    newQuery => {
        if (route?.params?.tab === 'bang-xep-hang-bong-da') return
        if (newQuery?.date === 'week') {
            filterWeek.value = 'week'
            queryDate.value = dayjs().format('YYYY-MM-DD')
        } else if (newQuery?.date === 'all' || typeof newQuery?.date === 'undefined') {
            queryDate.value = newQuery?.date ?? dayjs().format('YYYY-MM-DD')
            filterWeek.value = 'all'
        } else {
            filterWeek.value = 'date'
            queryDate.value = newQuery?.date || dayjs().format('YYYY-MM-DD')
        }
        querySeason.value = newQuery?.season || 'all'
        getSchedules(
            queryDate.value,
            querySeason.value,
            filterWeek.value,
            schedule.value,
            queryAddOn.value
        )
    }
)

await useAsyncData('schedules', async () => {
    if (isStandingTab.value) return
    await getSchedules(
        queryDate.value,
        querySeason.value,
        filterWeek.value,
        schedule.value,
        queryAddOn.value
    )

    return {}
})
</script>
