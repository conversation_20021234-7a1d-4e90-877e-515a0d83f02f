<template>
    <CommonModal :show="show" @close="closeSelf" size="md" class="!rounded-2xl">
        <template #default>
            <div class="pb-1 pt-4">
                <div class="odds-detail-content">
                    <div class="mb-3 flex w-full items-center justify-between px-3 text-grey-900">
                        <div
                            class="flex max-w-[calc(100%_-_80px)] items-center justify-start gap-2"
                        >
                            <div class="relative h-5 w-5 min-w-5">
                                <img
                                    :src="schedule.season_info.logo"
                                    :alt="schedule.season_info.name"
                                    class="size-5 object-contain"
                                />
                            </div>
                            <div class="line-clamp-1 break-all text-xs font-medium">
                                {{ schedule.season_info.name }}
                            </div>
                        </div>
                        <div class="flex items-end justify-center gap-1">
                            <div class="text-xs font-semibold">
                                {{ formatDateUTCOdds(dataOdds.sport_event.start_time) }}
                            </div>
                        </div>
                    </div>
                    <div class="match-item__teams mb-6 px-3">
                        <div class="match-item__teams--box mb-1 flex items-center gap-2 py-0.5">
                            <div class="match-item__teams--flag min-w-5">
                                <img
                                    :src="dataOdds.sport_event.competitors[0].logo"
                                    :alt="dataOdds.sport_event.competitors[0].name"
                                    class="size-5 min-w-5 object-contain"
                                />
                            </div>
                            <div class="match-item__teams--name line-clamp-1 break-all">
                                <div
                                    class="team__name line-clamp-1 break-all text-sm font-normal text-grey-900"
                                >
                                    {{ dataOdds.sport_event.competitors[0].name }}
                                </div>
                            </div>
                        </div>
                        <div class="match-item__teams--box flex items-center gap-2 py-0.5">
                            <div class="match-item__teams--flag min-w-5">
                                <img
                                    :src="dataOdds.sport_event.competitors[1].logo"
                                    :alt="dataOdds.sport_event.competitors[1].name"
                                    class="size-5 object-contain"
                                />
                            </div>
                            <div class="match-item__teams--name line-clamp-1 break-all">
                                <div
                                    class="team__name line-clamp-1 break-all text-sm font-normal text-grey-900"
                                >
                                    {{ dataOdds.sport_event.competitors[1].name }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div
                            class="grid grid-cols-6 items-center gap-2 bg-[#E4E9F0] p-2 text-[#505364]"
                        >
                            <div class="text-xs">
                                {{ $t('football_schedules.handicap') }}
                            </div>
                            <div class="text-xs">
                                {{ $t('football_schedules.over_under') }}
                            </div>
                            <div class="text-xs">
                                {{ $t('football_schedules.1x2') }}
                            </div>
                            <div class="text-xs">{{ $t('football_schedules.handicap') }} HT</div>
                            <div class="text-xs">{{ $t('football_schedules.over_under') }} HT</div>
                            <div class="text-xs">{{ $t('football_schedules.1x2') }} HT</div>
                        </div>
                        <div class="mt-3 px-3">
                            <div class="match-item__odds--row">
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left min-w-[27px] sm:text-right">
                                        {{ handicapHome(dataOdds).rate }}
                                    </div>
                                    <div class="match-item__odds--right">
                                        {{ handicapHome(dataOdds).odds }}
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">
                                        O {{ overRate(dataOdds).rate }}
                                    </div>
                                    <div class="match-item__odds--right">
                                        {{ overRate(dataOdds).odds }}
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">H</div>
                                    <div class="match-item__odds--right">
                                        <span :class="checkNumber(fullMatch(dataOdds).oh)">
                                            {{ fullMatch(dataOdds).oh }}
                                        </span>
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left min-w-[27px] sm:text-right">
                                        {{ handicapHomeHT(dataOdds).rate }}
                                    </div>
                                    <div class="match-item__odds--right">
                                        {{ handicapHomeHT(dataOdds).odds }}
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">
                                        O {{ overRateHT(dataOdds).rate }}
                                    </div>
                                    <div class="match-item__odds--right">
                                        {{ overRateHT(dataOdds).odds }}
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">H</div>
                                    <div class="match-item__odds--right">
                                        <span :class="checkNumber(fullMatch(dataOdds).oh)">
                                            {{ fullMatchHT(dataOdds).oh }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="match-item__odds--row">
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left min-w-[27px] sm:text-right">
                                        {{ handicapAway(dataOdds).rate }}
                                    </div>
                                    <div
                                        class="match-item__odds--right"
                                        :class="checkNumber(handicapAway(dataOdds).odds)"
                                    >
                                        {{ handicapAway(dataOdds).odds }}
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">
                                        U {{ underRate(dataOdds).rate }}
                                    </div>
                                    <div
                                        class="match-item__odds--right"
                                        :class="checkNumber(underRate(dataOdds).odds)"
                                    >
                                        {{ underRate(dataOdds).odds }}
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">A</div>
                                    <div class="match-item__odds--right">
                                        <span :class="checkNumber(fullMatch(dataOdds).oa)">
                                            {{ fullMatch(dataOdds).oa }}
                                        </span>
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left min-w-[27px] sm:text-right">
                                        {{ handicapAwayHT(dataOdds).rate }}
                                    </div>
                                    <div
                                        class="match-item__odds--right"
                                        :class="checkNumber(handicapAwayHT(dataOdds).odds)"
                                    >
                                        {{ handicapAwayHT(dataOdds).odds }}
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">
                                        U {{ underRateHT(dataOdds).rate }}
                                    </div>
                                    <div
                                        class="match-item__odds--right"
                                        :class="checkNumber(underRate(dataOdds).odds)"
                                    >
                                        {{ underRateHT(dataOdds).odds }}
                                    </div>
                                </div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">A</div>
                                    <div class="match-item__odds--right">
                                        <span :class="checkNumber(fullMatchHT(dataOdds).oa)">
                                            {{ fullMatchHT(dataOdds).oa }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="match-item__odds--row">
                                <div class="match-item__odds--td !bg-transparent"></div>
                                <div class="match-item__odds--td !bg-transparent"></div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">D</div>
                                    <div class="match-item__odds--right">
                                        <span :class="checkNumber(fullMatch(dataOdds).od)">
                                            {{ fullMatch(dataOdds).od }}
                                        </span>
                                    </div>
                                </div>
                                <div class="match-item__odds--td !bg-transparent"></div>
                                <div class="match-item__odds--td !bg-transparent"></div>
                                <div class="match-item__odds--td">
                                    <div class="match-item__odds--left">D</div>
                                    <div class="match-item__odds--right">
                                        <span :class="checkNumber(fullMatchHT(dataOdds).od)">
                                            {{ fullMatchHT(dataOdds).od }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template #footer>
            <div class="grid grid-cols-2 justify-between gap-3 px-3 py-6">
                <button
                    @click="closeSelf"
                    class="h-9 rounded-lg border border-solid border-[#DFE3EC] px-4 py-2 text-sm font-medium text-[#3D3F4D] hover:border-[#F1F3F9] hover:bg-[#F1F3F9]"
                >
                    {{ $t('common.close') }}
                </button>
                <button
                    @click="submit"
                    class="h-9 rounded-lg bg-rose-600 px-4 py-2 text-sm font-medium text-white hover:bg-[#39B689]"
                >
                    {{ $t('football_schedules.bet_now') }}
                </button>
            </div>
        </template>
    </CommonModal>
</template>
<script setup>
import {
    handicapHome,
    handicapAway,
    overRate,
    underRate,
    fullMatch,
    handicapHomeHT,
    handicapAwayHT,
    overRateHT,
    underRateHT,
    fullMatchHT,
    checkNumber,
    formatDateUTCOdds,
} from '~/utils/football-schedule-helper'

defineProps(['show', 'dataOdds', 'schedule'])

const emit = defineEmits(['close', 'submit'])
const closeSelf = () => {
    emit('close')
}
const submit = () => {
    emit('submit')
}
</script>

<style scoped>
.match-item {
    &__odds {
        @apply text-xs;
        &--row {
            @apply grid w-full grid-cols-6 items-center justify-end gap-2 text-center;
            &:not(:last-child) {
                @apply mb-1;
            }
        }
        &--td {
            @apply flex h-[36px] w-full flex-col items-center justify-between text-nowrap rounded bg-[#F7F7F9] px-1 py-[2px] text-xs;
        }
        &--left {
            @apply text-[#505364];
        }
        &--right {
            @apply font-semibold text-grey-900;
        }
    }
}
</style>
