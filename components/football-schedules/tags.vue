<template>
    <div class="tags">
        <div class="mb-2 text-base font-semibold leading-normal text-white">TAGS</div>
        <div class="flex flex-wrap gap-2">
            <NuxtLink
                v-for="(tag, index) in tagsNews"
                :key="index"
                :to="tag.link"
                class="news__tags--item block bg-slate-800 px-[10px] py-0.5 text-xs text-white hover:bg-slate-700 hover:text-white"
                @click="scrollToTop"
            >
                {{ tag.name }}
            </NuxtLink>
        </div>
    </div>
</template>
<script setup>
import { scrollToTop } from '~/utils/scroll/index'
import { tagsNews } from '~/resources/football-schedules'
</script>
