<template>
    <div class="filter-day flex gap-1 max-xl:px-3 xl:gap-2">
        <NuxtLink
            v-for="(item, index) in filterDays"
            :key="index"
            role="button"
            @click="setFilterDate(item.key)"
            :class="item.key === currentDate ? 'bg-rose-600 text-white' : 'bg-slate-700 text-white'"
            class="block cursor-pointer select-none rounded-full px-4 py-2 text-center text-xs xl:px-6 xl:py-2 xl:text-sm"
        >
            {{ item.name }}
        </NuxtLink>
    </div>
</template>
<script setup>
import { getNewUrl } from '~/utils/url'

const props = defineProps({
    filterDays: {
        type: Array,
        default: () => [],
    },
    isAll: {
        type: Boolean,
        default: false,
    },
})
const route = useRoute()
const router = useRouter()

const currentDate = computed(() => {
    const queryDate = route?.query?.date ?? 'all'
    return props.filterDays.some(date => date.key === queryDate)
        ? queryDate
        : new Date().toISOString().split('T')[0]
})

const setFilterDate = key => {
    if (key === currentDate.value) {
        return
    }
    const url = getNewUrl(route.fullPath, { date: key })
    router.push(url)
}
</script>
