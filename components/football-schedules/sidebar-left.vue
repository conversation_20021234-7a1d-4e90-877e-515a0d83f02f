<template>
    <div class="sidebar-left shrink-0">
        <div class="relative z-10 rounded bg-slate-900 xl:sticky xl:top-[94px] xl:px-3 xl:py-4">
            <div
                ref="searchContainerRef"
                class="search-filter relative xl:mb-3"
                :class="{ active: showSearchMobile }"
            >
                <i
                    class="icon-search absolute left-2 top-1/2 z-1 mr-[6px] -translate-y-1/2 text-[20px] text-slate-300"
                ></i>
                <i
                    v-if="searchInput"
                    @click="showMobile ? resetSelectMobile() : resetFilters()"
                    class="search-close icon-close-mb absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer text-[18px] text-[#7E8393] hover:text-[#fff]"
                ></i>
                <input
                    name="search"
                    ref="searchInputRef"
                    v-model="searchInput"
                    class="mb-0 h-10 w-full text-ellipsis rounded-lg border border-solid border-slate-800 !bg-slate-800 !px-2 !py-[10px] !pl-8 text-[14px] text-white placeholder:text-slate-300 focus:border-slate-800"
                    :placeholder="$t('common.search')"
                    :label="''"
                    @input="showDropdown"
                    @focus="showDropdown"
                />
                <div
                    v-if="isShowDropdown"
                    class="shadow-small common-scrollbar absolute left-0 top-[calc(100%_+_2px)] flex max-h-[240px] w-full flex-col gap-2 overflow-auto rounded bg-slate-700 text-white"
                >
                    <template v-if="filteredLeagues?.length">
                        <div
                            v-for="(league, index) in filteredLeagues"
                            :key="index"
                            role="button"
                            @click="handleFilterLeagueNew(league.id, league.name)"
                            class="flex cursor-pointer items-center gap-0.5 rounded px-1 py-[10px] xl:hover:bg-slate-900"
                        >
                            <img
                                :src="league.logo"
                                :alt="league.name"
                                class="size-5 shrink-0 rounded-sm bg-[#fff] object-contain p-[2px]"
                            />
                            <span class="text-text-white line-clamp-1 text-xs">{{
                                league.name
                            }}</span>
                        </div>
                    </template>
                    <template v-else>
                        <div class="flex items-center gap-1 p-3 text-xs text-white xl:gap-2">
                            <i class="icon-search size-5 text-xl leading-5 text-white"></i>
                            {{ searchInput }}
                        </div>
                    </template>
                </div>
            </div>
            <div class="filter-left__mobile xl:hidden">
                <div
                    role="button"
                    ref="suggestFilterMobile"
                    :class="['filter-left__mobile--select', { active: showLeagueMobile }]"
                    @click="handleLeagueChoose"
                >
                    <div class="filter-left__mobile--choose">
                        {{ titleLeagueMobile }}
                    </div>
                </div>
                <div role="button" class="filter-left__mobile--search" @click="handleSearchMobile">
                    <i v-if="!showSearchMobile" class="icon-search"></i>
                    <i v-if="showSearchMobile" class="icon-close-mb"></i>
                </div>
            </div>
            <FootballSchedulesSidebarLeftNav
                ref="sidebarLeftNavRef"
                :class="{ active: showLeagueMobile }"
                :listLeagues="dataLeagues"
                @closeItem="setTitleLeagueMobile"
            />
        </div>
    </div>
</template>
<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { getNewUrl } from '~/utils/url'

const { t } = useI18n()
const { showMobile } = useCommon()
const useFootballSchedulesInstance = useFootballSchedules()

const { listLeagues, selectSeasonMB } = storeToRefs(useFootballSchedulesInstance)
const { getLeagues, handleFilterLeague, setSelectSeasonMB } = useFootballSchedulesInstance

const route = useRoute()
const router = useRouter()
const searchInput = ref('')
const searchInputRef = ref()
const searchContainerRef = ref(null)
const isShowDropdown = ref(false)
const currentSeason = ref('')
const showLeagueMobile = ref(false)
const showSearchMobile = ref(false)
const sidebarLeftNavRef = ref()

const titleLeagueMobile = computed(() => {
    if (selectSeasonMB.value === 'all') {
        return t('football_schedules.select_season')
    } else {
        return dataLeagues.value?.find(league => league.id === selectSeasonMB.value)?.name || ''
    }
})

const resetSelectMobile = () => {
    searchInput.value = ''

    const url = getNewUrl(route.fullPath, { season: selectSeasonMB.value })
    router.push(url)
}

const setTitleLeagueMobile = (name: string) => {
    titleLeagueMobile.value = name
    handleLeagueChoose()
}
const showDropdown = () => {
    isShowDropdown.value = true
}

const handleLeagueChoose = () => {
    showLeagueMobile.value = !showLeagueMobile.value
}
const handleSearchMobile = () => {
    showSearchMobile.value = !showSearchMobile.value
}
const resetFilters = () => {
    searchInput.value = ''
}

onClickOutside(searchInputRef, () => {
    isShowDropdown.value = false
})

onClickOutside(sidebarLeftNavRef, () => {
    showLeagueMobile.value = false
})

const { data: dataLeagues } = await useAsyncData('leagues', () => getLeagues())
listLeagues.value = dataLeagues?.value?.data || []

onMounted(async () => {
    await nextTick()
    setSelectSeasonMB(route?.query?.season || 'all')
})

const handleFilterLeagueNew = (id, name) => {
    handleFilterLeague(id)
    currentSeason.value = id
    searchInput.value = name
    isShowDropdown.value = false

    // isShowFilter.value = false
    showLeagueMobile.value = false
    titleLeagueMobile.value = name
}

const filteredLeagues = computed(() => {
    if (!searchInput.value) {
        return dataLeagues.value
    }

    return dataLeagues.value.filter(league =>
        league.name.toLowerCase().includes(searchInput.value.toLowerCase())
    )
})

watch(
    () => [route.query.tab, route.query.season],
    ([newTab, newSeason], [oldTab]) => {
        setSelectSeasonMB(route?.query?.season || 'all')
        if (newTab !== oldTab || newSeason !== currentSeason.value) {
            resetFilters()
        }
        if (newTab !== oldTab) {
            titleLeagueMobile.value = t('football_schedules.featured_tournaments')
        }
    }
)
watch(showSearchMobile, newVal => {
    if (newVal && searchContainerRef.value) {
        const inputElement = searchContainerRef.value.querySelector('input')
        if (inputElement) {
            inputElement.focus()
        }
    }
})
</script>
<style lang="scss" scoped>
:deep(.suffix-icon) {
    @apply cursor-pointer;
}

@media (max-width: 1199px) {
    .list-leagues {
        @apply absolute -left-3 -right-3 top-[52px] z-[1] hidden w-screen bg-white px-3;
        box-shadow: 0px 2px 4px 0px #2525251f;
        &.active {
            @apply block;
        }
    }

    .search-filter {
        @apply pointer-events-none absolute right-[48px] top-[0] z-20 w-[0] opacity-0 [transition:0.3s];
        &.active {
            @apply pointer-events-auto w-[calc(100%_-_48px)] opacity-100;
        }
        &:deep(.form-input) {
            @apply h-10;
        }
    }
}

.filter-left__mobile {
    @apply relative;
    &--select {
        @apply border-solid max-xl:border max-xl:border-[#EBEBEB] xl:[box-shadow:0px_12px_24px_0px_#00000040];
        @apply relative flex h-10 w-[calc(100%_-_48px)] items-center rounded bg-[#ffffff] py-3 pl-[14px] pr-8 text-sm font-normal leading-[20px] text-grey-900 xl:border-none;
        &:before {
            @apply absolute right-[12px] top-2/4 size-6 -translate-y-1/2 transform rounded bg-[#F1F3F9] text-gray-700 content-[''];
        }

        &:after {
            transform: translateY(-50%) rotate(180deg);
            @apply absolute right-[18px] top-1/2 font-[icomoon] text-xs font-normal text-gray-700 transition-all content-['\e96c'];
        }
        &.active {
            &:after {
                transform: translateY(-50%) rotate(0deg);
            }
        }
    }
    &--choose {
        @apply line-clamp-1 w-full;
    }

    &--search {
        @apply absolute right-0 top-0 grid size-[40px] place-content-center rounded bg-[#F1F3F9] text-[20px] text-gray-700;
        img {
            @apply absolute left-2/4 top-2/4 -translate-x-1/2 -translate-y-1/2 transform;
        }
    }
}
</style>
