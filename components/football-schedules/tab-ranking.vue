<template>
    <div class="tab-schedule flex h-full flex-col">
        <div v-if="showMobile" class="mb-4 px-3 py-2 lg:hidden">
            <FootballSchedulesSidebarLeft class="mt-0 rounded-lg p-0" />
        </div>
        <h1
            class="mb-4 text-[18px] font-medium capitalize leading-[26px] text-white max-lg:px-3 lg:text-2xl"
        >
            {{ $t(`pages.${route.params.tab}`) }}
        </h1>
        <div
            class="min-h-[290px] grow grid-cols-1 place-content-center"
            :class="ranking?.length && Object.keys(ranking)?.length ? 'flex' : 'grid'"
        >
            <div v-if="isLoading" class="w-full">
                <CommonSpinner />
            </div>
            <template v-else>
                <div v-if="ranking?.length && Object.keys(ranking)?.length" class="w-full">
                    <div v-for="(league, idx) in ranking" :key="idx">
                        <FootballSchedulesRankingItem :league="league" :is-active="true" />
                    </div>
                </div>
                <FootballSchedulesEmptyData v-if="!ranking?.length" />
            </template>
        </div>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'

const { showMobile } = useCommon()

const route = useRoute()
const useFootballSchedulesInstance = useFootballSchedules()

const { ranking, isLoading } = storeToRefs(useFootballSchedulesInstance)
const { getStandings } = useFootballSchedulesInstance

const seasonId = ref(route?.query?.season || 'all')

watch(
    () => route.query.season,
    newSeasonId => {
        getStandings(newSeasonId)
    }
)

await useAsyncData('standings', async () => {
    await getStandings(seasonId.value)
    return ranking.value
})
</script>
