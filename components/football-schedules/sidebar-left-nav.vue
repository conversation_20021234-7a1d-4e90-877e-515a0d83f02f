<template>
    <div class="list-leagues">
        <div
            class="list-leagues__title mb-2 text-base font-semibold leading-normal text-white max-lg:hidden"
        >
            {{ $t('football_schedules.featured_tournaments') }}
        </div>
        <div
            class="common-scrollbar max-h-[200px] overflow-auto max-xl:rounded-b lg:max-h-[calc(100vh-250px)]"
        >
            <CommonSpinner v-if="isLoadingGetLeagues" />
            <template v-else>
                <div
                    v-for="(league, index) in listLeaguesNew"
                    :key="index"
                    role="button"
                    @click="handleFilterLeagueNew(league.id, league.name)"
                    class="list__item flex cursor-pointer items-center gap-0.5 rounded px-1 py-[10px] text-white"
                    :class="[league.id === currentSeason ? 'is-active' : '', 'item-' + league.id]"
                >
                    <img
                        :src="league.logo"
                        :alt="league.name"
                        class="size-5 shrink-0 object-contain p-[2px]"
                    />
                    <span class="text-xs">{{ league.name }}</span>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia'

const props = defineProps({
    listLeagues: {
        type: Array,
        default: () => [],
    },
})
const { t } = useI18n()
const emit = defineEmits(['closeItem'])

const route = useRoute()
const useFootballSchedulesInstance = useFootballSchedules()
const { isLoadingGetLeagues } = storeToRefs(useFootballSchedulesInstance)
const { handleFilterLeague, setSelectSeasonMB } = useFootballSchedulesInstance

const handleFilterLeagueNew = (id: string, name: string) => {
    handleFilterLeague(id)
    emit('closeItem', name)
    setSelectSeasonMB(id)
}

const listLeaguesNew = computed(() => {
    const leagues = (props.listLeagues ?? [])?.slice(0, 20)
    if (route?.params?.tab !== 'bang-xep-hang') {
        const allLeagues = {
            id: 'all',
            name: t('football_schedules.filter_all'),
            logo: '/assets/images/pages/football-schedules/logo-league.svg',
        }
        leagues.unshift(allLeagues)
    }
    return leagues
})

const currentSeason = computed(() => route?.query?.season || listLeaguesNew.value[0]?.id)
</script>
<style lang="scss" scoped>
.list__item {
    @apply xl:hover:bg-slate-700;
    &.is-active {
        @apply bg-slate-700 font-medium text-green-400 hover:bg-slate-700;
    }
}
.item-all img {
    @apply bg-[transparent] p-0;
}
</style>
