<template>
    <div class="schedule-item" v-if="schedule" :class="{ 'is-active': isShowSchedule }">
        <NuxtLink
            role="button"
            class="schedule-item__league flex cursor-pointer items-center gap-2 bg-slate-700 px-3 py-2"
            @click="toggleCollapse"
        >
            <img
                :src="schedule.season_info.logo"
                :alt="schedule.season_info.name"
                class="size-5 object-contain"
            />
            <span class="text-xs font-medium text-white">{{ schedule.season_info.name }}</span>
        </NuxtLink>
        <div class="schedule-item__content" v-if="isShowSchedule">
            <div
                v-for="(item, index) in schedule.events"
                :key="index"
                class="match-item pb-4"
                :class="{ 'py-4': match }"
            >
                <div
                    v-if="odds"
                    class="match-item__thead match-item-odd flex items-center bg-slate-800 py-2 text-xs font-normal text-white"
                >
                    <div class="match-item__thead--time text-xs font-semibold text-white">
                        {{ formatDateUTCOdds(item.sport_event.start_time) }}
                    </div>
                    <div class="match-item__thead--title">
                        <div>{{ $t('football_schedules.handicap') }}</div>
                        <div>{{ $t('football_schedules.over_under') }}</div>
                        <div>{{ $t('football_schedules.1x2') }}</div>
                        <div class="max-md:!hidden">{{ $t('football_schedules.handicap') }} HT</div>
                        <div class="max-md:!hidden">
                            {{ $t('football_schedules.over_under') }} HT
                        </div>
                        <div class="max-md:!hidden">{{ $t('football_schedules.1x2') }} HT</div>
                    </div>
                </div>
                <div
                    class="match-item__battle flex justify-between xl:justify-start"
                    :class="odds ? 'odds flex-row px-3 pt-2' : 'flex-row-reverse xl:flex-row'"
                >
                    <div
                        v-if="match"
                        class="match-item__date ml-3 mr-3 flex w-auto shrink-0 flex-col items-end justify-center font-semibold text-white xl:mr-2 xl:w-[60px] xl:items-start"
                    >
                        <p class="text-xs">
                            {{ formatDateUTC(item.sport_event.start_time) }}
                        </p>
                        <span class="text-sm">{{
                            formatTimeUTC(item.sport_event.start_time)
                        }}</span>
                    </div>
                    <div class="match-item__teams" :class="odds ? '' : 'pl-3 xl:pl-0'">
                        <div class="match-item__teams--box mb-2 flex items-center gap-2">
                            <div class="match-item__teams--flag">
                                <img
                                    :src="item.sport_event.competitors[0].logo"
                                    :alt="item.sport_event.competitors[0].name"
                                    class="size-5 object-contain"
                                />
                            </div>
                            <div class="match-item__teams--name">
                                <div class="team__name">
                                    {{ item.sport_event.competitors[0].name }}
                                </div>
                            </div>
                        </div>
                        <div class="match-item__teams--box flex items-center gap-2">
                            <div class="match-item__teams--flag">
                                <img
                                    :src="item.sport_event.competitors[1].logo"
                                    :alt="item.sport_event.competitors[1].name"
                                    class="size-5 object-contain"
                                />
                            </div>
                            <div class="match-item__teams--name">
                                <div class="team__name">
                                    {{ item.sport_event.competitors[1].name }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="odds" class="match-item__odds">
                        <div class="match-item__odds--row">
                            <div class="match-item__odds--td">
                                <div class="match-item__odds--left min-w-[27px] sm:text-right">
                                    {{ handicapHome(item).rate }}
                                </div>
                                <div class="match-item__odds--right">
                                    {{ handicapHome(item).odds }}
                                </div>
                            </div>
                            <div class="match-item__odds--td">
                                <div class="match-item__odds--left">
                                    O {{ overRate(item).rate }}
                                </div>
                                <div class="match-item__odds--right">
                                    {{ overRate(item).odds }}
                                </div>
                            </div>
                            <div class="match-item__odds--td">
                                <div class="match-item__odds--left">H</div>
                                <div class="match-item__odds--right">
                                    <span :class="checkNumber(fullMatch(item).oh)">
                                        {{ fullMatch(item).oh }}
                                    </span>
                                </div>
                            </div>
                            <div class="match-item__odds--td max-md:!hidden">
                                <div class="match-item__odds--left min-w-[27px] sm:text-right">
                                    {{ handicapHomeHT(item).rate }}
                                </div>
                                <div class="match-item__odds--right">
                                    {{ handicapHomeHT(item).odds }}
                                </div>
                            </div>
                            <div class="match-item__odds--td max-md:!hidden">
                                <div class="match-item__odds--left">
                                    O {{ overRateHT(item).rate }}
                                </div>
                                <div class="match-item__odds--right">
                                    {{ overRateHT(item).odds }}
                                </div>
                            </div>
                            <div class="match-item__odds--td max-md:!hidden">
                                <div class="match-item__odds--left">H</div>
                                <div class="match-item__odds--right">
                                    <span :class="checkNumber(fullMatch(item).oh)">
                                        {{ fullMatchHT(item).oh }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="match-item__odds--row">
                            <div class="match-item__odds--td">
                                <div class="match-item__odds--left min-w-[27px] sm:text-right">
                                    {{ handicapAway(item).rate }}
                                </div>
                                <div
                                    class="match-item__odds--right"
                                    :class="checkNumber(handicapAway(item).odds)"
                                >
                                    {{ handicapAway(item).odds }}
                                </div>
                            </div>
                            <div class="match-item__odds--td">
                                <div class="match-item__odds--left">
                                    U {{ underRate(item).rate }}
                                </div>
                                <div
                                    class="match-item__odds--right"
                                    :class="checkNumber(underRate(item).odds)"
                                >
                                    {{ underRate(item).odds }}
                                </div>
                            </div>
                            <div class="match-item__odds--td">
                                <div class="match-item__odds--left">A</div>
                                <div class="match-item__odds--right">
                                    <span :class="checkNumber(fullMatch(item).oa)">
                                        {{ fullMatch(item).oa }}
                                    </span>
                                </div>
                            </div>
                            <div class="match-item__odds--td max-md:!hidden">
                                <div class="match-item__odds--left min-w-[27px] sm:text-right">
                                    {{ handicapAwayHT(item).rate }}
                                </div>
                                <div
                                    class="match-item__odds--right"
                                    :class="checkNumber(handicapAwayHT(item).odds)"
                                >
                                    {{ handicapAwayHT(item).odds }}
                                </div>
                            </div>
                            <div class="match-item__odds--td max-md:!hidden">
                                <div class="match-item__odds--left">
                                    U {{ underRateHT(item).rate }}
                                </div>
                                <div
                                    class="match-item__odds--right"
                                    :class="checkNumber(underRate(item).odds)"
                                >
                                    {{ underRateHT(item).odds }}
                                </div>
                            </div>
                            <div class="match-item__odds--td max-md:!hidden">
                                <div class="match-item__odds--left">A</div>
                                <div class="match-item__odds--right">
                                    <span :class="checkNumber(fullMatchHT(item).oa)">
                                        {{ fullMatchHT(item).oa }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="match-item__odds--row">
                            <div class="match-item__odds--td !bg-transparent max-md:!hidden"></div>
                            <div class="match-item__odds--td !bg-transparent max-md:!hidden"></div>
                            <NuxtLink
                                role="button"
                                class="match-item__odds--btn cursor-pointer bg-rose-600 text-xs font-semibold capitalize text-white md:!hidden"
                                @click="toggleShowModalOdds(item)"
                            >
                                {{ $t('football_schedules.see_more') }}
                            </NuxtLink>
                            <div class="match-item__odds--td">
                                <div class="match-item__odds--left">D</div>
                                <div class="match-item__odds--right">
                                    <span :class="checkNumber(fullMatch(item).od)">
                                        {{ fullMatch(item).od }}
                                    </span>
                                </div>
                            </div>
                            <div class="match-item__odds--td !bg-transparent max-md:!hidden"></div>
                            <NuxtLink
                                role="button"
                                class="match-item__odds--btn cursor-pointer whitespace-nowrap bg-rose-600 text-xs font-semibold capitalize text-white max-md:!hidden"
                                @click="betNowHotmatch(item)"
                            >
                                {{ $t('football_schedules.bet_now') }}
                            </NuxtLink>
                            <div class="match-item__odds--td max-md:!hidden">
                                <div class="match-item__odds--left">D</div>
                                <div class="match-item__odds--right">
                                    <span :class="checkNumber(fullMatchHT(item).od)">
                                        {{ fullMatchHT(item).od }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <FootballSchedulesModalOdds
        v-if="isShowModalOdds && odds"
        :data-odds="dataOdds"
        :schedule="schedule"
        :show="isShowModalOdds"
        @close="toggleShowModalOdds"
        @submit="betNowHotmatch(dataOdds)"
    >
    </FootballSchedulesModalOdds>
</template>
<script setup lang="ts">
import {
    handicapHome,
    handicapAway,
    overRate,
    underRate,
    fullMatch,
    handicapHomeHT,
    handicapAwayHT,
    overRateHT,
    underRateHT,
    fullMatchHT,
    checkNumber,
    formatDateUTC,
    formatTimeUTC,
    formatDateUTCOdds,
} from '~/utils/football-schedule-helper'

const props = defineProps({
    schedule: {
        type: Object,
        default: () => {},
    },
    odds: {
        type: Boolean,
        required: false,
    },
    match: {
        type: Boolean,
        required: false,
    },
    result: {
        type: Boolean,
        required: false,
    },
    isActive: {
        type: Boolean,
        required: false,
    },
})

const isShowSchedule = ref(props.isActive)
const isShowModalOdds = ref(false)
const dataOdds = ref(null)

const { loadSportLink } = useHotMatch()

const toggleCollapse = () => {
    isShowSchedule.value = !isShowSchedule.value
}

const betNowHotmatch = item => {
    loadSportLink({
        ...item?.sport_event,
        league_id: item?.odds?.li ?? item?.leagueId,
        match_id: item?.odds?.ei ?? item?.matchId,
        event_id: item?.odds?.ei ?? item?.matchId,
    })
}

const toggleShowModalOdds = data => {
    dataOdds.value = data
    isShowModalOdds.value = !isShowModalOdds.value
}
</script>
<style scoped lang="scss">
.schedule-item {
    &.is-active {
        .schedule-item__league {
            &::before {
                @apply rotate-0 transform;
            }
        }
    }
    &__league {
        @apply relative border-b border-solid border-slate-700;
        &::before {
            @apply absolute right-3 top-1/2 grid size-6 -translate-y-1/2 rotate-180 transform place-content-center rounded bg-[#FFFFFF1A] font-['icomoon'] text-[12px] font-normal text-white transition-all content-['\e96c'] xl:right-2;
        }
    }
}
.match-item {
    @apply relative border-b border-solid border-slate-750;
    &__thead--time {
        @apply pl-3;
    }
    &__thead--title {
        @apply ml-auto flex items-center justify-end gap-1 pr-3 text-center text-[10px] font-normal leading-[15px] text-white xl:gap-1 xl:text-xs xl:leading-4 2xl:gap-2;
        > div {
            @apply w-[45px] xl:w-[74px] 2xl:w-[87px];
        }
    }

    &__teams {
        @apply min-w-[140px];
    }

    &__odds {
        @apply ml-3 min-w-[143px] shrink-0 border-l border-solid border-slate-750 pl-[13.5px] text-xs xl:ml-auto xl:pl-3 2xl:pl-8;
        &--row {
            @apply flex w-full items-center justify-end gap-x-1 gap-y-2 text-center xl:gap-1 2xl:gap-2;
            &:not(:last-child) {
                @apply mb-1 xl:mb-1 2xl:mb-2;
            }
        }
        &--td {
            @apply flex h-[36px] w-[45px] flex-col items-center justify-between text-nowrap rounded bg-[#FFFFFF0D] px-1 py-[2px] text-xs xl:h-[24px] xl:w-[74px] xl:flex-row xl:px-1 2xl:w-[87px] 2xl:px-2;
        }
        &--left {
            @apply text-white;
        }
        &--right {
            @apply font-semibold text-white;
        }
    }
    &__teams--flag {
        @apply shrink-0;
    }
    .team__name {
        @apply line-clamp-1 break-all text-sm font-normal text-white;
    }
    &__odds--btn {
        @apply flex h-9 w-[94px] items-center justify-center rounded py-1 text-xs font-semibold xl:h-6 xl:w-[74px] xl:px-[4px] 2xl:w-[87px] 2xl:px-[6px];
    }
}
</style>
