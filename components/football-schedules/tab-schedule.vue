<template>
    <div class="tab-schedule flex h-full flex-col">
        <div v-if="showMobile" class="mb-4 px-3 py-2 lg:hidden">
            <FootballSchedulesSidebarLeft class="mt-0 rounded-lg p-0" />
        </div>
        <h1
            class="mb-4 text-[18px] font-medium capitalize leading-[26px] text-white max-lg:px-3 lg:text-2xl"
        >
            {{ $t(`pages.${route.params.tab}`) }}
        </h1>
        <FootballSchedulesFilterDays class="pb-4 xl:pb-6" :filterDays />
        <div
            class="min-h-[290px] grow grid-cols-1 place-content-center"
            :class="dataSchedules?.length && Object.keys(dataSchedules)?.length ? 'flex' : 'grid'"
        >
            <div v-if="isLoading" class="w-full">
                <CommonSpinner />
            </div>
            <template v-else>
                <div
                    v-if="dataSchedules?.length && Object.keys(dataSchedules)?.length"
                    class="w-full"
                >
                    <FootballSchedulesMatchItem
                        v-for="(item, index) in dataSchedules"
                        :key="index"
                        :schedule="item"
                        match
                        :is-active="true"
                    />
                </div>
                <FootballSchedulesEmptyData v-else />
            </template>
        </div>
    </div>
</template>
<script setup>
import { filterDays } from '~/resources/football-schedules'

const { showMobile } = useCommon()
const route = useRoute()
const useFootballSchedulesInstance = useFootballSchedules()
const { dataSchedules, isLoading } = storeToRefs(useFootballSchedulesInstance)
</script>
