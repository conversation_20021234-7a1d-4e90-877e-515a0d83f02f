<template>
    <div class="tab-schedule flex h-full flex-col">
        <div v-if="showMobile" class="mb-4 px-3 py-2 lg:hidden">
            <FootballSchedulesSidebarLeft class="mt-0 rounded-lg p-0" />
        </div>
        <h1
            class="mb-4 text-[18px] font-medium capitalize leading-[26px] text-white max-lg:px-3 lg:text-2xl"
        >
            {{ $t(`pages.${route.params.tab}`) }}
        </h1>
        <FootballSchedulesFilterDays :filterDays="modifiedFilterDays" class="pb-4 xl:pb-6" />
        <div
            class="min-h-[290px] grow grid-cols-1"
            :class="dataSchedules?.length && Object.keys(dataSchedules)?.length ? 'flex' : 'grid'"
        >
            <div v-if="isLoading" class="w-full">
                <CommonSpinner />
            </div>
            <template v-else>
                <div
                    v-if="dataSchedules?.length && Object.keys(dataSchedules)?.length"
                    class="w-full"
                >
                    <FootballSchedulesResultItem
                        v-for="(item, index) in dataSchedules"
                        :key="index"
                        :schedule="item"
                        result
                        :is-active="true"
                    />
                </div>
                <FootballSchedulesEmptyData v-else />
            </template>
        </div>
    </div>
</template>
<script setup>
import { storeToRefs } from 'pinia'
import { filterDays } from '~/resources/football-schedules'

const { showMobile } = useCommon()
const route = useRoute()
const useFootballSchedulesInstance = useFootballSchedules()
const { dataSchedules, isLoading } = storeToRefs(useFootballSchedulesInstance)

const { t } = useI18n()

const modifiedFilterDays = computed(() => {
    const newFilterDays = [...filterDays]
    const yesterdayDate = new Date()
    yesterdayDate.setDate(new Date().getDate() - 1)
    newFilterDays[2] = {
        key: formatDate(yesterdayDate),
        name: t('football_schedules.filter_yesterday'),
    }
    return newFilterDays
})

const formatDate = date => {
    return date.toISOString().split('T')[0]
}
</script>
<style lang="scss" scoped></style>
